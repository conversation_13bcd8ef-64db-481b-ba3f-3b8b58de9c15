"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/Contact.tsx":
/*!********************************!*\
  !*** ./components/Contact.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Contact; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-intersection-observer */ \"(app-pages-browser)/./node_modules/react-intersection-observer/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_MapPin_Phone_Send_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,MapPin,Phone,Send,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_MapPin_Phone_Send_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,MapPin,Phone,Send,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_MapPin_Phone_Send_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,MapPin,Phone,Send,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_MapPin_Phone_Send_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,MapPin,Phone,Send,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_MapPin_Phone_Send_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,MapPin,Phone,Send,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_MapPin_Phone_Send_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,MapPin,Phone,Send,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_MapPin_Phone_Send_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,MapPin,Phone,Send,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst socialLinks = [\n    {\n        name: \"GitHub\",\n        icon: _barrel_optimize_names_Github_Linkedin_Mail_MapPin_Phone_Send_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        url: \"https://github.com/yourusername\",\n        color: \"text-gray-400 hover:text-white\"\n    },\n    {\n        name: \"LinkedIn\",\n        icon: _barrel_optimize_names_Github_Linkedin_Mail_MapPin_Phone_Send_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        url: \"https://linkedin.com/in/yourusername\",\n        color: \"text-blue-400 hover:text-blue-300\"\n    },\n    {\n        name: \"Twitter\",\n        icon: _barrel_optimize_names_Github_Linkedin_Mail_MapPin_Phone_Send_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        url: \"https://twitter.com/yourusername\",\n        color: \"text-blue-400 hover:text-blue-300\"\n    }\n];\nconst contactInfo = [\n    {\n        icon: _barrel_optimize_names_Github_Linkedin_Mail_MapPin_Phone_Send_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        label: \"Email\",\n        value: \"<EMAIL>\",\n        href: \"mailto:<EMAIL>\"\n    },\n    {\n        icon: _barrel_optimize_names_Github_Linkedin_Mail_MapPin_Phone_Send_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        label: \"Phone\",\n        value: \"+****************\",\n        href: \"tel:+15551234567\"\n    },\n    {\n        icon: _barrel_optimize_names_Github_Linkedin_Mail_MapPin_Phone_Send_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        label: \"Location\",\n        value: \"San Francisco, CA\",\n        href: \"#\"\n    }\n];\nfunction Contact() {\n    _s();\n    const [ref, inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_9__.useInView)({\n        triggerOnce: true,\n        threshold: 0.1\n    });\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        email: \"\",\n        subject: \"\",\n        message: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.name.trim()) {\n            newErrors.name = \"Name is required\";\n        }\n        if (!formData.email.trim()) {\n            newErrors.email = \"Email is required\";\n        } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\n            newErrors.email = \"Please enter a valid email address\";\n        }\n        if (!formData.subject.trim()) {\n            newErrors.subject = \"Subject is required\";\n        }\n        if (!formData.message.trim()) {\n            newErrors.message = \"Message is required\";\n        } else if (formData.message.trim().length < 10) {\n            newErrors.message = \"Message must be at least 10 characters long\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Please fix the errors in the form\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // Simulate API call - replace with actual email service\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // Here you would integrate with EmailJS, Formspree, or your backend\n            console.log(\"Form submitted:\", formData);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(\"Message sent successfully! I'll get back to you soon.\");\n            setFormData({\n                name: \"\",\n                email: \"\",\n                subject: \"\",\n                message: \"\"\n            });\n            setErrors({});\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Failed to send message. Please try again.\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: undefined\n                }));\n        }\n    };\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        ref: ref,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                variants: containerVariants,\n                initial: \"hidden\",\n                animate: inView ? \"visible\" : \"hidden\",\n                className: \"text-center mb-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h2, {\n                        variants: itemVariants,\n                        className: \"text-4xl md:text-6xl font-cyber font-bold text-neon-blue neon-glow-subtle mb-6\",\n                        children: \"Get In Touch\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        variants: itemVariants,\n                        className: \"w-24 h-1 bg-gradient-to-r from-neon-blue to-neon-green mx-auto mb-8\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                        variants: itemVariants,\n                        className: \"text-xl text-gray-400 max-w-3xl mx-auto\",\n                        children: \"Ready to bring your ideas to life? Let's collaborate and create something amazing together.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-2 gap-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        animate: inView ? \"visible\" : \"hidden\",\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                variants: itemVariants,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-tech font-semibold text-white mb-8\",\n                                        children: \"Let's Connect\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: contactInfo.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.a, {\n                                                href: info.href,\n                                                variants: itemVariants,\n                                                className: \"flex items-center p-4 bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg hover:border-neon-blue/50 transition-all duration-300 group\",\n                                                whileHover: {\n                                                    scale: 1.02,\n                                                    x: 10\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-neon-blue/20 rounded-lg mr-4 group-hover:bg-neon-blue/30 transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(info.icon, {\n                                                            className: \"text-neon-blue\",\n                                                            size: 24\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-sm\",\n                                                                children: info.label\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white font-tech\",\n                                                                children: info.value\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                variants: itemVariants,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-xl font-tech font-semibold text-white mb-6\",\n                                        children: \"Follow Me\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-4\",\n                                        children: socialLinks.map((social, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.a, {\n                                                href: social.url,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"p-3 bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg \".concat(social.color, \" transition-all duration-300\"),\n                                                whileHover: {\n                                                    scale: 1.1,\n                                                    y: -5\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(social.icon, {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                variants: itemVariants,\n                                className: \"bg-gray-900/30 backdrop-blur-sm border border-gray-700 rounded-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-tech font-semibold text-white mb-3\",\n                                        children: \"Response Time\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"I typically respond to messages within 24 hours. For urgent inquiries, feel free to reach out via phone or LinkedIn.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        animate: inView ? \"visible\" : \"hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.form, {\n                            variants: itemVariants,\n                            onSubmit: handleSubmit,\n                            className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg p-8 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-tech font-semibold text-white mb-6\",\n                                    children: \"Send Message\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"name\",\n                                            className: \"block text-sm font-tech font-medium text-gray-300 mb-2\",\n                                            children: \"Name *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"name\",\n                                            name: \"name\",\n                                            value: formData.name,\n                                            onChange: handleChange,\n                                            className: \"w-full px-4 py-3 bg-gray-800/50 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all duration-300 \".concat(errors.name ? \"border-red-500 focus:ring-red-500\" : \"border-gray-600 focus:border-neon-blue focus:ring-neon-blue\"),\n                                            placeholder: \"Your full name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: -10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            className: \"text-red-400 text-sm mt-1\",\n                                            children: errors.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-tech font-medium text-gray-300 mb-2\",\n                                            children: \"Email *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            id: \"email\",\n                                            name: \"email\",\n                                            value: formData.email,\n                                            onChange: handleChange,\n                                            className: \"w-full px-4 py-3 bg-gray-800/50 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all duration-300 \".concat(errors.email ? \"border-red-500 focus:ring-red-500\" : \"border-gray-600 focus:border-neon-blue focus:ring-neon-blue\"),\n                                            placeholder: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: -10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            className: \"text-red-400 text-sm mt-1\",\n                                            children: errors.email\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"subject\",\n                                            className: \"block text-sm font-tech font-medium text-gray-300 mb-2\",\n                                            children: \"Subject *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"subject\",\n                                            name: \"subject\",\n                                            value: formData.subject,\n                                            onChange: handleChange,\n                                            className: \"w-full px-4 py-3 bg-gray-800/50 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all duration-300 \".concat(errors.subject ? \"border-red-500 focus:ring-red-500\" : \"border-gray-600 focus:border-neon-blue focus:ring-neon-blue\"),\n                                            placeholder: \"What's this about?\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.subject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: -10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            className: \"text-red-400 text-sm mt-1\",\n                                            children: errors.subject\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"message\",\n                                            className: \"block text-sm font-tech font-medium text-gray-300 mb-2\",\n                                            children: \"Message *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            id: \"message\",\n                                            name: \"message\",\n                                            rows: 6,\n                                            value: formData.message,\n                                            onChange: handleChange,\n                                            className: \"w-full px-4 py-3 bg-gray-800/50 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all duration-300 resize-none \".concat(errors.message ? \"border-red-500 focus:ring-red-500\" : \"border-gray-600 focus:border-neon-blue focus:ring-neon-blue\"),\n                                            placeholder: \"Tell me about your project or inquiry...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: -10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            className: \"text-red-400 text-sm mt-1\",\n                                            children: errors.message\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                                    type: \"submit\",\n                                    disabled: isSubmitting,\n                                    className: \"w-full btn-cyber flex items-center justify-center gap-3 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    whileHover: !isSubmitting ? {\n                                        scale: 1.02\n                                    } : {},\n                                    whileTap: !isSubmitting ? {\n                                        scale: 0.98\n                                    } : {},\n                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-5 h-5 border-2 border-black border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Sending...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Mail_MapPin_Phone_Send_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Send Message\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Contact.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(Contact, \"sTLtlJ5VBPIcmddvfv0L9lakJ5g=\", false, function() {\n    return [\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_9__.useInView\n    ];\n});\n_c = Contact;\nvar _c;\n$RefreshReg$(_c, \"Contact\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Contact.tsx\n"));

/***/ })

});