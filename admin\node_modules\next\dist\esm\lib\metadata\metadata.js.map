{"version": 3, "sources": ["../../../src/lib/metadata/metadata.tsx"], "names": ["React", "AppleWebAppMeta", "FormatDetectionMeta", "ItunesMeta", "BasicMeta", "ViewportMeta", "VerificationMeta", "AlternatesMetadata", "OpenGraphMetadata", "TwitterMetadata", "AppLinksMeta", "IconsMetadata", "resolveMetadata", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createDefaultMetadata", "createDefaultViewport", "isNotFoundError", "createMetadataComponents", "tree", "pathname", "trailingSlash", "query", "getDynamicParamFromSegment", "appUsingSizeAdjustment", "errorType", "createDynamicallyTrackedSearchParams", "metadataContext", "split", "resolve", "metadataErrorResolving", "Promise", "res", "MetadataTree", "defaultMetadata", "defaultViewport", "metadata", "viewport", "error", "errorMetadataItem", "errorConvention", "undefined", "searchParams", "resolvedError", "resolvedMetadata", "resolvedViewport", "parentParams", "metadataItems", "notFoundMetadataError", "notFoundMetadata", "notFoundViewport", "elements", "alternates", "itunes", "formatDetection", "verification", "appleWebApp", "openGraph", "twitter", "appLinks", "icons", "push", "meta", "name", "map", "el", "index", "cloneElement", "key", "MetadataOutlet"], "mappings": ";AAIA,OAAOA,WAAW,QAAO;AACzB,SACEC,eAAe,EACfC,mBAAmB,EACnBC,UAAU,EACVC,SAAS,EACTC,YAAY,EACZC,gBAAgB,QACX,mBAAkB;AACzB,SAASC,kBAAkB,QAAQ,uBAAsB;AACzD,SACEC,iBAAiB,EACjBC,eAAe,EACfC,YAAY,QACP,uBAAsB;AAC7B,SAASC,aAAa,QAAQ,mBAAkB;AAChD,SAASC,eAAe,QAAQ,qBAAoB;AACpD,SAASC,UAAU,QAAQ,kBAAiB;AAK5C,SACEC,qBAAqB,EACrBC,qBAAqB,QAChB,qBAAoB;AAC3B,SAASC,eAAe,QAAQ,oCAAmC;AAEnE,+DAA+D;AAC/D,+DAA+D;AAC/D,sGAAsG;AACtG,0GAA0G;AAC1G,uEAAuE;AACvE,4EAA4E;AAC5E,OAAO,SAASC,yBAAyB,EACvCC,IAAI,EACJC,QAAQ,EACRC,aAAa,EACbC,KAAK,EACLC,0BAA0B,EAC1BC,sBAAsB,EACtBC,SAAS,EACTC,oCAAoC,EAYrC;IACC,MAAMC,kBAAkB;QACtB,8CAA8C;QAC9CP,UAAUA,SAASQ,KAAK,CAAC,IAAI,CAAC,EAAE;QAChCP;IACF;IAEA,IAAIQ;IACJ,8DAA8D;IAC9D,MAAMC,yBAAyB,IAAIC,QAA2B,CAACC;QAC7DH,UAAUG;IACZ;IAEA,eAAeC;QACb,MAAMC,kBAAkBnB;QACxB,MAAMoB,kBAAkBnB;QACxB,IAAIoB,WAAyCF;QAC7C,IAAIG,WAAyCF;QAC7C,IAAIG;QACJ,MAAMC,oBAAwC;YAAC;YAAM;YAAM;SAAK;QAChE,MAAMC,kBAAkBf,cAAc,aAAagB,YAAYhB;QAC/D,MAAMiB,eAAehB,qCAAqCJ;QAE1D,MAAM,CAACqB,eAAeC,kBAAkBC,iBAAiB,GACvD,MAAMhC,gBAAgB;YACpBM;YACA2B,cAAc,CAAC;YACfC,eAAe,EAAE;YACjBR;YACAG;YACAnB;YACAiB;YACAb;QACF;QACF,IAAI,CAACgB,eAAe;YAClBN,WAAWQ;YACXT,WAAWQ;YACXf,QAAQY;QACV,OAAO;YACLH,QAAQK;YACR,0FAA0F;YAC1F,kGAAkG;YAClG,kDAAkD;YAClD,IAAI,CAAClB,aAAaR,gBAAgB0B,gBAAgB;gBAChD,MAAM,CAACK,uBAAuBC,kBAAkBC,iBAAiB,GAC/D,MAAMrC,gBAAgB;oBACpBM;oBACA2B,cAAc,CAAC;oBACfC,eAAe,EAAE;oBACjBR;oBACAG;oBACAnB;oBACAiB,iBAAiB;oBACjBb;gBACF;gBACFU,WAAWa;gBACXd,WAAWa;gBACXX,QAAQU,yBAAyBV;YACnC;YACAT,QAAQS;QACV;QAEA,MAAMa,WAAWrC,WAAW;YAC1BR,aAAa;gBAAE+B,UAAUA;YAAS;YAClChC,UAAU;gBAAE+B;YAAS;YACrB5B,mBAAmB;gBAAE4C,YAAYhB,SAASgB,UAAU;YAAC;YACrDhD,WAAW;gBAAEiD,QAAQjB,SAASiB,MAAM;YAAC;YACrClD,oBAAoB;gBAAEmD,iBAAiBlB,SAASkB,eAAe;YAAC;YAChE/C,iBAAiB;gBAAEgD,cAAcnB,SAASmB,YAAY;YAAC;YACvDrD,gBAAgB;gBAAEsD,aAAapB,SAASoB,WAAW;YAAC;YACpD/C,kBAAkB;gBAAEgD,WAAWrB,SAASqB,SAAS;YAAC;YAClD/C,gBAAgB;gBAAEgD,SAAStB,SAASsB,OAAO;YAAC;YAC5C/C,aAAa;gBAAEgD,UAAUvB,SAASuB,QAAQ;YAAC;YAC3C/C,cAAc;gBAAEgD,OAAOxB,SAASwB,KAAK;YAAC;SACvC;QAED,IAAIpC,wBAAwB2B,SAASU,IAAI,eAAC,KAACC;YAAKC,MAAK;;QAErD,qBACE;sBACGZ,SAASa,GAAG,CAAC,CAACC,IAAIC;gBACjB,qBAAOjE,MAAMkE,YAAY,CAACF,IAA0B;oBAAEG,KAAKF;gBAAM;YACnE;;IAGN;IAEA,eAAeG;QACb,MAAM/B,QAAQ,MAAMR;QACpB,IAAIQ,OAAO;YACT,MAAMA;QACR;QACA,OAAO;IACT;IAEA,OAAO;QAACL;QAAcoC;KAAe;AACvC"}