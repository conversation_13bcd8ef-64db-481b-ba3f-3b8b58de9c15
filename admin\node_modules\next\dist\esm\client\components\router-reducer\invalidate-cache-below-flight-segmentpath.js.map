{"version": 3, "sources": ["../../../../src/client/components/router-reducer/invalidate-cache-below-flight-segmentpath.ts"], "names": ["createRouterCache<PERSON>ey", "invalidateCacheBelowFlightSegmentPath", "newCache", "existingCache", "flightSegmentPath", "isLastEntry", "length", "parallelRouteKey", "segment", "cache<PERSON>ey", "existingChildSegmentMap", "parallelRoutes", "get", "childSegmentMap", "Map", "set", "delete", "existingChildCacheNode", "childCacheNode", "lazyData", "rsc", "prefetchRsc", "head", "prefetchHead", "lazyDataResolved", "slice"], "mappings": "AAEA,SAASA,oBAAoB,QAAQ,4BAA2B;AAEhE;;CAEC,GACD,OAAO,SAASC,sCACdC,QAAmB,EACnBC,aAAwB,EACxBC,iBAAoC;IAEpC,MAAMC,cAAcD,kBAAkBE,MAAM,IAAI;IAChD,MAAM,CAACC,kBAAkBC,QAAQ,GAAGJ;IAEpC,MAAMK,WAAWT,qBAAqBQ;IAEtC,MAAME,0BACJP,cAAcQ,cAAc,CAACC,GAAG,CAACL;IAEnC,IAAI,CAACG,yBAAyB;QAC5B,6EAA6E;QAC7E,sEAAsE;QACtE;IACF;IAEA,IAAIG,kBAAkBX,SAASS,cAAc,CAACC,GAAG,CAACL;IAClD,IAAI,CAACM,mBAAmBA,oBAAoBH,yBAAyB;QACnEG,kBAAkB,IAAIC,IAAIJ;QAC1BR,SAASS,cAAc,CAACI,GAAG,CAACR,kBAAkBM;IAChD;IAEA,iDAAiD;IACjD,IAAIR,aAAa;QACfQ,gBAAgBG,MAAM,CAACP;QACvB;IACF;IAEA,MAAMQ,yBAAyBP,wBAAwBE,GAAG,CAACH;IAC3D,IAAIS,iBAAiBL,gBAAgBD,GAAG,CAACH;IAEzC,IAAI,CAACS,kBAAkB,CAACD,wBAAwB;QAC9C,6EAA6E;QAC7E,sEAAsE;QACtE;IACF;IAEA,IAAIC,mBAAmBD,wBAAwB;QAC7CC,iBAAiB;YACfC,UAAUD,eAAeC,QAAQ;YACjCC,KAAKF,eAAeE,GAAG;YACvBC,aAAaH,eAAeG,WAAW;YACvCC,MAAMJ,eAAeI,IAAI;YACzBC,cAAcL,eAAeK,YAAY;YACzCZ,gBAAgB,IAAIG,IAAII,eAAeP,cAAc;YACrDa,kBAAkBN,eAAeM,gBAAgB;QACnD;QACAX,gBAAgBE,GAAG,CAACN,UAAUS;IAChC;IAEAjB,sCACEiB,gBACAD,wBACAb,kBAAkBqB,KAAK,CAAC;AAE5B"}