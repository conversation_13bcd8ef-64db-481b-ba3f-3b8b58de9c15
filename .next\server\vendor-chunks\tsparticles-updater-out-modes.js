"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tsparticles-updater-out-modes";
exports.ids = ["vendor-chunks/tsparticles-updater-out-modes"];
exports.modules = {

/***/ "(ssr)/./node_modules/tsparticles-updater-out-modes/esm/BounceOutMode.js":
/*!*************************************************************************!*\
  !*** ./node_modules/tsparticles-updater-out-modes/esm/BounceOutMode.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BounceOutMode: () => (/* binding */ BounceOutMode)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/Utils.js\");\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/tsparticles-updater-out-modes/esm/Utils.js\");\n\n\nclass BounceOutMode {\n    constructor(container) {\n        this.container = container;\n        this.modes = [\n            \"bounce\",\n            \"bounce-vertical\",\n            \"bounce-horizontal\",\n            \"bounceVertical\",\n            \"bounceHorizontal\",\n            \"split\",\n        ];\n    }\n    update(particle, direction, delta, outMode) {\n        if (!this.modes.includes(outMode)) {\n            return;\n        }\n        const container = this.container;\n        let handled = false;\n        for (const [, plugin] of container.plugins) {\n            if (plugin.particleBounce !== undefined) {\n                handled = plugin.particleBounce(particle, delta, direction);\n            }\n            if (handled) {\n                break;\n            }\n        }\n        if (handled) {\n            return;\n        }\n        const pos = particle.getPosition(), offset = particle.offset, size = particle.getRadius(), bounds = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.calculateBounds)(pos, size), canvasSize = container.canvas.size;\n        (0,_Utils__WEBPACK_IMPORTED_MODULE_1__.bounceHorizontal)({ particle, outMode, direction, bounds, canvasSize, offset, size });\n        (0,_Utils__WEBPACK_IMPORTED_MODULE_1__.bounceVertical)({ particle, outMode, direction, bounds, canvasSize, offset, size });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-out-modes/esm/BounceOutMode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-updater-out-modes/esm/DestroyOutMode.js":
/*!**************************************************************************!*\
  !*** ./node_modules/tsparticles-updater-out-modes/esm/DestroyOutMode.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DestroyOutMode: () => (/* binding */ DestroyOutMode)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/Utils.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/Vector.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n\nclass DestroyOutMode {\n    constructor(container) {\n        this.container = container;\n        this.modes = [\"destroy\"];\n    }\n    update(particle, direction, _delta, outMode) {\n        if (!this.modes.includes(outMode)) {\n            return;\n        }\n        const container = this.container;\n        switch (particle.outType) {\n            case \"normal\":\n            case \"outside\":\n                if ((0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isPointInside)(particle.position, container.canvas.size, tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.Vector.origin, particle.getRadius(), direction)) {\n                    return;\n                }\n                break;\n            case \"inside\": {\n                const { dx, dy } = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.getDistances)(particle.position, particle.moveCenter);\n                const { x: vx, y: vy } = particle.velocity;\n                if ((vx < 0 && dx > particle.moveCenter.radius) ||\n                    (vy < 0 && dy > particle.moveCenter.radius) ||\n                    (vx >= 0 && dx < -particle.moveCenter.radius) ||\n                    (vy >= 0 && dy < -particle.moveCenter.radius)) {\n                    return;\n                }\n                break;\n            }\n        }\n        container.particles.remove(particle, undefined, true);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-out-modes/esm/DestroyOutMode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-updater-out-modes/esm/NoneOutMode.js":
/*!***********************************************************************!*\
  !*** ./node_modules/tsparticles-updater-out-modes/esm/NoneOutMode.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NoneOutMode: () => (/* binding */ NoneOutMode)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/Utils.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/Vector.js\");\n\nclass NoneOutMode {\n    constructor(container) {\n        this.container = container;\n        this.modes = [\"none\"];\n    }\n    update(particle, direction, delta, outMode) {\n        if (!this.modes.includes(outMode)) {\n            return;\n        }\n        if ((particle.options.move.distance.horizontal &&\n            (direction === \"left\" || direction === \"right\")) ||\n            (particle.options.move.distance.vertical &&\n                (direction === \"top\" || direction === \"bottom\"))) {\n            return;\n        }\n        const gravityOptions = particle.options.move.gravity, container = this.container;\n        const canvasSize = container.canvas.size;\n        const pRadius = particle.getRadius();\n        if (!gravityOptions.enable) {\n            if ((particle.velocity.y > 0 && particle.position.y <= canvasSize.height + pRadius) ||\n                (particle.velocity.y < 0 && particle.position.y >= -pRadius) ||\n                (particle.velocity.x > 0 && particle.position.x <= canvasSize.width + pRadius) ||\n                (particle.velocity.x < 0 && particle.position.x >= -pRadius)) {\n                return;\n            }\n            if (!(0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isPointInside)(particle.position, container.canvas.size, tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.Vector.origin, pRadius, direction)) {\n                container.particles.remove(particle);\n            }\n        }\n        else {\n            const position = particle.position;\n            if ((!gravityOptions.inverse &&\n                position.y > canvasSize.height + pRadius &&\n                direction === \"bottom\") ||\n                (gravityOptions.inverse && position.y < -pRadius && direction === \"top\")) {\n                container.particles.remove(particle);\n            }\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-out-modes/esm/NoneOutMode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-updater-out-modes/esm/OutOfCanvasUpdater.js":
/*!******************************************************************************!*\
  !*** ./node_modules/tsparticles-updater-out-modes/esm/OutOfCanvasUpdater.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OutOfCanvasUpdater: () => (/* binding */ OutOfCanvasUpdater)\n/* harmony export */ });\n/* harmony import */ var _BounceOutMode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BounceOutMode */ \"(ssr)/./node_modules/tsparticles-updater-out-modes/esm/BounceOutMode.js\");\n/* harmony import */ var _DestroyOutMode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DestroyOutMode */ \"(ssr)/./node_modules/tsparticles-updater-out-modes/esm/DestroyOutMode.js\");\n/* harmony import */ var _NoneOutMode__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./NoneOutMode */ \"(ssr)/./node_modules/tsparticles-updater-out-modes/esm/NoneOutMode.js\");\n/* harmony import */ var _OutOutMode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OutOutMode */ \"(ssr)/./node_modules/tsparticles-updater-out-modes/esm/OutOutMode.js\");\n\n\n\n\nclass OutOfCanvasUpdater {\n    constructor(container) {\n        this.container = container;\n        this._updateOutMode = (particle, delta, outMode, direction) => {\n            for (const updater of this.updaters) {\n                updater.update(particle, direction, delta, outMode);\n            }\n        };\n        this.updaters = [\n            new _BounceOutMode__WEBPACK_IMPORTED_MODULE_0__.BounceOutMode(container),\n            new _DestroyOutMode__WEBPACK_IMPORTED_MODULE_1__.DestroyOutMode(container),\n            new _OutOutMode__WEBPACK_IMPORTED_MODULE_2__.OutOutMode(container),\n            new _NoneOutMode__WEBPACK_IMPORTED_MODULE_3__.NoneOutMode(container),\n        ];\n    }\n    init() {\n    }\n    isEnabled(particle) {\n        return !particle.destroyed && !particle.spawning;\n    }\n    update(particle, delta) {\n        const outModes = particle.options.move.outModes;\n        this._updateOutMode(particle, delta, outModes.bottom ?? outModes.default, \"bottom\");\n        this._updateOutMode(particle, delta, outModes.left ?? outModes.default, \"left\");\n        this._updateOutMode(particle, delta, outModes.right ?? outModes.default, \"right\");\n        this._updateOutMode(particle, delta, outModes.top ?? outModes.default, \"top\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-out-modes/esm/OutOfCanvasUpdater.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-updater-out-modes/esm/OutOutMode.js":
/*!**********************************************************************!*\
  !*** ./node_modules/tsparticles-updater-out-modes/esm/OutOutMode.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OutOutMode: () => (/* binding */ OutOutMode)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/Vector.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/Utils.js\");\n\nclass OutOutMode {\n    constructor(container) {\n        this.container = container;\n        this.modes = [\"out\"];\n    }\n    update(particle, direction, delta, outMode) {\n        if (!this.modes.includes(outMode)) {\n            return;\n        }\n        const container = this.container;\n        switch (particle.outType) {\n            case \"inside\": {\n                const { x: vx, y: vy } = particle.velocity;\n                const circVec = tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.Vector.origin;\n                circVec.length = particle.moveCenter.radius;\n                circVec.angle = particle.velocity.angle + Math.PI;\n                circVec.addTo(tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.Vector.create(particle.moveCenter));\n                const { dx, dy } = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getDistances)(particle.position, circVec);\n                if ((vx <= 0 && dx >= 0) || (vy <= 0 && dy >= 0) || (vx >= 0 && dx <= 0) || (vy >= 0 && dy <= 0)) {\n                    return;\n                }\n                particle.position.x = Math.floor((0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.randomInRange)({\n                    min: 0,\n                    max: container.canvas.size.width,\n                }));\n                particle.position.y = Math.floor((0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.randomInRange)({\n                    min: 0,\n                    max: container.canvas.size.height,\n                }));\n                const { dx: newDx, dy: newDy } = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getDistances)(particle.position, particle.moveCenter);\n                particle.direction = Math.atan2(-newDy, -newDx);\n                particle.velocity.angle = particle.direction;\n                break;\n            }\n            default: {\n                if ((0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.isPointInside)(particle.position, container.canvas.size, tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.Vector.origin, particle.getRadius(), direction)) {\n                    return;\n                }\n                switch (particle.outType) {\n                    case \"outside\": {\n                        particle.position.x =\n                            Math.floor((0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.randomInRange)({\n                                min: -particle.moveCenter.radius,\n                                max: particle.moveCenter.radius,\n                            })) + particle.moveCenter.x;\n                        particle.position.y =\n                            Math.floor((0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.randomInRange)({\n                                min: -particle.moveCenter.radius,\n                                max: particle.moveCenter.radius,\n                            })) + particle.moveCenter.y;\n                        const { dx, dy } = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getDistances)(particle.position, particle.moveCenter);\n                        if (particle.moveCenter.radius) {\n                            particle.direction = Math.atan2(dy, dx);\n                            particle.velocity.angle = particle.direction;\n                        }\n                        break;\n                    }\n                    case \"normal\": {\n                        const wrap = particle.options.move.warp, canvasSize = container.canvas.size, newPos = {\n                            bottom: canvasSize.height + particle.getRadius() + particle.offset.y,\n                            left: -particle.getRadius() - particle.offset.x,\n                            right: canvasSize.width + particle.getRadius() + particle.offset.x,\n                            top: -particle.getRadius() - particle.offset.y,\n                        }, sizeValue = particle.getRadius(), nextBounds = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.calculateBounds)(particle.position, sizeValue);\n                        if (direction === \"right\" &&\n                            nextBounds.left > canvasSize.width + particle.offset.x) {\n                            particle.position.x = newPos.left;\n                            particle.initialPosition.x = particle.position.x;\n                            if (!wrap) {\n                                particle.position.y = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getRandom)() * canvasSize.height;\n                                particle.initialPosition.y = particle.position.y;\n                            }\n                        }\n                        else if (direction === \"left\" && nextBounds.right < -particle.offset.x) {\n                            particle.position.x = newPos.right;\n                            particle.initialPosition.x = particle.position.x;\n                            if (!wrap) {\n                                particle.position.y = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getRandom)() * canvasSize.height;\n                                particle.initialPosition.y = particle.position.y;\n                            }\n                        }\n                        if (direction === \"bottom\" &&\n                            nextBounds.top > canvasSize.height + particle.offset.y) {\n                            if (!wrap) {\n                                particle.position.x = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getRandom)() * canvasSize.width;\n                                particle.initialPosition.x = particle.position.x;\n                            }\n                            particle.position.y = newPos.top;\n                            particle.initialPosition.y = particle.position.y;\n                        }\n                        else if (direction === \"top\" && nextBounds.bottom < -particle.offset.y) {\n                            if (!wrap) {\n                                particle.position.x = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getRandom)() * canvasSize.width;\n                                particle.initialPosition.x = particle.position.x;\n                            }\n                            particle.position.y = newPos.bottom;\n                            particle.initialPosition.y = particle.position.y;\n                        }\n                        break;\n                    }\n                }\n                break;\n            }\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-out-modes/esm/OutOutMode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-updater-out-modes/esm/Utils.js":
/*!*****************************************************************!*\
  !*** ./node_modules/tsparticles-updater-out-modes/esm/Utils.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bounceHorizontal: () => (/* binding */ bounceHorizontal),\n/* harmony export */   bounceVertical: () => (/* binding */ bounceVertical)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n\nfunction bounceHorizontal(data) {\n    if ((data.outMode !== \"bounce\" &&\n        data.outMode !== \"bounce-horizontal\" &&\n        data.outMode !== \"bounceHorizontal\" &&\n        data.outMode !== \"split\") ||\n        (data.direction !== \"left\" && data.direction !== \"right\")) {\n        return;\n    }\n    if (data.bounds.right < 0 && data.direction === \"left\") {\n        data.particle.position.x = data.size + data.offset.x;\n    }\n    else if (data.bounds.left > data.canvasSize.width && data.direction === \"right\") {\n        data.particle.position.x = data.canvasSize.width - data.size - data.offset.x;\n    }\n    const velocity = data.particle.velocity.x;\n    let bounced = false;\n    if ((data.direction === \"right\" && data.bounds.right >= data.canvasSize.width && velocity > 0) ||\n        (data.direction === \"left\" && data.bounds.left <= 0 && velocity < 0)) {\n        const newVelocity = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getValue)(data.particle.options.bounce.horizontal);\n        data.particle.velocity.x *= -newVelocity;\n        bounced = true;\n    }\n    if (!bounced) {\n        return;\n    }\n    const minPos = data.offset.x + data.size;\n    if (data.bounds.right >= data.canvasSize.width && data.direction === \"right\") {\n        data.particle.position.x = data.canvasSize.width - minPos;\n    }\n    else if (data.bounds.left <= 0 && data.direction === \"left\") {\n        data.particle.position.x = minPos;\n    }\n    if (data.outMode === \"split\") {\n        data.particle.destroy();\n    }\n}\nfunction bounceVertical(data) {\n    if ((data.outMode !== \"bounce\" &&\n        data.outMode !== \"bounce-vertical\" &&\n        data.outMode !== \"bounceVertical\" &&\n        data.outMode !== \"split\") ||\n        (data.direction !== \"bottom\" && data.direction !== \"top\")) {\n        return;\n    }\n    if (data.bounds.bottom < 0 && data.direction === \"top\") {\n        data.particle.position.y = data.size + data.offset.y;\n    }\n    else if (data.bounds.top > data.canvasSize.height && data.direction === \"bottom\") {\n        data.particle.position.y = data.canvasSize.height - data.size - data.offset.y;\n    }\n    const velocity = data.particle.velocity.y;\n    let bounced = false;\n    if ((data.direction === \"bottom\" && data.bounds.bottom >= data.canvasSize.height && velocity > 0) ||\n        (data.direction === \"top\" && data.bounds.top <= 0 && velocity < 0)) {\n        const newVelocity = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getValue)(data.particle.options.bounce.vertical);\n        data.particle.velocity.y *= -newVelocity;\n        bounced = true;\n    }\n    if (!bounced) {\n        return;\n    }\n    const minPos = data.offset.y + data.size;\n    if (data.bounds.bottom >= data.canvasSize.height && data.direction === \"bottom\") {\n        data.particle.position.y = data.canvasSize.height - minPos;\n    }\n    else if (data.bounds.top <= 0 && data.direction === \"top\") {\n        data.particle.position.y = minPos;\n    }\n    if (data.outMode === \"split\") {\n        data.particle.destroy();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-out-modes/esm/Utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-updater-out-modes/esm/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/tsparticles-updater-out-modes/esm/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadOutModesUpdater: () => (/* binding */ loadOutModesUpdater)\n/* harmony export */ });\n/* harmony import */ var _OutOfCanvasUpdater__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./OutOfCanvasUpdater */ \"(ssr)/./node_modules/tsparticles-updater-out-modes/esm/OutOfCanvasUpdater.js\");\n\nasync function loadOutModesUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"outModes\", (container) => new _OutOfCanvasUpdater__WEBPACK_IMPORTED_MODULE_0__.OutOfCanvasUpdater(container), refresh);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtdXBkYXRlci1vdXQtbW9kZXMvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBEO0FBQ25EO0FBQ1AsbUVBQW1FLG1FQUFrQjtBQUNyRiIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1dHVyaXN0aWMtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL3RzcGFydGljbGVzLXVwZGF0ZXItb3V0LW1vZGVzL2VzbS9pbmRleC5qcz83MDQ0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE91dE9mQ2FudmFzVXBkYXRlciB9IGZyb20gXCIuL091dE9mQ2FudmFzVXBkYXRlclwiO1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGxvYWRPdXRNb2Rlc1VwZGF0ZXIoZW5naW5lLCByZWZyZXNoID0gdHJ1ZSkge1xuICAgIGF3YWl0IGVuZ2luZS5hZGRQYXJ0aWNsZVVwZGF0ZXIoXCJvdXRNb2Rlc1wiLCAoY29udGFpbmVyKSA9PiBuZXcgT3V0T2ZDYW52YXNVcGRhdGVyKGNvbnRhaW5lciksIHJlZnJlc2gpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-out-modes/esm/index.js\n");

/***/ })

};
;