{"version": 3, "sources": ["../../../src/client/components/navigation.ts"], "names": ["useContext", "useMemo", "AppRouterContext", "LayoutRouterContext", "SearchParamsContext", "PathnameContext", "PathParamsContext", "getSegmentValue", "PAGE_SEGMENT_KEY", "DEFAULT_SEGMENT_KEY", "ReadonlyURLSearchParams", "useSearchParams", "searchParams", "readonlySearchParams", "window", "bailoutToClientRendering", "require", "usePathname", "ServerInsertedHTMLContext", "useServerInsertedHTML", "useRouter", "router", "Error", "useParams", "getSelectedLayoutSegmentPath", "tree", "parallelRouteKey", "first", "segmentPath", "node", "parallelRoutes", "children", "Object", "values", "segment", "segmentValue", "startsWith", "push", "useSelectedLayoutSegments", "context", "useSelectedLayoutSegment", "selectedLayoutSegments", "length", "selectedLayoutSegment", "notFound", "redirect", "permanentRedirect", "RedirectType"], "mappings": "AAAA,SAASA,UAAU,EAAEC,OAAO,QAAQ,QAAO;AAE3C,SACEC,gBAAgB,EAChBC,mBAAmB,QAEd,qDAAoD;AAC3D,SACEC,mBAAmB,EACnBC,eAAe,EACfC,iBAAiB,QACZ,uDAAsD;AAC7D,SAASC,eAAe,QAAQ,8CAA6C;AAC7E,SAASC,gBAAgB,EAAEC,mBAAmB,QAAQ,2BAA0B;AAChF,SAASC,uBAAuB,QAAQ,4BAA2B;AAEnE;;;;;;;;;;;;;;;;;;;CAmBC,GACD,SAASC;IACP,MAAMC,eAAeZ,WAAWI;IAEhC,8DAA8D;IAC9D,0EAA0E;IAC1E,kBAAkB;IAClB,MAAMS,uBAAuBZ,QAAQ;QACnC,IAAI,CAACW,cAAc;YACjB,yEAAyE;YACzE,aAAa;YACb,OAAO;QACT;QAEA,OAAO,IAAIF,wBAAwBE;IACrC,GAAG;QAACA;KAAa;IAEjB,IAAI,OAAOE,WAAW,aAAa;QACjC,iEAAiE;QACjE,MAAM,EAAEC,wBAAwB,EAAE,GAChCC,QAAQ;QACV,mEAAmE;QACnED,yBAAyB;IAC3B;IAEA,OAAOF;AACT;AAEA;;;;;;;;;;;;;;;;CAgBC,GACD,SAASI;IACP,8EAA8E;IAC9E,0EAA0E;IAC1E,OAAOjB,WAAWK;AACpB;AAEA,SACEa,yBAAyB,EACzBC,qBAAqB,QAChB,uDAAsD;AAE7D;;;;;;;;;;;;;;;;;CAiBC,GACD,SAASC;IACP,MAAMC,SAASrB,WAAWE;IAC1B,IAAImB,WAAW,MAAM;QACnB,MAAM,IAAIC,MAAM;IAClB;IAEA,OAAOD;AACT;AAMA;;;;;;;;;;;;;;;;CAgBC,GACD,SAASE;IACP,OAAOvB,WAAWM;AACpB;AAEA,0EAA0E,GAC1E,SAASkB,6BACPC,IAAuB,EACvBC,gBAAwB,EACxBC,KAAY,EACZC,WAA0B;IAD1BD,IAAAA,kBAAAA,QAAQ;IACRC,IAAAA,wBAAAA,cAAwB,EAAE;IAE1B,IAAIC;IACJ,IAAIF,OAAO;QACT,kEAAkE;QAClEE,OAAOJ,IAAI,CAAC,EAAE,CAACC,iBAAiB;IAClC,OAAO;QACL,oGAAoG;QACpG,MAAMI,iBAAiBL,IAAI,CAAC,EAAE;YACvBK;QAAPD,OAAOC,CAAAA,2BAAAA,eAAeC,QAAQ,YAAvBD,2BAA2BE,OAAOC,MAAM,CAACH,eAAe,CAAC,EAAE;IACpE;IAEA,IAAI,CAACD,MAAM,OAAOD;IAClB,MAAMM,UAAUL,IAAI,CAAC,EAAE;IAEvB,MAAMM,eAAe5B,gBAAgB2B;IACrC,IAAI,CAACC,gBAAgBA,aAAaC,UAAU,CAAC5B,mBAAmB;QAC9D,OAAOoB;IACT;IAEAA,YAAYS,IAAI,CAACF;IAEjB,OAAOX,6BACLK,MACAH,kBACA,OACAE;AAEJ;AAEA;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GACD,SAASU,0BACPZ,gBAAqC;IAArCA,IAAAA,6BAAAA,mBAA2B;IAE3B,MAAMa,UAAUvC,WAAWG;IAC3B,wFAAwF;IACxF,IAAI,CAACoC,SAAS,OAAO;IAErB,OAAOf,6BAA6Be,QAAQd,IAAI,EAAEC;AACpD;AAEA;;;;;;;;;;;;;;;;;CAiBC,GACD,SAASc,yBACPd,gBAAqC;IAArCA,IAAAA,6BAAAA,mBAA2B;IAE3B,MAAMe,yBAAyBH,0BAA0BZ;IAEzD,IAAI,CAACe,0BAA0BA,uBAAuBC,MAAM,KAAK,GAAG;QAClE,OAAO;IACT;IAEA,MAAMC,wBACJjB,qBAAqB,aACjBe,sBAAsB,CAAC,EAAE,GACzBA,sBAAsB,CAACA,uBAAuBC,MAAM,GAAG,EAAE;IAE/D,yGAAyG;IACzG,yEAAyE;IACzE,OAAOC,0BAA0BlC,sBAC7B,OACAkC;AACN;AAEA,yBAAyB;AACzB,SACEhC,eAAe,EACfM,WAAW,EACXuB,wBAAwB,EACxBF,yBAAyB,EACzBf,SAAS,EACTH,SAAS,EACTD,qBAAqB,EACrBD,yBAAyB,KAC1B;AAED,yBAAyB;AACzB,SACE0B,QAAQ,EACRC,QAAQ,EACRC,iBAAiB,EACjBC,YAAY,EACZrC,uBAAuB,QAClB,4BAA2B"}