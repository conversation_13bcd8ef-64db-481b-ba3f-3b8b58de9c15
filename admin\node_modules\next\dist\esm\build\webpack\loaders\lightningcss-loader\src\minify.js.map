{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/lightningcss-loader/src/minify.ts"], "names": ["ModuleFilenameHelpers", "webpack", "RawSource", "SourceMapSource", "<PERSON><PERSON><PERSON><PERSON>", "getTargets", "<PERSON><PERSON><PERSON>", "PLUGIN_NAME", "CSS_FILE_REG", "LightningCssMinifyPlugin", "constructor", "opts", "implementation", "otherOpts", "transformCss", "TypeError", "transform", "options", "apply", "compiler", "meta", "JSON", "stringify", "name", "version", "hooks", "compilation", "tap", "chunkHash", "_", "hash", "update", "processAssets", "tapPromise", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_SIZE", "additionalAssets", "transformAssets", "statsPrinter", "print", "for", "minimized", "green", "formatFlag", "undefined", "devtool", "loadBindings", "require", "css", "lightning", "sourcemap", "sourceMap", "includes", "include", "exclude", "test", "testRegExp", "targets", "userTargets", "transformOptions", "assets", "getAssets", "filter", "asset", "info", "matchObject", "Promise", "all", "map", "source", "sourceAndMap", "sourceAsString", "toString", "code", "from", "key", "minify", "result", "filename", "codeString", "updateAsset", "parse"], "mappings": "AAAA,aAAa;AACb,SAASA,qBAAqB,QAAQ,qCAAoC;AAC1E,SAASC,OAAO,QAAQ,qCAAoC;AAC5D,aAAa;AACb,SAASC,SAAS,EAAEC,eAAe,QAAQ,sCAAqC;AAChF,SAASC,SAAS,QAAQ,cAAa;AAEvC,SAASC,UAAU,QAAQ,UAAS;AACpC,SAASC,MAAM,QAAQ,SAAQ;AAE/B,MAAMC,cAAc;AACpB,MAAMC,eAAe;AAErB,OAAO,MAAMC;IAIXC,YAAYC,OAAY,CAAC,CAAC,CAAE;QAC1B,MAAM,EAAEC,cAAc,EAAE,GAAGC,WAAW,GAAGF;QACzC,IAAIC,kBAAkB,OAAOA,eAAeE,YAAY,KAAK,YAAY;YACvE,MAAM,IAAIC,UACR,CAAC,+GAA+G,EAAE,OAAOH,eAAeE,YAAY,CAAC,CAAC;QAE1J;QAEA,IAAI,CAACE,SAAS,GAAGJ,kCAAAA,eAAgBE,YAAY;QAC7C,IAAI,CAACG,OAAO,GAAGJ;IACjB;IAEAK,MAAMC,QAAkB,EAAE;QACxB,MAAMC,OAAOC,KAAKC,SAAS,CAAC;YAC1BC,MAAM;YACNC,SAAS;YACTP,SAAS,IAAI,CAACA,OAAO;QACvB;QAEAE,SAASM,KAAK,CAACC,WAAW,CAACC,GAAG,CAACpB,aAAa,CAACmB;YAC3CA,YAAYD,KAAK,CAACG,SAAS,CAACD,GAAG,CAACpB,aAAa,CAACsB,GAAGC,OAC/CA,KAAKC,MAAM,CAACX;YAGdM,YAAYD,KAAK,CAACO,aAAa,CAACC,UAAU,CACxC;gBACEV,MAAMhB;gBACN2B,OAAOjC,QAAQkC,WAAW,CAACC,kCAAkC;gBAC7DC,kBAAkB;YACpB,GACA,UAAY,MAAM,IAAI,CAACC,eAAe,CAACZ;YAGzCA,YAAYD,KAAK,CAACc,YAAY,CAACZ,GAAG,CAACpB,aAAa,CAACgC;gBAC/CA,aAAad,KAAK,CAACe,KAAK,CACrBC,GAAG,CAAC,uBACL,aAAa;iBACZd,GAAG,CAACpB,aAAa,CAACmC,WAAW,EAAEC,KAAK,EAAEC,UAAU,EAAE;oBACjD,aAAa;oBACb,OAAOF,YAAYC,MAAMC,WAAW,gBAAgBC;gBACtD;YACJ;QACF;IACF;IAEA,MAAcP,gBAAgBZ,WAAwB,EAAiB;QACrE,MAAM,EACJT,SAAS,EAAE6B,OAAO,EAAE,EACrB,GAAGpB,YAAYP,QAAQ;QAExB,IAAI,CAAC,IAAI,CAACH,SAAS,EAAE;YACnB,MAAM,EAAE+B,YAAY,EAAE,GAAGC,QAAQ;YACjC,IAAI,CAAChC,SAAS,GAAG,AAAC,CAAA,MAAM+B,cAAa,EAAGE,GAAG,CAACC,SAAS,CAAClC,SAAS;QACjE;QAEA,MAAMmC,YACJ,IAAI,CAAClC,OAAO,CAACmC,SAAS,KAAKP,YACrBC,WAAW,AAACA,QAAmBO,QAAQ,CAAC,gBAC1C,IAAI,CAACpC,OAAO,CAACmC,SAAS;QAE5B,MAAM,EACJE,OAAO,EACPC,OAAO,EACPC,MAAMC,UAAU,EAChBC,SAASC,WAAW,EACpB,GAAGC,kBACJ,GAAG,IAAI,CAAC3C,OAAO;QAEhB,MAAM4C,SAASnC,YAAYoC,SAAS,GAAGC,MAAM,CAC3C,CAACC,QACC,+BAA+B;YAC/B,CAACA,MAAMC,IAAI,CAACvB,SAAS,IAErB,AADA,0BAA0B;YACzBe,CAAAA,cAAcjD,YAAW,EAAGgD,IAAI,CAACQ,MAAMzC,IAAI,KAC5CvB,sBAAsBkE,WAAW,CAAC;gBAAEZ;gBAASC;YAAQ,GAAGS,MAAMzC,IAAI;QAGtE,MAAM4C,QAAQC,GAAG,CACfP,OAAOQ,GAAG,CAAC,OAAOL;YAChB,MAAM,EAAEM,MAAM,EAAED,GAAG,EAAE,GAAGL,MAAMM,MAAM,CAACC,YAAY;YACjD,MAAMC,iBAAiBF,OAAOG,QAAQ;YACtC,MAAMC,OAAO,OAAOJ,WAAW,WAAWhE,OAAOqE,IAAI,CAACL,UAAUA;YAChE,MAAMZ,UAAUrD,WAAW;gBACzBqD,SAASC;gBACTiB,KAAKxE,UAAUyE,MAAM;YACvB;YAEA,MAAMC,SAAS,MAAM,IAAI,CAAC9D,SAAS,CAAE;gBACnC+D,UAAUf,MAAMzC,IAAI;gBACpBmD;gBACAG,QAAQ;gBACRzB,WAAWD;gBACXO;gBACA,GAAGE,gBAAgB;YACrB;YACA,MAAMoB,aAAaF,OAAOJ,IAAI,CAACD,QAAQ;YAEvC/C,YAAYuD,WAAW,CACrBjB,MAAMzC,IAAI,EACV,aAAa;YACb4B,YACI,IAAIhD,gBACF6E,YACAhB,MAAMzC,IAAI,EACVF,KAAK6D,KAAK,CAACJ,OAAOT,GAAG,CAAEI,QAAQ,KAC/BD,gBACAH,KACA,QAEF,IAAInE,UAAU8E,aAClB;gBACE,GAAGhB,MAAMC,IAAI;gBACbvB,WAAW;YACb;QAEJ;IAEJ;AACF"}