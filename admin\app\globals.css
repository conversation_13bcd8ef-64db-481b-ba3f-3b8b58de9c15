@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #00d4ff, #0ea5e9);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #0ea5e9, #00d4ff);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Dark theme by default */
.dark {
  color-scheme: dark;
}

/* Neon glow effect */
.neon-glow {
  text-shadow: 
    0 0 2px currentColor,
    0 0 4px currentColor,
    0 0 8px currentColor;
}

/* Cyber grid background */
.cyber-grid {
  background-image: 
    linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* Custom button styles */
.btn-cyber {
  @apply relative px-6 py-3 font-tech font-semibold text-white bg-transparent border-2 border-neon-blue rounded-lg overflow-hidden transition-all duration-300 hover:text-black;
}

.btn-cyber::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, #00d4ff, transparent);
  transition: left 0.5s;
}

.btn-cyber:hover::before {
  left: 100%;
}

.btn-cyber::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #00d4ff;
  z-index: -1;
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s;
}

.btn-cyber:hover::after {
  transform: scaleX(1);
}

/* Loading animation */
.loading-dots {
  display: inline-block;
  position: relative;
  width: 80px;
  height: 80px;
}

.loading-dots div {
  position: absolute;
  top: 33px;
  width: 13px;
  height: 13px;
  border-radius: 50%;
  background: #00d4ff;
  animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.loading-dots div:nth-child(1) {
  left: 8px;
  animation: loading-dots1 0.6s infinite;
}

.loading-dots div:nth-child(2) {
  left: 8px;
  animation: loading-dots2 0.6s infinite;
}

.loading-dots div:nth-child(3) {
  left: 32px;
  animation: loading-dots2 0.6s infinite;
}

.loading-dots div:nth-child(4) {
  left: 56px;
  animation: loading-dots3 0.6s infinite;
}

@keyframes loading-dots1 {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes loading-dots3 {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0);
  }
}

@keyframes loading-dots2 {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(24px, 0);
  }
}

/* Form styles */
.form-input {
  @apply w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-neon-blue focus:border-neon-blue transition-all duration-300;
}

.form-textarea {
  @apply w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-neon-blue focus:border-neon-blue transition-all duration-300 resize-none;
}

.form-select {
  @apply w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-neon-blue focus:border-neon-blue transition-all duration-300;
}

/* Card styles */
.admin-card {
  @apply bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg p-6 hover:border-neon-blue/50 transition-all duration-300;
}

/* Table styles */
.admin-table {
  @apply w-full bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg overflow-hidden;
}

.admin-table th {
  @apply px-6 py-4 bg-gray-800/50 text-left text-xs font-medium text-gray-300 uppercase tracking-wider border-b border-gray-700;
}

.admin-table td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-300 border-b border-gray-800;
}

.admin-table tr:hover {
  @apply bg-gray-800/30;
}

/* Status badges */
.status-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-published {
  @apply bg-green-100 text-green-800;
}

.status-draft {
  @apply bg-yellow-100 text-yellow-800;
}

.status-archived {
  @apply bg-gray-100 text-gray-800;
}

/* Responsive design utilities */
@media (max-width: 768px) {
  .admin-card {
    @apply p-4;
  }
  
  .admin-table th,
  .admin-table td {
    @apply px-4 py-3;
  }
}
