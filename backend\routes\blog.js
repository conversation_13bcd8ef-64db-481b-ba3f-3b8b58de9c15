const express = require('express');
const { body, validationResult } = require('express-validator');
const BlogPost = require('../models/BlogPost');
const { auth, adminAuth, logAdminAction } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/blog
// @desc    Get all published blog posts
// @access  Public
router.get('/', async (req, res) => {
  try {
    const { category, featured, limit, page = 1, search } = req.query;
    
    let query = { isPublished: true };
    
    if (category) query.category = category;
    if (featured === 'true') query.isFeatured = true;
    
    let postsQuery;
    
    if (search) {
      postsQuery = BlogPost.search(search);
    } else {
      postsQuery = BlogPost.find(query);
    }
    
    const skip = (page - 1) * (limit || 10);
    
    postsQuery = postsQuery
      .sort({ isFeatured: -1, publishedAt: -1 })
      .skip(skip);
    
    if (limit) postsQuery = postsQuery.limit(parseInt(limit));
    
    const posts = await postsQuery;
    const total = await BlogPost.countDocuments(query);
    
    res.json({
      success: true,
      count: posts.length,
      total,
      data: posts,
      pagination: limit ? {
        current: parseInt(page),
        pages: Math.ceil(total / limit),
        total
      } : null
    });
  } catch (error) {
    console.error('Get blog posts error:', error);
    res.status(500).json({ message: 'Server error fetching blog posts' });
  }
});

// @route   GET /api/blog/:slug
// @desc    Get single blog post by slug
// @access  Public
router.get('/:slug', async (req, res) => {
  try {
    const post = await BlogPost.findOne({ 
      slug: req.params.slug, 
      isPublished: true 
    });
    
    if (!post) {
      return res.status(404).json({ message: 'Blog post not found' });
    }
    
    // Increment views
    await post.incrementViews();
    
    res.json({
      success: true,
      data: post
    });
  } catch (error) {
    console.error('Get blog post error:', error);
    res.status(500).json({ message: 'Server error fetching blog post' });
  }
});

// @route   POST /api/blog/:slug/like
// @desc    Like a blog post
// @access  Public
router.post('/:slug/like', async (req, res) => {
  try {
    const post = await BlogPost.findOne({ 
      slug: req.params.slug, 
      isPublished: true 
    });
    
    if (!post) {
      return res.status(404).json({ message: 'Blog post not found' });
    }
    
    await post.incrementLikes();
    
    res.json({
      success: true,
      message: 'Blog post liked',
      likes: post.likes
    });
  } catch (error) {
    console.error('Like blog post error:', error);
    res.status(500).json({ message: 'Server error liking blog post' });
  }
});

// Admin routes below require authentication

// @route   GET /api/blog/admin/all
// @desc    Get all blog posts for admin
// @access  Private/Admin
router.get('/admin/all', auth, adminAuth, async (req, res) => {
  try {
    const posts = await BlogPost.find()
      .sort({ createdAt: -1 });
    
    res.json({
      success: true,
      count: posts.length,
      data: posts
    });
  } catch (error) {
    console.error('Get all blog posts error:', error);
    res.status(500).json({ message: 'Server error fetching blog posts' });
  }
});

// @route   POST /api/blog
// @desc    Create new blog post
// @access  Private/Admin
router.post('/',
  auth,
  adminAuth,
  logAdminAction('CREATE_BLOG_POST'),
  [
    body('title').notEmpty().trim(),
    body('excerpt').notEmpty().isLength({ max: 300 }),
    body('content').notEmpty(),
    body('category').isIn(['web-development', 'ai', 'technology', 'career', 'tutorial', 'personal', 'review'])
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ 
          message: 'Invalid input',
          errors: errors.array() 
        });
      }

      const post = new BlogPost(req.body);
      await post.save();

      res.status(201).json({
        success: true,
        message: 'Blog post created successfully',
        data: post
      });
    } catch (error) {
      console.error('Create blog post error:', error);
      res.status(500).json({ message: 'Server error creating blog post' });
    }
  }
);

// @route   PUT /api/blog/:id
// @desc    Update blog post
// @access  Private/Admin
router.put('/:id',
  auth,
  adminAuth,
  logAdminAction('UPDATE_BLOG_POST'),
  async (req, res) => {
    try {
      const post = await BlogPost.findByIdAndUpdate(
        req.params.id,
        req.body,
        { new: true, runValidators: true }
      );

      if (!post) {
        return res.status(404).json({ message: 'Blog post not found' });
      }

      res.json({
        success: true,
        message: 'Blog post updated successfully',
        data: post
      });
    } catch (error) {
      console.error('Update blog post error:', error);
      res.status(500).json({ message: 'Server error updating blog post' });
    }
  }
);

// @route   DELETE /api/blog/:id
// @desc    Delete blog post
// @access  Private/Admin
router.delete('/:id',
  auth,
  adminAuth,
  logAdminAction('DELETE_BLOG_POST'),
  async (req, res) => {
    try {
      const post = await BlogPost.findByIdAndDelete(req.params.id);

      if (!post) {
        return res.status(404).json({ message: 'Blog post not found' });
      }

      res.json({
        success: true,
        message: 'Blog post deleted successfully'
      });
    } catch (error) {
      console.error('Delete blog post error:', error);
      res.status(500).json({ message: 'Server error deleting blog post' });
    }
  }
);

// @route   PATCH /api/blog/:id/toggle-featured
// @desc    Toggle blog post featured status
// @access  Private/Admin
router.patch('/:id/toggle-featured',
  auth,
  adminAuth,
  logAdminAction('TOGGLE_BLOG_FEATURED'),
  async (req, res) => {
    try {
      const post = await BlogPost.findById(req.params.id);

      if (!post) {
        return res.status(404).json({ message: 'Blog post not found' });
      }

      post.isFeatured = !post.isFeatured;
      await post.save();

      res.json({
        success: true,
        message: `Blog post ${post.isFeatured ? 'featured' : 'unfeatured'} successfully`,
        data: post
      });
    } catch (error) {
      console.error('Toggle featured error:', error);
      res.status(500).json({ message: 'Server error toggling featured status' });
    }
  }
);

// @route   PATCH /api/blog/:id/toggle-published
// @desc    Toggle blog post published status
// @access  Private/Admin
router.patch('/:id/toggle-published',
  auth,
  adminAuth,
  logAdminAction('TOGGLE_BLOG_PUBLISHED'),
  async (req, res) => {
    try {
      const post = await BlogPost.findById(req.params.id);

      if (!post) {
        return res.status(404).json({ message: 'Blog post not found' });
      }

      post.isPublished = !post.isPublished;
      if (post.isPublished && !post.publishedAt) {
        post.publishedAt = new Date();
      }
      await post.save();

      res.json({
        success: true,
        message: `Blog post ${post.isPublished ? 'published' : 'unpublished'} successfully`,
        data: post
      });
    } catch (error) {
      console.error('Toggle published error:', error);
      res.status(500).json({ message: 'Server error toggling published status' });
    }
  }
);

module.exports = router;
