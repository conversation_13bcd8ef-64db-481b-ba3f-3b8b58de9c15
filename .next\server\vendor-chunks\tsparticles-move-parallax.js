"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tsparticles-move-parallax";
exports.ids = ["vendor-chunks/tsparticles-move-parallax"];
exports.modules = {

/***/ "(ssr)/./node_modules/tsparticles-move-parallax/esm/ParallaxMover.js":
/*!*********************************************************************!*\
  !*** ./node_modules/tsparticles-move-parallax/esm/ParallaxMover.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParallaxMover: () => (/* binding */ ParallaxMover)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/Utils.js\");\n\nclass ParallaxMover {\n    init() {\n    }\n    isEnabled(particle) {\n        return (!(0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isSsr)() &&\n            !particle.destroyed &&\n            particle.container.actualOptions.interactivity.events.onHover.parallax.enable);\n    }\n    move(particle) {\n        const container = particle.container, options = container.actualOptions, parallaxOptions = options.interactivity.events.onHover.parallax;\n        if ((0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isSsr)() || !parallaxOptions.enable) {\n            return;\n        }\n        const parallaxForce = parallaxOptions.force, mousePos = container.interactivity.mouse.position;\n        if (!mousePos) {\n            return;\n        }\n        const canvasSize = container.canvas.size, canvasCenter = {\n            x: canvasSize.width / 2,\n            y: canvasSize.height / 2,\n        }, parallaxSmooth = parallaxOptions.smooth, factor = particle.getRadius() / parallaxForce, centerDistance = {\n            x: (mousePos.x - canvasCenter.x) * factor,\n            y: (mousePos.y - canvasCenter.y) * factor,\n        }, { offset } = particle;\n        offset.x += (centerDistance.x - offset.x) / parallaxSmooth;\n        offset.y += (centerDistance.y - offset.y) / parallaxSmooth;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtbW92ZS1wYXJhbGxheC9lc20vUGFyYWxsYXhNb3Zlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEyQztBQUNwQztBQUNQO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQix5REFBSztBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSx5REFBSztBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVMsSUFBSSxTQUFTO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnV0dXJpc3RpYy1wb3J0Zm9saW8vLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtbW92ZS1wYXJhbGxheC9lc20vUGFyYWxsYXhNb3Zlci5qcz9kOTUzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzU3NyIH0gZnJvbSBcInRzcGFydGljbGVzLWVuZ2luZVwiO1xuZXhwb3J0IGNsYXNzIFBhcmFsbGF4TW92ZXIge1xuICAgIGluaXQoKSB7XG4gICAgfVxuICAgIGlzRW5hYmxlZChwYXJ0aWNsZSkge1xuICAgICAgICByZXR1cm4gKCFpc1NzcigpICYmXG4gICAgICAgICAgICAhcGFydGljbGUuZGVzdHJveWVkICYmXG4gICAgICAgICAgICBwYXJ0aWNsZS5jb250YWluZXIuYWN0dWFsT3B0aW9ucy5pbnRlcmFjdGl2aXR5LmV2ZW50cy5vbkhvdmVyLnBhcmFsbGF4LmVuYWJsZSk7XG4gICAgfVxuICAgIG1vdmUocGFydGljbGUpIHtcbiAgICAgICAgY29uc3QgY29udGFpbmVyID0gcGFydGljbGUuY29udGFpbmVyLCBvcHRpb25zID0gY29udGFpbmVyLmFjdHVhbE9wdGlvbnMsIHBhcmFsbGF4T3B0aW9ucyA9IG9wdGlvbnMuaW50ZXJhY3Rpdml0eS5ldmVudHMub25Ib3Zlci5wYXJhbGxheDtcbiAgICAgICAgaWYgKGlzU3NyKCkgfHwgIXBhcmFsbGF4T3B0aW9ucy5lbmFibGUpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBwYXJhbGxheEZvcmNlID0gcGFyYWxsYXhPcHRpb25zLmZvcmNlLCBtb3VzZVBvcyA9IGNvbnRhaW5lci5pbnRlcmFjdGl2aXR5Lm1vdXNlLnBvc2l0aW9uO1xuICAgICAgICBpZiAoIW1vdXNlUG9zKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgY2FudmFzU2l6ZSA9IGNvbnRhaW5lci5jYW52YXMuc2l6ZSwgY2FudmFzQ2VudGVyID0ge1xuICAgICAgICAgICAgeDogY2FudmFzU2l6ZS53aWR0aCAvIDIsXG4gICAgICAgICAgICB5OiBjYW52YXNTaXplLmhlaWdodCAvIDIsXG4gICAgICAgIH0sIHBhcmFsbGF4U21vb3RoID0gcGFyYWxsYXhPcHRpb25zLnNtb290aCwgZmFjdG9yID0gcGFydGljbGUuZ2V0UmFkaXVzKCkgLyBwYXJhbGxheEZvcmNlLCBjZW50ZXJEaXN0YW5jZSA9IHtcbiAgICAgICAgICAgIHg6IChtb3VzZVBvcy54IC0gY2FudmFzQ2VudGVyLngpICogZmFjdG9yLFxuICAgICAgICAgICAgeTogKG1vdXNlUG9zLnkgLSBjYW52YXNDZW50ZXIueSkgKiBmYWN0b3IsXG4gICAgICAgIH0sIHsgb2Zmc2V0IH0gPSBwYXJ0aWNsZTtcbiAgICAgICAgb2Zmc2V0LnggKz0gKGNlbnRlckRpc3RhbmNlLnggLSBvZmZzZXQueCkgLyBwYXJhbGxheFNtb290aDtcbiAgICAgICAgb2Zmc2V0LnkgKz0gKGNlbnRlckRpc3RhbmNlLnkgLSBvZmZzZXQueSkgLyBwYXJhbGxheFNtb290aDtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-move-parallax/esm/ParallaxMover.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-move-parallax/esm/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/tsparticles-move-parallax/esm/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadParallaxMover: () => (/* binding */ loadParallaxMover)\n/* harmony export */ });\n/* harmony import */ var _ParallaxMover__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ParallaxMover */ \"(ssr)/./node_modules/tsparticles-move-parallax/esm/ParallaxMover.js\");\n\nasync function loadParallaxMover(engine, refresh = true) {\n    await engine.addMover(\"parallax\", () => new _ParallaxMover__WEBPACK_IMPORTED_MODULE_0__.ParallaxMover(), refresh);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtbW92ZS1wYXJhbGxheC9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBZ0Q7QUFDekM7QUFDUCxnREFBZ0QseURBQWE7QUFDN0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mdXR1cmlzdGljLXBvcnRmb2xpby8uL25vZGVfbW9kdWxlcy90c3BhcnRpY2xlcy1tb3ZlLXBhcmFsbGF4L2VzbS9pbmRleC5qcz8xNDllIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBhcmFsbGF4TW92ZXIgfSBmcm9tIFwiLi9QYXJhbGxheE1vdmVyXCI7XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gbG9hZFBhcmFsbGF4TW92ZXIoZW5naW5lLCByZWZyZXNoID0gdHJ1ZSkge1xuICAgIGF3YWl0IGVuZ2luZS5hZGRNb3ZlcihcInBhcmFsbGF4XCIsICgpID0+IG5ldyBQYXJhbGxheE1vdmVyKCksIHJlZnJlc2gpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-move-parallax/esm/index.js\n");

/***/ })

};
;