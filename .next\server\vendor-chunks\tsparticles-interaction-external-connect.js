"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tsparticles-interaction-external-connect";
exports.ids = ["vendor-chunks/tsparticles-interaction-external-connect"];
exports.modules = {

/***/ "(ssr)/./node_modules/tsparticles-interaction-external-connect/esm/Connector.js":
/*!********************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-external-connect/esm/Connector.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Connector: () => (/* binding */ Connector)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/ExternalInteractorBase.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/Utils.js\");\n/* harmony import */ var _Options_Classes_Connect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Options/Classes/Connect */ \"(ssr)/./node_modules/tsparticles-interaction-external-connect/esm/Options/Classes/Connect.js\");\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/tsparticles-interaction-external-connect/esm/Utils.js\");\n\n\n\nclass Connector extends tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    clear() {\n    }\n    init() {\n        const container = this.container, connect = container.actualOptions.interactivity.modes.connect;\n        if (!connect) {\n            return;\n        }\n        container.retina.connectModeDistance = connect.distance * container.retina.pixelRatio;\n        container.retina.connectModeRadius = connect.radius * container.retina.pixelRatio;\n    }\n    async interact() {\n        const container = this.container, options = container.actualOptions;\n        if (options.interactivity.events.onHover.enable && container.interactivity.status === \"pointermove\") {\n            const mousePos = container.interactivity.mouse.position;\n            if (!container.retina.connectModeDistance ||\n                container.retina.connectModeDistance < 0 ||\n                !container.retina.connectModeRadius ||\n                container.retina.connectModeRadius < 0 ||\n                !mousePos) {\n                return;\n            }\n            const distance = Math.abs(container.retina.connectModeRadius), query = container.particles.quadTree.queryCircle(mousePos, distance, (p) => this.isEnabled(p));\n            let i = 0;\n            for (const p1 of query) {\n                const pos1 = p1.getPosition();\n                for (const p2 of query.slice(i + 1)) {\n                    const pos2 = p2.getPosition(), distMax = Math.abs(container.retina.connectModeDistance), xDiff = Math.abs(pos1.x - pos2.x), yDiff = Math.abs(pos1.y - pos2.y);\n                    if (xDiff < distMax && yDiff < distMax) {\n                        (0,_Utils__WEBPACK_IMPORTED_MODULE_1__.drawConnection)(container, p1, p2);\n                    }\n                }\n                ++i;\n            }\n        }\n    }\n    isEnabled(particle) {\n        const container = this.container, mouse = container.interactivity.mouse, events = (particle?.interactivity ?? container.actualOptions.interactivity).events;\n        if (!(events.onHover.enable && mouse.position)) {\n            return false;\n        }\n        return (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.isInArray)(\"connect\", events.onHover.mode);\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.connect) {\n            options.connect = new _Options_Classes_Connect__WEBPACK_IMPORTED_MODULE_3__.Connect();\n        }\n        for (const source of sources) {\n            options.connect.load(source?.connect);\n        }\n    }\n    reset() {\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tZXh0ZXJuYWwtY29ubmVjdC9lc20vQ29ubmVjdG9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXdFO0FBQ3BCO0FBQ1g7QUFDbEMsd0JBQXdCLHNFQUFzQjtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixzREFBYztBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSw2REFBUztBQUN4QjtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0MsNkRBQU87QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1dHVyaXN0aWMtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL3RzcGFydGljbGVzLWludGVyYWN0aW9uLWV4dGVybmFsLWNvbm5lY3QvZXNtL0Nvbm5lY3Rvci5qcz9hNDU3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEV4dGVybmFsSW50ZXJhY3RvckJhc2UsIGlzSW5BcnJheSwgfSBmcm9tIFwidHNwYXJ0aWNsZXMtZW5naW5lXCI7XG5pbXBvcnQgeyBDb25uZWN0IH0gZnJvbSBcIi4vT3B0aW9ucy9DbGFzc2VzL0Nvbm5lY3RcIjtcbmltcG9ydCB7IGRyYXdDb25uZWN0aW9uIH0gZnJvbSBcIi4vVXRpbHNcIjtcbmV4cG9ydCBjbGFzcyBDb25uZWN0b3IgZXh0ZW5kcyBFeHRlcm5hbEludGVyYWN0b3JCYXNlIHtcbiAgICBjb25zdHJ1Y3Rvcihjb250YWluZXIpIHtcbiAgICAgICAgc3VwZXIoY29udGFpbmVyKTtcbiAgICB9XG4gICAgY2xlYXIoKSB7XG4gICAgfVxuICAgIGluaXQoKSB7XG4gICAgICAgIGNvbnN0IGNvbnRhaW5lciA9IHRoaXMuY29udGFpbmVyLCBjb25uZWN0ID0gY29udGFpbmVyLmFjdHVhbE9wdGlvbnMuaW50ZXJhY3Rpdml0eS5tb2Rlcy5jb25uZWN0O1xuICAgICAgICBpZiAoIWNvbm5lY3QpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBjb250YWluZXIucmV0aW5hLmNvbm5lY3RNb2RlRGlzdGFuY2UgPSBjb25uZWN0LmRpc3RhbmNlICogY29udGFpbmVyLnJldGluYS5waXhlbFJhdGlvO1xuICAgICAgICBjb250YWluZXIucmV0aW5hLmNvbm5lY3RNb2RlUmFkaXVzID0gY29ubmVjdC5yYWRpdXMgKiBjb250YWluZXIucmV0aW5hLnBpeGVsUmF0aW87XG4gICAgfVxuICAgIGFzeW5jIGludGVyYWN0KCkge1xuICAgICAgICBjb25zdCBjb250YWluZXIgPSB0aGlzLmNvbnRhaW5lciwgb3B0aW9ucyA9IGNvbnRhaW5lci5hY3R1YWxPcHRpb25zO1xuICAgICAgICBpZiAob3B0aW9ucy5pbnRlcmFjdGl2aXR5LmV2ZW50cy5vbkhvdmVyLmVuYWJsZSAmJiBjb250YWluZXIuaW50ZXJhY3Rpdml0eS5zdGF0dXMgPT09IFwicG9pbnRlcm1vdmVcIikge1xuICAgICAgICAgICAgY29uc3QgbW91c2VQb3MgPSBjb250YWluZXIuaW50ZXJhY3Rpdml0eS5tb3VzZS5wb3NpdGlvbjtcbiAgICAgICAgICAgIGlmICghY29udGFpbmVyLnJldGluYS5jb25uZWN0TW9kZURpc3RhbmNlIHx8XG4gICAgICAgICAgICAgICAgY29udGFpbmVyLnJldGluYS5jb25uZWN0TW9kZURpc3RhbmNlIDwgMCB8fFxuICAgICAgICAgICAgICAgICFjb250YWluZXIucmV0aW5hLmNvbm5lY3RNb2RlUmFkaXVzIHx8XG4gICAgICAgICAgICAgICAgY29udGFpbmVyLnJldGluYS5jb25uZWN0TW9kZVJhZGl1cyA8IDAgfHxcbiAgICAgICAgICAgICAgICAhbW91c2VQb3MpIHtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBkaXN0YW5jZSA9IE1hdGguYWJzKGNvbnRhaW5lci5yZXRpbmEuY29ubmVjdE1vZGVSYWRpdXMpLCBxdWVyeSA9IGNvbnRhaW5lci5wYXJ0aWNsZXMucXVhZFRyZWUucXVlcnlDaXJjbGUobW91c2VQb3MsIGRpc3RhbmNlLCAocCkgPT4gdGhpcy5pc0VuYWJsZWQocCkpO1xuICAgICAgICAgICAgbGV0IGkgPSAwO1xuICAgICAgICAgICAgZm9yIChjb25zdCBwMSBvZiBxdWVyeSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHBvczEgPSBwMS5nZXRQb3NpdGlvbigpO1xuICAgICAgICAgICAgICAgIGZvciAoY29uc3QgcDIgb2YgcXVlcnkuc2xpY2UoaSArIDEpKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHBvczIgPSBwMi5nZXRQb3NpdGlvbigpLCBkaXN0TWF4ID0gTWF0aC5hYnMoY29udGFpbmVyLnJldGluYS5jb25uZWN0TW9kZURpc3RhbmNlKSwgeERpZmYgPSBNYXRoLmFicyhwb3MxLnggLSBwb3MyLngpLCB5RGlmZiA9IE1hdGguYWJzKHBvczEueSAtIHBvczIueSk7XG4gICAgICAgICAgICAgICAgICAgIGlmICh4RGlmZiA8IGRpc3RNYXggJiYgeURpZmYgPCBkaXN0TWF4KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBkcmF3Q29ubmVjdGlvbihjb250YWluZXIsIHAxLCBwMik7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgKytpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIGlzRW5hYmxlZChwYXJ0aWNsZSkge1xuICAgICAgICBjb25zdCBjb250YWluZXIgPSB0aGlzLmNvbnRhaW5lciwgbW91c2UgPSBjb250YWluZXIuaW50ZXJhY3Rpdml0eS5tb3VzZSwgZXZlbnRzID0gKHBhcnRpY2xlPy5pbnRlcmFjdGl2aXR5ID8/IGNvbnRhaW5lci5hY3R1YWxPcHRpb25zLmludGVyYWN0aXZpdHkpLmV2ZW50cztcbiAgICAgICAgaWYgKCEoZXZlbnRzLm9uSG92ZXIuZW5hYmxlICYmIG1vdXNlLnBvc2l0aW9uKSkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBpc0luQXJyYXkoXCJjb25uZWN0XCIsIGV2ZW50cy5vbkhvdmVyLm1vZGUpO1xuICAgIH1cbiAgICBsb2FkTW9kZU9wdGlvbnMob3B0aW9ucywgLi4uc291cmNlcykge1xuICAgICAgICBpZiAoIW9wdGlvbnMuY29ubmVjdCkge1xuICAgICAgICAgICAgb3B0aW9ucy5jb25uZWN0ID0gbmV3IENvbm5lY3QoKTtcbiAgICAgICAgfVxuICAgICAgICBmb3IgKGNvbnN0IHNvdXJjZSBvZiBzb3VyY2VzKSB7XG4gICAgICAgICAgICBvcHRpb25zLmNvbm5lY3QubG9hZChzb3VyY2U/LmNvbm5lY3QpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJlc2V0KCkge1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-external-connect/esm/Connector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-external-connect/esm/Options/Classes/Connect.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-external-connect/esm/Options/Classes/Connect.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Connect: () => (/* binding */ Connect)\n/* harmony export */ });\n/* harmony import */ var _ConnectLinks__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ConnectLinks */ \"(ssr)/./node_modules/tsparticles-interaction-external-connect/esm/Options/Classes/ConnectLinks.js\");\n\nclass Connect {\n    constructor() {\n        this.distance = 80;\n        this.links = new _ConnectLinks__WEBPACK_IMPORTED_MODULE_0__.ConnectLinks();\n        this.radius = 60;\n    }\n    get lineLinked() {\n        return this.links;\n    }\n    set lineLinked(value) {\n        this.links = value;\n    }\n    get line_linked() {\n        return this.links;\n    }\n    set line_linked(value) {\n        this.links = value;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n        this.links.load(data.links ?? data.lineLinked ?? data.line_linked);\n        if (data.radius !== undefined) {\n            this.radius = data.radius;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tZXh0ZXJuYWwtY29ubmVjdC9lc20vT3B0aW9ucy9DbGFzc2VzL0Nvbm5lY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEM7QUFDdkM7QUFDUDtBQUNBO0FBQ0EseUJBQXlCLHVEQUFZO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1dHVyaXN0aWMtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL3RzcGFydGljbGVzLWludGVyYWN0aW9uLWV4dGVybmFsLWNvbm5lY3QvZXNtL09wdGlvbnMvQ2xhc3Nlcy9Db25uZWN0LmpzP2EyMzciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ29ubmVjdExpbmtzIH0gZnJvbSBcIi4vQ29ubmVjdExpbmtzXCI7XG5leHBvcnQgY2xhc3MgQ29ubmVjdCB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHRoaXMuZGlzdGFuY2UgPSA4MDtcbiAgICAgICAgdGhpcy5saW5rcyA9IG5ldyBDb25uZWN0TGlua3MoKTtcbiAgICAgICAgdGhpcy5yYWRpdXMgPSA2MDtcbiAgICB9XG4gICAgZ2V0IGxpbmVMaW5rZWQoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmxpbmtzO1xuICAgIH1cbiAgICBzZXQgbGluZUxpbmtlZCh2YWx1ZSkge1xuICAgICAgICB0aGlzLmxpbmtzID0gdmFsdWU7XG4gICAgfVxuICAgIGdldCBsaW5lX2xpbmtlZCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMubGlua3M7XG4gICAgfVxuICAgIHNldCBsaW5lX2xpbmtlZCh2YWx1ZSkge1xuICAgICAgICB0aGlzLmxpbmtzID0gdmFsdWU7XG4gICAgfVxuICAgIGxvYWQoZGF0YSkge1xuICAgICAgICBpZiAoIWRhdGEpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5kaXN0YW5jZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aGlzLmRpc3RhbmNlID0gZGF0YS5kaXN0YW5jZTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmxpbmtzLmxvYWQoZGF0YS5saW5rcyA/PyBkYXRhLmxpbmVMaW5rZWQgPz8gZGF0YS5saW5lX2xpbmtlZCk7XG4gICAgICAgIGlmIChkYXRhLnJhZGl1cyAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aGlzLnJhZGl1cyA9IGRhdGEucmFkaXVzO1xuICAgICAgICB9XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-external-connect/esm/Options/Classes/Connect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-external-connect/esm/Options/Classes/ConnectLinks.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-external-connect/esm/Options/Classes/ConnectLinks.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectLinks: () => (/* binding */ ConnectLinks)\n/* harmony export */ });\nclass ConnectLinks {\n    constructor() {\n        this.opacity = 0.5;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tZXh0ZXJuYWwtY29ubmVjdC9lc20vT3B0aW9ucy9DbGFzc2VzL0Nvbm5lY3RMaW5rcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mdXR1cmlzdGljLXBvcnRmb2xpby8uL25vZGVfbW9kdWxlcy90c3BhcnRpY2xlcy1pbnRlcmFjdGlvbi1leHRlcm5hbC1jb25uZWN0L2VzbS9PcHRpb25zL0NsYXNzZXMvQ29ubmVjdExpbmtzLmpzP2I3MDIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNsYXNzIENvbm5lY3RMaW5rcyB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHRoaXMub3BhY2l0eSA9IDAuNTtcbiAgICB9XG4gICAgbG9hZChkYXRhKSB7XG4gICAgICAgIGlmICghZGF0YSkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGlmIChkYXRhLm9wYWNpdHkgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5vcGFjaXR5ID0gZGF0YS5vcGFjaXR5O1xuICAgICAgICB9XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-external-connect/esm/Options/Classes/ConnectLinks.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-external-connect/esm/Utils.js":
/*!****************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-external-connect/esm/Utils.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   drawConnectLine: () => (/* binding */ drawConnectLine),\n/* harmony export */   drawConnection: () => (/* binding */ drawConnection),\n/* harmony export */   gradient: () => (/* binding */ gradient),\n/* harmony export */   lineStyle: () => (/* binding */ lineStyle)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/ColorUtils.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/CanvasUtils.js\");\n\nfunction gradient(context, p1, p2, opacity) {\n    const gradStop = Math.floor(p2.getRadius() / p1.getRadius()), color1 = p1.getFillColor(), color2 = p2.getFillColor();\n    if (!color1 || !color2) {\n        return;\n    }\n    const sourcePos = p1.getPosition(), destPos = p2.getPosition(), midRgb = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.colorMix)(color1, color2, p1.getRadius(), p2.getRadius()), grad = context.createLinearGradient(sourcePos.x, sourcePos.y, destPos.x, destPos.y);\n    grad.addColorStop(0, (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getStyleFromHsl)(color1, opacity));\n    grad.addColorStop(gradStop > 1 ? 1 : gradStop, (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getStyleFromRgb)(midRgb, opacity));\n    grad.addColorStop(1, (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getStyleFromHsl)(color2, opacity));\n    return grad;\n}\nfunction drawConnectLine(context, width, lineStyle, begin, end) {\n    (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.drawLine)(context, begin, end);\n    context.lineWidth = width;\n    context.strokeStyle = lineStyle;\n    context.stroke();\n}\nfunction lineStyle(container, ctx, p1, p2) {\n    const options = container.actualOptions, connectOptions = options.interactivity.modes.connect;\n    if (!connectOptions) {\n        return;\n    }\n    return gradient(ctx, p1, p2, connectOptions.links.opacity);\n}\nfunction drawConnection(container, p1, p2) {\n    container.canvas.draw((ctx) => {\n        const ls = lineStyle(container, ctx, p1, p2);\n        if (!ls) {\n            return;\n        }\n        const pos1 = p1.getPosition(), pos2 = p2.getPosition();\n        drawConnectLine(ctx, p1.retina.linksWidth ?? 0, ls, pos1, pos2);\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-external-connect/esm/Utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-external-connect/esm/index.js":
/*!****************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-external-connect/esm/index.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Connect: () => (/* reexport safe */ _Options_Classes_Connect__WEBPACK_IMPORTED_MODULE_1__.Connect),\n/* harmony export */   ConnectLinks: () => (/* reexport safe */ _Options_Classes_ConnectLinks__WEBPACK_IMPORTED_MODULE_2__.ConnectLinks),\n/* harmony export */   loadExternalConnectInteraction: () => (/* binding */ loadExternalConnectInteraction)\n/* harmony export */ });\n/* harmony import */ var _Connector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Connector */ \"(ssr)/./node_modules/tsparticles-interaction-external-connect/esm/Connector.js\");\n/* harmony import */ var _Options_Classes_Connect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Options/Classes/Connect */ \"(ssr)/./node_modules/tsparticles-interaction-external-connect/esm/Options/Classes/Connect.js\");\n/* harmony import */ var _Options_Classes_ConnectLinks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Options/Classes/ConnectLinks */ \"(ssr)/./node_modules/tsparticles-interaction-external-connect/esm/Options/Classes/ConnectLinks.js\");\n\nasync function loadExternalConnectInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalConnect\", (container) => new _Connector__WEBPACK_IMPORTED_MODULE_0__.Connector(container), refresh);\n}\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tZXh0ZXJuYWwtY29ubmVjdC9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXdDO0FBQ2pDO0FBQ1AscUVBQXFFLGlEQUFTO0FBQzlFO0FBQzBDO0FBQ0s7QUFDRDtBQUNLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnV0dXJpc3RpYy1wb3J0Zm9saW8vLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tZXh0ZXJuYWwtY29ubmVjdC9lc20vaW5kZXguanM/Njc0ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDb25uZWN0b3IgfSBmcm9tIFwiLi9Db25uZWN0b3JcIjtcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBsb2FkRXh0ZXJuYWxDb25uZWN0SW50ZXJhY3Rpb24oZW5naW5lLCByZWZyZXNoID0gdHJ1ZSkge1xuICAgIGF3YWl0IGVuZ2luZS5hZGRJbnRlcmFjdG9yKFwiZXh0ZXJuYWxDb25uZWN0XCIsIChjb250YWluZXIpID0+IG5ldyBDb25uZWN0b3IoY29udGFpbmVyKSwgcmVmcmVzaCk7XG59XG5leHBvcnQgKiBmcm9tIFwiLi9PcHRpb25zL0NsYXNzZXMvQ29ubmVjdFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vT3B0aW9ucy9DbGFzc2VzL0Nvbm5lY3RMaW5rc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vT3B0aW9ucy9JbnRlcmZhY2VzL0lDb25uZWN0XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9PcHRpb25zL0ludGVyZmFjZXMvSUNvbm5lY3RMaW5rc1wiO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-external-connect/esm/index.js\n");

/***/ })

};
;