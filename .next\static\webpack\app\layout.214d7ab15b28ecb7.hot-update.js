"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"1b6538a4e659\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL2dsb2JhbHMuY3NzP2JhN2YiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxYjY1MzhhNGU2NTlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/CustomCursor.tsx":
/*!*************************************!*\
  !*** ./components/CustomCursor.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CustomCursor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction CustomCursor() {\n    _s();\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [isHovering, setIsHovering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const cursorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const trailRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const requestRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let mouseX = 0;\n        let mouseY = 0;\n        let cursorX = 0;\n        let cursorY = 0;\n        let trailX = 0;\n        let trailY = 0;\n        const updateMousePosition = (e)=>{\n            mouseX = e.clientX;\n            mouseY = e.clientY;\n        };\n        const animateCursor = ()=>{\n            // Smooth cursor movement with different speeds\n            cursorX += (mouseX - cursorX) * 0.3;\n            cursorY += (mouseY - cursorY) * 0.3;\n            trailX += (mouseX - trailX) * 0.1;\n            trailY += (mouseY - trailY) * 0.1;\n            if (cursorRef.current) {\n                cursorRef.current.style.transform = \"translate3d(\".concat(cursorX - 8, \"px, \").concat(cursorY - 8, \"px, 0) scale(\").concat(isHovering ? 1.5 : 1, \")\");\n            }\n            if (trailRef.current) {\n                trailRef.current.style.transform = \"translate3d(\".concat(trailX - 16, \"px, \").concat(trailY - 16, \"px, 0) scale(\").concat(isHovering ? 2 : 1, \")\");\n            }\n            requestRef.current = requestAnimationFrame(animateCursor);\n        };\n        const handleMouseEnter = ()=>setIsHovering(true);\n        const handleMouseLeave = ()=>setIsHovering(false);\n        // Add event listeners for interactive elements\n        const interactiveElements = document.querySelectorAll('a, button, [role=\"button\"], input, textarea');\n        interactiveElements.forEach((el)=>{\n            el.addEventListener(\"mouseenter\", handleMouseEnter);\n            el.addEventListener(\"mouseleave\", handleMouseLeave);\n        });\n        window.addEventListener(\"mousemove\", updateMousePosition);\n        requestRef.current = requestAnimationFrame(animateCursor);\n        return ()=>{\n            window.removeEventListener(\"mousemove\", updateMousePosition);\n            if (requestRef.current) {\n                cancelAnimationFrame(requestRef.current);\n            }\n            interactiveElements.forEach((el)=>{\n                el.removeEventListener(\"mouseenter\", handleMouseEnter);\n                el.removeEventListener(\"mouseleave\", handleMouseLeave);\n            });\n        };\n    }, [\n        isHovering\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: cursorRef,\n                className: \"fixed top-0 left-0 w-4 h-4 bg-neon-blue rounded-full pointer-events-none z-50 mix-blend-difference transition-transform duration-200 ease-out\",\n                style: {\n                    willChange: \"transform\"\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\CustomCursor.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: trailRef,\n                className: \"fixed top-0 left-0 w-8 h-8 border-2 border-neon-blue rounded-full pointer-events-none z-40 transition-transform duration-300 ease-out\",\n                style: {\n                    willChange: \"transform\"\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\CustomCursor.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(CustomCursor, \"FghlINSgb3ia9Hl5IK1vl1bCypA=\");\n_c = CustomCursor;\nvar _c;\n$RefreshReg$(_c, \"CustomCursor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/CustomCursor.tsx\n"));

/***/ })

});