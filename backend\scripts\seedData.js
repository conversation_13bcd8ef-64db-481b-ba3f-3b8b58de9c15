const mongoose = require('mongoose');
require('dotenv').config();

const User = require('../models/User');
const Project = require('../models/Project');
const Skill = require('../models/Skill');
const Testimonial = require('../models/Testimonial');
const BlogPost = require('../models/BlogPost');

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const seedData = async () => {
  try {
    console.log('🌱 Starting database seeding...');

    // Clear existing data
    await Promise.all([
      User.deleteMany({}),
      Project.deleteMany({}),
      Skill.deleteMany({}),
      Testimonial.deleteMany({}),
      BlogPost.deleteMany({})
    ]);

    console.log('🗑️  Cleared existing data');

    // Create admin user
    const adminUser = new User({
      email: process.env.ADMIN_EMAIL || '<EMAIL>',
      password: process.env.ADMIN_PASSWORD || 'admin123456',
      role: 'admin'
    });

    await adminUser.save();
    console.log('👤 Created admin user');

    // Create skills
    const skills = [
      // Frontend
      { name: 'HTML/CSS', level: 95, category: 'frontend', icon: '🌐', color: '#e34c26', order: 1 },
      { name: 'JavaScript', level: 90, category: 'frontend', icon: '⚡', color: '#f7df1e', order: 2 },
      { name: 'React', level: 85, category: 'frontend', icon: '⚛️', color: '#61dafb', order: 3 },
      { name: 'Next.js', level: 80, category: 'frontend', icon: '🔺', color: '#000000', order: 4 },
      { name: 'Tailwind CSS', level: 88, category: 'frontend', icon: '🎨', color: '#06b6d4', order: 5 },
      { name: 'TypeScript', level: 75, category: 'frontend', icon: '📘', color: '#3178c6', order: 6 },

      // Backend
      { name: 'Python', level: 88, category: 'backend', icon: '🐍', color: '#3776ab', order: 1 },
      { name: 'FastAPI', level: 80, category: 'backend', icon: '⚡', color: '#009688', order: 2 },
      { name: 'Django', level: 75, category: 'backend', icon: '🎯', color: '#092e20', order: 3 },
      { name: 'Node.js', level: 70, category: 'backend', icon: '🟢', color: '#339933', order: 4 },
      { name: 'PostgreSQL', level: 78, category: 'backend', icon: '🐘', color: '#336791', order: 5 },
      { name: 'MongoDB', level: 72, category: 'backend', icon: '🍃', color: '#47a248', order: 6 },

      // DevOps
      { name: 'Docker', level: 70, category: 'devops', icon: '🐳', color: '#2496ed', order: 1 },
      { name: 'Git', level: 85, category: 'devops', icon: '📚', color: '#f05032', order: 2 },
      { name: 'Linux', level: 75, category: 'devops', icon: '🐧', color: '#fcc624', order: 3 },
      { name: 'AWS', level: 65, category: 'devops', icon: '☁️', color: '#ff9900', order: 4 },

      // AI & Data
      { name: 'Machine Learning', level: 75, category: 'ai-data', icon: '🤖', color: '#ff6f00', order: 1 },
      { name: 'TensorFlow', level: 70, category: 'ai-data', icon: '🧠', color: '#ff6f00', order: 2 },
      { name: 'OpenAI API', level: 80, category: 'ai-data', icon: '🎭', color: '#412991', order: 3 },
      { name: 'Data Analysis', level: 72, category: 'ai-data', icon: '📊', color: '#e97627', order: 4 }
    ];

    await Skill.insertMany(skills);
    console.log('🎯 Created skills');

    // Create sample projects
    const projects = [
      {
        title: 'AI-Powered Chat Application',
        description: 'A real-time chat application with AI integration, built with React, Node.js, and OpenAI API. Features include smart responses, sentiment analysis, and modern UI.',
        shortDescription: 'Real-time chat app with AI integration and sentiment analysis.',
        technologies: ['React', 'Node.js', 'OpenAI', 'Socket.io', 'MongoDB'],
        liveUrl: 'https://example.com',
        githubUrl: 'https://github.com/yourusername/ai-chat',
        featured: true,
        category: 'ai',
        status: 'completed',
        isPublished: true,
        order: 1
      },
      {
        title: 'E-Commerce Platform',
        description: 'Full-stack e-commerce solution with payment integration, inventory management, and admin dashboard.',
        shortDescription: 'Complete e-commerce platform with payment integration.',
        technologies: ['Next.js', 'PostgreSQL', 'Stripe', 'Tailwind CSS'],
        liveUrl: 'https://example.com',
        githubUrl: 'https://github.com/yourusername/ecommerce',
        featured: true,
        category: 'web',
        status: 'completed',
        isPublished: true,
        order: 2
      },
      {
        title: 'Machine Learning Dashboard',
        description: 'Interactive dashboard for visualizing ML model performance with real-time data processing.',
        shortDescription: 'ML dashboard with real-time data visualization.',
        technologies: ['Python', 'FastAPI', 'React', 'D3.js', 'TensorFlow'],
        liveUrl: 'https://example.com',
        githubUrl: 'https://github.com/yourusername/ml-dashboard',
        featured: false,
        category: 'ai',
        status: 'completed',
        isPublished: true,
        order: 3
      }
    ];

    await Project.insertMany(projects);
    console.log('💼 Created projects');

    // Create sample testimonials
    const testimonials = [
      {
        name: 'Sarah Johnson',
        role: 'Product Manager',
        company: 'TechCorp Inc.',
        rating: 5,
        text: 'Working with this developer was an absolute pleasure. Their technical expertise and attention to detail resulted in a product that exceeded our expectations.',
        isFeatured: true,
        isPublished: true,
        order: 1
      },
      {
        name: 'Michael Chen',
        role: 'CTO',
        company: 'StartupXYZ',
        rating: 5,
        text: 'Exceptional work on our e-commerce platform. The performance optimizations and modern architecture have significantly improved our conversion rates.',
        isFeatured: true,
        isPublished: true,
        order: 2
      },
      {
        name: 'Emily Rodriguez',
        role: 'Founder',
        company: 'Digital Solutions',
        rating: 5,
        text: 'The machine learning dashboard they built for us has transformed how we analyze our data. A true professional who delivers quality work.',
        isFeatured: false,
        isPublished: true,
        order: 3
      }
    ];

    await Testimonial.insertMany(testimonials);
    console.log('💬 Created testimonials');

    // Create sample blog posts
    const blogPosts = [
      {
        title: 'Building AI-Powered Web Applications: A Complete Guide',
        slug: 'building-ai-powered-web-applications-complete-guide',
        excerpt: 'Learn how to integrate AI capabilities into your web applications using modern frameworks and APIs.',
        content: `# Building AI-Powered Web Applications

In this comprehensive guide, we'll explore how to integrate AI capabilities into your web applications...

## Getting Started

First, let's set up our development environment...

## Integrating OpenAI API

Here's how to integrate the OpenAI API into your React application...

## Best Practices

When building AI-powered applications, consider these best practices...`,
        category: 'ai',
        tags: ['ai', 'web-development', 'react', 'openai'],
        isFeatured: true,
        isPublished: true,
        publishedAt: new Date(),
        readTime: 8
      },
      {
        title: 'The Future of Web Development: Trends to Watch in 2024',
        slug: 'future-web-development-trends-2024',
        excerpt: 'Explore the latest trends and technologies shaping the future of web development.',
        content: `# The Future of Web Development

Web development is constantly evolving, and 2024 brings exciting new trends...

## AI Integration

Artificial Intelligence is becoming increasingly important...

## Performance Optimization

Modern web applications need to be fast...`,
        category: 'web-development',
        tags: ['web-development', 'trends', 'technology'],
        isFeatured: false,
        isPublished: true,
        publishedAt: new Date(),
        readTime: 6
      }
    ];

    await BlogPost.insertMany(blogPosts);
    console.log('📝 Created blog posts');

    console.log('✅ Database seeding completed successfully!');
    console.log(`👤 Admin login: ${adminUser.email}`);
    console.log(`🔑 Admin password: ${process.env.ADMIN_PASSWORD || 'admin123456'}`);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  }
};

seedData();
