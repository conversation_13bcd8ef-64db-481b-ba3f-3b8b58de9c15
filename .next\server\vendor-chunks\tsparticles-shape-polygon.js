"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tsparticles-shape-polygon";
exports.ids = ["vendor-chunks/tsparticles-shape-polygon"];
exports.modules = {

/***/ "(ssr)/./node_modules/tsparticles-shape-polygon/esm/PolygonDrawer.js":
/*!*********************************************************************!*\
  !*** ./node_modules/tsparticles-shape-polygon/esm/PolygonDrawer.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PolygonDrawer: () => (/* binding */ PolygonDrawer)\n/* harmony export */ });\n/* harmony import */ var _PolygonDrawerBase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./PolygonDrawerBase */ \"(ssr)/./node_modules/tsparticles-shape-polygon/esm/PolygonDrawerBase.js\");\n\nclass PolygonDrawer extends _PolygonDrawerBase__WEBPACK_IMPORTED_MODULE_0__.PolygonDrawerBase {\n    getCenter(particle, radius) {\n        return {\n            x: -radius / (particle.sides / 3.5),\n            y: -radius / (2.66 / 3.5),\n        };\n    }\n    getSidesData(particle, radius) {\n        const sides = particle.sides;\n        return {\n            count: {\n                denominator: 1,\n                numerator: sides,\n            },\n            length: (radius * 2.66) / (sides / 3),\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtc2hhcGUtcG9seWdvbi9lc20vUG9seWdvbkRyYXdlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3RDtBQUNqRCw0QkFBNEIsaUVBQWlCO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1dHVyaXN0aWMtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL3RzcGFydGljbGVzLXNoYXBlLXBvbHlnb24vZXNtL1BvbHlnb25EcmF3ZXIuanM/ZmQ1OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQb2x5Z29uRHJhd2VyQmFzZSB9IGZyb20gXCIuL1BvbHlnb25EcmF3ZXJCYXNlXCI7XG5leHBvcnQgY2xhc3MgUG9seWdvbkRyYXdlciBleHRlbmRzIFBvbHlnb25EcmF3ZXJCYXNlIHtcbiAgICBnZXRDZW50ZXIocGFydGljbGUsIHJhZGl1cykge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgeDogLXJhZGl1cyAvIChwYXJ0aWNsZS5zaWRlcyAvIDMuNSksXG4gICAgICAgICAgICB5OiAtcmFkaXVzIC8gKDIuNjYgLyAzLjUpLFxuICAgICAgICB9O1xuICAgIH1cbiAgICBnZXRTaWRlc0RhdGEocGFydGljbGUsIHJhZGl1cykge1xuICAgICAgICBjb25zdCBzaWRlcyA9IHBhcnRpY2xlLnNpZGVzO1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgY291bnQ6IHtcbiAgICAgICAgICAgICAgICBkZW5vbWluYXRvcjogMSxcbiAgICAgICAgICAgICAgICBudW1lcmF0b3I6IHNpZGVzLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGxlbmd0aDogKHJhZGl1cyAqIDIuNjYpIC8gKHNpZGVzIC8gMyksXG4gICAgICAgIH07XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-shape-polygon/esm/PolygonDrawer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-shape-polygon/esm/PolygonDrawerBase.js":
/*!*************************************************************************!*\
  !*** ./node_modules/tsparticles-shape-polygon/esm/PolygonDrawerBase.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PolygonDrawerBase: () => (/* binding */ PolygonDrawerBase)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n\nclass PolygonDrawerBase {\n    draw(context, particle, radius) {\n        const start = this.getCenter(particle, radius), side = this.getSidesData(particle, radius), sideCount = side.count.numerator * side.count.denominator, decimalSides = side.count.numerator / side.count.denominator, interiorAngleDegrees = (180 * (decimalSides - 2)) / decimalSides, interiorAngle = Math.PI - (Math.PI * interiorAngleDegrees) / 180;\n        if (!context) {\n            return;\n        }\n        context.beginPath();\n        context.translate(start.x, start.y);\n        context.moveTo(0, 0);\n        for (let i = 0; i < sideCount; i++) {\n            context.lineTo(side.length, 0);\n            context.translate(side.length, 0);\n            context.rotate(interiorAngle);\n        }\n    }\n    getSidesCount(particle) {\n        const polygon = particle.shapeData;\n        return Math.round((0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(polygon?.sides ?? polygon?.nb_sides ?? 5));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtc2hhcGUtcG9seWdvbi9lc20vUG9seWdvbkRyYXdlckJhc2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBbUQ7QUFDNUM7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGVBQWU7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsaUVBQWE7QUFDdkM7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1dHVyaXN0aWMtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL3RzcGFydGljbGVzLXNoYXBlLXBvbHlnb24vZXNtL1BvbHlnb25EcmF3ZXJCYXNlLmpzPzNhNzMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0UmFuZ2VWYWx1ZSB9IGZyb20gXCJ0c3BhcnRpY2xlcy1lbmdpbmVcIjtcbmV4cG9ydCBjbGFzcyBQb2x5Z29uRHJhd2VyQmFzZSB7XG4gICAgZHJhdyhjb250ZXh0LCBwYXJ0aWNsZSwgcmFkaXVzKSB7XG4gICAgICAgIGNvbnN0IHN0YXJ0ID0gdGhpcy5nZXRDZW50ZXIocGFydGljbGUsIHJhZGl1cyksIHNpZGUgPSB0aGlzLmdldFNpZGVzRGF0YShwYXJ0aWNsZSwgcmFkaXVzKSwgc2lkZUNvdW50ID0gc2lkZS5jb3VudC5udW1lcmF0b3IgKiBzaWRlLmNvdW50LmRlbm9taW5hdG9yLCBkZWNpbWFsU2lkZXMgPSBzaWRlLmNvdW50Lm51bWVyYXRvciAvIHNpZGUuY291bnQuZGVub21pbmF0b3IsIGludGVyaW9yQW5nbGVEZWdyZWVzID0gKDE4MCAqIChkZWNpbWFsU2lkZXMgLSAyKSkgLyBkZWNpbWFsU2lkZXMsIGludGVyaW9yQW5nbGUgPSBNYXRoLlBJIC0gKE1hdGguUEkgKiBpbnRlcmlvckFuZ2xlRGVncmVlcykgLyAxODA7XG4gICAgICAgIGlmICghY29udGV4dCkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGNvbnRleHQuYmVnaW5QYXRoKCk7XG4gICAgICAgIGNvbnRleHQudHJhbnNsYXRlKHN0YXJ0LngsIHN0YXJ0LnkpO1xuICAgICAgICBjb250ZXh0Lm1vdmVUbygwLCAwKTtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzaWRlQ291bnQ7IGkrKykge1xuICAgICAgICAgICAgY29udGV4dC5saW5lVG8oc2lkZS5sZW5ndGgsIDApO1xuICAgICAgICAgICAgY29udGV4dC50cmFuc2xhdGUoc2lkZS5sZW5ndGgsIDApO1xuICAgICAgICAgICAgY29udGV4dC5yb3RhdGUoaW50ZXJpb3JBbmdsZSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZ2V0U2lkZXNDb3VudChwYXJ0aWNsZSkge1xuICAgICAgICBjb25zdCBwb2x5Z29uID0gcGFydGljbGUuc2hhcGVEYXRhO1xuICAgICAgICByZXR1cm4gTWF0aC5yb3VuZChnZXRSYW5nZVZhbHVlKHBvbHlnb24/LnNpZGVzID8/IHBvbHlnb24/Lm5iX3NpZGVzID8/IDUpKTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-shape-polygon/esm/PolygonDrawerBase.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-shape-polygon/esm/TriangleDrawer.js":
/*!**********************************************************************!*\
  !*** ./node_modules/tsparticles-shape-polygon/esm/TriangleDrawer.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TriangleDrawer: () => (/* binding */ TriangleDrawer)\n/* harmony export */ });\n/* harmony import */ var _PolygonDrawerBase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./PolygonDrawerBase */ \"(ssr)/./node_modules/tsparticles-shape-polygon/esm/PolygonDrawerBase.js\");\n\nclass TriangleDrawer extends _PolygonDrawerBase__WEBPACK_IMPORTED_MODULE_0__.PolygonDrawerBase {\n    getCenter(particle, radius) {\n        return {\n            x: -radius,\n            y: radius / 1.66,\n        };\n    }\n    getSidesCount() {\n        return 3;\n    }\n    getSidesData(particle, radius) {\n        return {\n            count: {\n                denominator: 2,\n                numerator: 3,\n            },\n            length: radius * 2,\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtc2hhcGUtcG9seWdvbi9lc20vVHJpYW5nbGVEcmF3ZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBd0Q7QUFDakQsNkJBQTZCLGlFQUFpQjtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnV0dXJpc3RpYy1wb3J0Zm9saW8vLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtc2hhcGUtcG9seWdvbi9lc20vVHJpYW5nbGVEcmF3ZXIuanM/YWI5MCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQb2x5Z29uRHJhd2VyQmFzZSB9IGZyb20gXCIuL1BvbHlnb25EcmF3ZXJCYXNlXCI7XG5leHBvcnQgY2xhc3MgVHJpYW5nbGVEcmF3ZXIgZXh0ZW5kcyBQb2x5Z29uRHJhd2VyQmFzZSB7XG4gICAgZ2V0Q2VudGVyKHBhcnRpY2xlLCByYWRpdXMpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHg6IC1yYWRpdXMsXG4gICAgICAgICAgICB5OiByYWRpdXMgLyAxLjY2LFxuICAgICAgICB9O1xuICAgIH1cbiAgICBnZXRTaWRlc0NvdW50KCkge1xuICAgICAgICByZXR1cm4gMztcbiAgICB9XG4gICAgZ2V0U2lkZXNEYXRhKHBhcnRpY2xlLCByYWRpdXMpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGNvdW50OiB7XG4gICAgICAgICAgICAgICAgZGVub21pbmF0b3I6IDIsXG4gICAgICAgICAgICAgICAgbnVtZXJhdG9yOiAzLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGxlbmd0aDogcmFkaXVzICogMixcbiAgICAgICAgfTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-shape-polygon/esm/TriangleDrawer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-shape-polygon/esm/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/tsparticles-shape-polygon/esm/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadGenericPolygonShape: () => (/* binding */ loadGenericPolygonShape),\n/* harmony export */   loadPolygonShape: () => (/* binding */ loadPolygonShape),\n/* harmony export */   loadTriangleShape: () => (/* binding */ loadTriangleShape)\n/* harmony export */ });\n/* harmony import */ var _PolygonDrawer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./PolygonDrawer */ \"(ssr)/./node_modules/tsparticles-shape-polygon/esm/PolygonDrawer.js\");\n/* harmony import */ var _TriangleDrawer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TriangleDrawer */ \"(ssr)/./node_modules/tsparticles-shape-polygon/esm/TriangleDrawer.js\");\n\n\nasync function loadGenericPolygonShape(engine, refresh = true) {\n    await engine.addShape(\"polygon\", new _PolygonDrawer__WEBPACK_IMPORTED_MODULE_0__.PolygonDrawer(), refresh);\n}\nasync function loadTriangleShape(engine, refresh = true) {\n    await engine.addShape(\"triangle\", new _TriangleDrawer__WEBPACK_IMPORTED_MODULE_1__.TriangleDrawer(), refresh);\n}\nasync function loadPolygonShape(engine, refresh = true) {\n    await loadGenericPolygonShape(engine, refresh);\n    await loadTriangleShape(engine, refresh);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtc2hhcGUtcG9seWdvbi9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBZ0Q7QUFDRTtBQUMzQztBQUNQLHlDQUF5Qyx5REFBYTtBQUN0RDtBQUNPO0FBQ1AsMENBQTBDLDJEQUFjO0FBQ3hEO0FBQ087QUFDUDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mdXR1cmlzdGljLXBvcnRmb2xpby8uL25vZGVfbW9kdWxlcy90c3BhcnRpY2xlcy1zaGFwZS1wb2x5Z29uL2VzbS9pbmRleC5qcz82MTg0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBvbHlnb25EcmF3ZXIgfSBmcm9tIFwiLi9Qb2x5Z29uRHJhd2VyXCI7XG5pbXBvcnQgeyBUcmlhbmdsZURyYXdlciB9IGZyb20gXCIuL1RyaWFuZ2xlRHJhd2VyXCI7XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gbG9hZEdlbmVyaWNQb2x5Z29uU2hhcGUoZW5naW5lLCByZWZyZXNoID0gdHJ1ZSkge1xuICAgIGF3YWl0IGVuZ2luZS5hZGRTaGFwZShcInBvbHlnb25cIiwgbmV3IFBvbHlnb25EcmF3ZXIoKSwgcmVmcmVzaCk7XG59XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gbG9hZFRyaWFuZ2xlU2hhcGUoZW5naW5lLCByZWZyZXNoID0gdHJ1ZSkge1xuICAgIGF3YWl0IGVuZ2luZS5hZGRTaGFwZShcInRyaWFuZ2xlXCIsIG5ldyBUcmlhbmdsZURyYXdlcigpLCByZWZyZXNoKTtcbn1cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBsb2FkUG9seWdvblNoYXBlKGVuZ2luZSwgcmVmcmVzaCA9IHRydWUpIHtcbiAgICBhd2FpdCBsb2FkR2VuZXJpY1BvbHlnb25TaGFwZShlbmdpbmUsIHJlZnJlc2gpO1xuICAgIGF3YWl0IGxvYWRUcmlhbmdsZVNoYXBlKGVuZ2luZSwgcmVmcmVzaCk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-shape-polygon/esm/index.js\n");

/***/ })

};
;