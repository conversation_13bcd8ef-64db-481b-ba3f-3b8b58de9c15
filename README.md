"# 🚀 Futuristic Portfolio Website

A cutting-edge personal portfolio built with Next.js, Three.js, and modern web technologies featuring AI-powered animations, 3D effects, and a cyberpunk aesthetic.

## ✨ Features

- **🎨 Futuristic Design**: Cyberpunk-inspired UI with neon colors and glitch effects
- **🌟 Smooth Animations**: Powered by Framer Motion for buttery-smooth interactions
- **🎯 3D Effects**: Three.js particle systems and interactive backgrounds
- **📱 Fully Responsive**: Mobile-first design that looks great on all devices
- **🌙 Dark/Light Mode**: Toggle between themes with smooth transitions
- **⚡ Performance Optimized**: Built with Next.js for optimal loading speeds
- **🔍 SEO Friendly**: Meta tags, OpenGraph, and sitemap included
- **📧 Working Contact Form**: Integrated with EmailJS for message handling
- **🎭 Custom Cursor**: Animated cursor with hover effects
- **📊 Interactive Skills**: Animated progress bars and rotating tech icons
- **💼 Project Showcase**: Dynamic project cards with hover animations
- **📝 Blog Section**: Ready for content with modern card layouts
- **🎪 Testimonials**: Carousel slider with auto-play functionality
- **🔄 Smooth Scrolling**: Seamless navigation between sections

## 🛠️ Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS with custom animations
- **Animations**: Framer Motion, CSS animations
- **3D Graphics**: Three.js, React Three Fiber
- **Particles**: React Particles with TSParticles
- **Icons**: Lucide React
- **Forms**: React Hook Form with validation
- **Notifications**: React Hot Toast
- **Email**: EmailJS integration
- **Deployment**: Vercel/GitHub Pages ready

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ installed
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/futuristic-portfolio.git
   cd futuristic-portfolio
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```

   Edit `.env.local` with your information:
   ```env
   NEXT_PUBLIC_EMAILJS_SERVICE_ID=your_service_id
   NEXT_PUBLIC_EMAILJS_TEMPLATE_ID=your_template_id
   NEXT_PUBLIC_EMAILJS_PUBLIC_KEY=your_public_key
   NEXT_PUBLIC_SITE_URL=https://yourname.dev
   NEXT_PUBLIC_EMAIL=<EMAIL>
   ```

4. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📝 Customization Guide

### Personal Information

1. **Update personal details** in the following files:
   - `components/Hero.tsx` - Name and tagline
   - `components/About.tsx` - About me content
   - `components/Contact.tsx` - Contact information
   - `app/layout.tsx` - Meta tags and SEO

2. **Replace placeholder content**:
   - Update project data in `components/Projects.tsx`
   - Modify skills in `components/Skills.tsx` and `components/About.tsx`
   - Add your blog posts in `components/Blog.tsx`
   - Update testimonials in `components/Testimonials.tsx`

3. **Add your assets**:
   - Replace favicon files in `/public`
   - Add your resume PDF as `/public/resume.pdf`
   - Add project images to `/public/projects`

### Styling Customization

1. **Colors**: Edit `tailwind.config.js` to change the color scheme
2. **Fonts**: Update font imports in `app/layout.tsx`
3. **Animations**: Modify animation variants in component files
4. **Layout**: Adjust spacing and sizing in component files

### Contact Form Setup

1. **Create EmailJS account** at [emailjs.com](https://emailjs.com)
2. **Set up email service** (Gmail, Outlook, etc.)
3. **Create email template** with variables: `{{name}}`, `{{email}}`, `{{subject}}`, `{{message}}`
4. **Update environment variables** with your EmailJS credentials

## 🚀 Deployment

### GitHub Pages

1. **Update `next.config.js`** for static export:
   ```javascript
   const nextConfig = {
     output: 'export',
     trailingSlash: true,
     images: { unoptimized: true }
   }
   ```

2. **Build and export**:
   ```bash
   npm run build
   npm run export
   ```

3. **Deploy to GitHub Pages**:
   - Push to GitHub repository
   - Enable GitHub Pages in repository settings
   - Set source to GitHub Actions
   - Use the included workflow file

### Vercel (Recommended)

1. **Connect repository** to Vercel
2. **Set environment variables** in Vercel dashboard
3. **Deploy automatically** on every push

### Custom Domain

1. **Add CNAME record** pointing to your hosting provider
2. **Update environment variables** with your domain
3. **Configure SSL certificate** (automatic with Vercel/GitHub Pages)

## 📁 Project Structure

```
portfolio_website/
├── app/                    # Next.js app directory
│   ├── layout.tsx         # Root layout with metadata
│   ├── page.tsx           # Homepage
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── Hero.tsx           # Hero section with animations
│   ├── About.tsx          # About me section
│   ├── Projects.tsx       # Projects showcase
│   ├── Skills.tsx         # Skills section
│   ├── Resume.tsx         # Resume section
│   ├── Contact.tsx        # Contact form
│   ├── Blog.tsx           # Blog section
│   ├── Testimonials.tsx   # Testimonials carousel
│   ├── Navbar.tsx         # Navigation bar
│   ├── Footer.tsx         # Footer component
│   └── ...               # Other components
├── public/               # Static assets
│   ├── favicon.ico       # Favicon
│   ├── manifest.json     # PWA manifest
│   └── robots.txt        # SEO robots file
├── .env.local           # Environment variables
├── next.config.js       # Next.js configuration
├── tailwind.config.js   # Tailwind CSS configuration
└── package.json         # Dependencies and scripts
```

## 🎨 Design System

### Colors
- **Primary**: Neon Blue (`#00d4ff`)
- **Secondary**: Neon Green (`#14b8a6`)
- **Accent**: Neon Pink (`#ff10f0`)
- **Background**: Black (`#000000`)
- **Text**: White/Gray variants

### Typography
- **Headings**: Orbitron (Cyber font)
- **Body**: Rajdhani (Tech font)
- **Code**: Monospace

### Animations
- **Entrance**: Fade in with slide up
- **Hover**: Scale and glow effects
- **Scroll**: Intersection observer triggers
- **Loading**: Custom loading animations

## 🔧 Advanced Features

### Custom Cursor
- Animated cursor that follows mouse movement
- Different states for interactive elements
- Smooth transitions and effects

### Particle System
- Three.js powered background particles
- Interactive particles that respond to mouse
- Configurable colors and behavior

### Glitch Effects
- CSS-based glitch animations
- Text distortion effects
- Cyberpunk aesthetic elements

### Performance Optimizations
- Code splitting and lazy loading
- Image optimization
- Minimal bundle size
- Fast loading times

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🙏 Acknowledgments

- **Next.js** - React framework
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Animation library
- **Three.js** - 3D graphics library
- **Lucide** - Icon library
- **EmailJS** - Email service

## 📞 Support

If you have any questions or need help customizing the portfolio:

- 📧 Email: <EMAIL>
- 💬 GitHub Issues: [Create an issue](https://github.com/yourusername/futuristic-portfolio/issues)
- 🐦 Twitter: [@yourusername](https://twitter.com/yourusername)

---

## 🔧 Backend & Admin Panel Setup

### Backend API Setup

1. **Navigate to backend directory**
   ```bash
   cd backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```

   Edit `.env` with your configuration:
   ```env
   MONGODB_URI=mongodb://localhost:27017/portfolio
   JWT_SECRET=your-super-secret-jwt-key
   ADMIN_EMAIL=<EMAIL>
   ADMIN_PASSWORD=your-secure-password
   EMAIL_HOST=smtp.gmail.com
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-app-password
   ```

4. **Start MongoDB** (if using local MongoDB)
   ```bash
   mongod
   ```

5. **Seed the database**
   ```bash
   npm run seed
   ```

6. **Start the backend server**
   ```bash
   npm run dev
   ```

   Backend will run on `http://localhost:5000`

### Admin Panel Setup

1. **Navigate to admin directory**
   ```bash
   cd admin
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the admin panel**
   ```bash
   npm run dev
   ```

   Admin panel will run on `http://localhost:3001`

4. **Access admin panel**
   - URL: `http://localhost:3001`
   - Email: `<EMAIL>` (or your configured email)
   - Password: Your configured password

### 🎛️ Admin Panel Features

#### **Dashboard**
- Overview statistics (projects, blog posts, contacts, etc.)
- Recent activity feed
- Analytics charts
- System information

#### **Projects Management**
- ✅ Create, edit, delete projects
- ✅ Toggle featured/published status
- ✅ Manage project images and technologies
- ✅ Track project views and likes
- ✅ Organize by categories

#### **Blog Management**
- ✅ Create, edit, delete blog posts
- ✅ Rich text editor with markdown support
- ✅ SEO optimization fields
- ✅ Tag management
- ✅ Featured posts
- ✅ Draft/published status

#### **Testimonials Management**
- ✅ Add, edit, delete testimonials
- ✅ Star ratings
- ✅ Featured testimonials
- ✅ Client information management

#### **Skills Management**
- ✅ Add, edit, delete skills
- ✅ Skill level management (0-100%)
- ✅ Category organization
- ✅ Icon and color customization
- ✅ Drag & drop reordering

#### **Contact Messages**
- ✅ View all contact form submissions
- ✅ Mark as read/replied/completed
- ✅ Priority levels (low, medium, high, urgent)
- ✅ Spam detection and management
- ✅ Response tracking
- ✅ Export functionality

#### **Analytics & Reports**
- ✅ Visitor analytics
- ✅ Content performance metrics
- ✅ Contact form statistics
- ✅ Monthly/weekly reports

#### **Security Features**
- ✅ JWT-based authentication
- ✅ Account lockout after failed attempts
- ✅ Action logging
- ✅ Rate limiting
- ✅ Input validation and sanitization

### 🔄 Frontend Integration

Update your frontend to use the backend API:

1. **Install axios in your main project**
   ```bash
   npm install axios
   ```

2. **Create API service** (`lib/api.js`):
   ```javascript
   import axios from 'axios'

   const API_URL = 'http://localhost:5000/api'

   export const api = axios.create({
     baseURL: API_URL,
   })

   export const getProjects = () => api.get('/projects')
   export const getBlogPosts = () => api.get('/blog')
   export const getTestimonials = () => api.get('/testimonials')
   export const getSkills = () => api.get('/skills')
   export const submitContact = (data) => api.post('/contact', data)
   ```

3. **Update your components** to fetch data from API instead of static data

### 🚀 Production Deployment

#### **Backend Deployment (Railway/Heroku/DigitalOcean)**

1. **Set environment variables** in your hosting platform
2. **Deploy backend** with MongoDB Atlas connection
3. **Update CORS settings** for your domain

#### **Admin Panel Deployment**

1. **Build admin panel**
   ```bash
   cd admin
   npm run build
   ```

2. **Deploy to Vercel/Netlify** with environment variables:
   ```env
   NEXT_PUBLIC_API_URL=https://your-backend-url.com/api
   ```

#### **Database Setup (MongoDB Atlas)**

1. Create MongoDB Atlas cluster
2. Get connection string
3. Update `MONGODB_URI` in backend environment

### 📊 API Endpoints

#### **Public Endpoints**
- `GET /api/projects` - Get published projects
- `GET /api/blog` - Get published blog posts
- `GET /api/testimonials` - Get published testimonials
- `GET /api/skills` - Get visible skills
- `POST /api/contact` - Submit contact form

#### **Admin Endpoints** (Require Authentication)
- `POST /api/auth/login` - Admin login
- `GET /api/admin/dashboard` - Dashboard stats
- `GET /api/projects/admin/all` - All projects
- `POST /api/projects` - Create project
- `PUT /api/projects/:id` - Update project
- `DELETE /api/projects/:id` - Delete project
- Similar CRUD endpoints for blog, testimonials, skills, contacts

### 🔐 Security Best Practices

1. **Change default admin credentials** immediately
2. **Use strong JWT secret** (generate with crypto.randomBytes)
3. **Enable HTTPS** in production
4. **Set up proper CORS** for your domain only
5. **Regular database backups**
6. **Monitor admin access logs**

### 🛠️ Customization

#### **Adding New Content Types**
1. Create Mongoose model in `backend/models/`
2. Create routes in `backend/routes/`
3. Add API functions in admin panel
4. Create admin interface components

#### **Modifying Admin UI**
- Admin panel uses Tailwind CSS
- Components are in `admin/components/`
- Easy to customize colors and layout

### 📝 Environment Variables Reference

#### **Backend (.env)**
```env
# Database
MONGODB_URI=mongodb://localhost:27017/portfolio

# Authentication
JWT_SECRET=your-super-secret-jwt-key
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your-secure-password

# Email
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Server
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# File Upload (Optional)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
```

#### **Admin Panel (.env.local)**
```env
NEXT_PUBLIC_API_URL=http://localhost:5000/api
```

---

**Made with ❤️ and ☕ by [Your Name](https://yourname.dev)**"
