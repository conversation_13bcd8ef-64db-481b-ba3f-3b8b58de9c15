"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tsparticles-move-base";
exports.ids = ["vendor-chunks/tsparticles-move-base"];
exports.modules = {

/***/ "(ssr)/./node_modules/tsparticles-move-base/esm/BaseMover.js":
/*!*************************************************************!*\
  !*** ./node_modules/tsparticles-move-base/esm/BaseMover.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseMover: () => (/* binding */ BaseMover)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/tsparticles-move-base/esm/Utils.js\");\n\n\nconst diffFactor = 2;\nclass BaseMover {\n    constructor() {\n        this._initSpin = (particle) => {\n            const container = particle.container, options = particle.options, spinOptions = options.move.spin;\n            if (!spinOptions.enable) {\n                return;\n            }\n            const spinPos = spinOptions.position ?? { x: 50, y: 50 }, spinCenter = {\n                x: (spinPos.x / 100) * container.canvas.size.width,\n                y: (spinPos.y / 100) * container.canvas.size.height,\n            }, pos = particle.getPosition(), distance = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getDistance)(pos, spinCenter), spinAcceleration = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(spinOptions.acceleration);\n            particle.retina.spinAcceleration = spinAcceleration * container.retina.pixelRatio;\n            particle.spin = {\n                center: spinCenter,\n                direction: particle.velocity.x >= 0 ? \"clockwise\" : \"counter-clockwise\",\n                angle: particle.velocity.angle,\n                radius: distance,\n                acceleration: particle.retina.spinAcceleration,\n            };\n        };\n    }\n    init(particle) {\n        const options = particle.options, gravityOptions = options.move.gravity;\n        particle.gravity = {\n            enable: gravityOptions.enable,\n            acceleration: (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(gravityOptions.acceleration),\n            inverse: gravityOptions.inverse,\n        };\n        this._initSpin(particle);\n    }\n    isEnabled(particle) {\n        return !particle.destroyed && particle.options.move.enable;\n    }\n    move(particle, delta) {\n        const particleOptions = particle.options, moveOptions = particleOptions.move;\n        if (!moveOptions.enable) {\n            return;\n        }\n        const container = particle.container, pxRatio = container.retina.pixelRatio, slowFactor = (0,_Utils__WEBPACK_IMPORTED_MODULE_1__.getProximitySpeedFactor)(particle), baseSpeed = (particle.retina.moveSpeed ??= (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(moveOptions.speed) * pxRatio) *\n            container.retina.reduceFactor, moveDrift = (particle.retina.moveDrift ??= (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(particle.options.move.drift) * pxRatio), maxSize = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeMax)(particleOptions.size.value) * pxRatio, sizeFactor = moveOptions.size ? particle.getRadius() / maxSize : 1, moveSpeed = (baseSpeed * sizeFactor * slowFactor * (delta.factor || 1)) / diffFactor, maxSpeed = particle.retina.maxSpeed ?? container.retina.maxSpeed;\n        if (moveOptions.spin.enable) {\n            (0,_Utils__WEBPACK_IMPORTED_MODULE_1__.spin)(particle, moveSpeed);\n        }\n        else {\n            (0,_Utils__WEBPACK_IMPORTED_MODULE_1__.move)(particle, moveOptions, moveSpeed, maxSpeed, moveDrift, delta);\n        }\n        (0,_Utils__WEBPACK_IMPORTED_MODULE_1__.applyDistance)(particle);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-move-base/esm/BaseMover.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-move-base/esm/Utils.js":
/*!*********************************************************!*\
  !*** ./node_modules/tsparticles-move-base/esm/Utils.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyDistance: () => (/* binding */ applyDistance),\n/* harmony export */   applyPath: () => (/* binding */ applyPath),\n/* harmony export */   getProximitySpeedFactor: () => (/* binding */ getProximitySpeedFactor),\n/* harmony export */   move: () => (/* binding */ move),\n/* harmony export */   spin: () => (/* binding */ spin)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n\nfunction applyDistance(particle) {\n    const initialPosition = particle.initialPosition, { dx, dy } = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getDistances)(initialPosition, particle.position), dxFixed = Math.abs(dx), dyFixed = Math.abs(dy), { maxDistance } = particle.retina, hDistance = maxDistance.horizontal, vDistance = maxDistance.vertical;\n    if (!hDistance && !vDistance) {\n        return;\n    }\n    if (((hDistance && dxFixed >= hDistance) || (vDistance && dyFixed >= vDistance)) && !particle.misplaced) {\n        particle.misplaced = (!!hDistance && dxFixed > hDistance) || (!!vDistance && dyFixed > vDistance);\n        if (hDistance) {\n            particle.velocity.x = particle.velocity.y / 2 - particle.velocity.x;\n        }\n        if (vDistance) {\n            particle.velocity.y = particle.velocity.x / 2 - particle.velocity.y;\n        }\n    }\n    else if ((!hDistance || dxFixed < hDistance) && (!vDistance || dyFixed < vDistance) && particle.misplaced) {\n        particle.misplaced = false;\n    }\n    else if (particle.misplaced) {\n        const pos = particle.position, vel = particle.velocity;\n        if (hDistance && ((pos.x < initialPosition.x && vel.x < 0) || (pos.x > initialPosition.x && vel.x > 0))) {\n            vel.x *= -(0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)();\n        }\n        if (vDistance && ((pos.y < initialPosition.y && vel.y < 0) || (pos.y > initialPosition.y && vel.y > 0))) {\n            vel.y *= -(0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)();\n        }\n    }\n}\nfunction move(particle, moveOptions, moveSpeed, maxSpeed, moveDrift, delta) {\n    applyPath(particle, delta);\n    const gravityOptions = particle.gravity, gravityFactor = gravityOptions?.enable && gravityOptions.inverse ? -1 : 1;\n    if (moveDrift && moveSpeed) {\n        particle.velocity.x += (moveDrift * delta.factor) / (60 * moveSpeed);\n    }\n    if (gravityOptions?.enable && moveSpeed) {\n        particle.velocity.y += (gravityFactor * (gravityOptions.acceleration * delta.factor)) / (60 * moveSpeed);\n    }\n    const decay = particle.moveDecay;\n    particle.velocity.multTo(decay);\n    const velocity = particle.velocity.mult(moveSpeed);\n    if (gravityOptions?.enable &&\n        maxSpeed > 0 &&\n        ((!gravityOptions.inverse && velocity.y >= 0 && velocity.y >= maxSpeed) ||\n            (gravityOptions.inverse && velocity.y <= 0 && velocity.y <= -maxSpeed))) {\n        velocity.y = gravityFactor * maxSpeed;\n        if (moveSpeed) {\n            particle.velocity.y = velocity.y / moveSpeed;\n        }\n    }\n    const zIndexOptions = particle.options.zIndex, zVelocityFactor = (1 - particle.zIndexFactor) ** zIndexOptions.velocityRate;\n    velocity.multTo(zVelocityFactor);\n    const { position } = particle;\n    position.addTo(velocity);\n    if (moveOptions.vibrate) {\n        position.x += Math.sin(position.x * Math.cos(position.y));\n        position.y += Math.cos(position.y * Math.sin(position.x));\n    }\n}\nfunction spin(particle, moveSpeed) {\n    const container = particle.container;\n    if (!particle.spin) {\n        return;\n    }\n    const updateFunc = {\n        x: particle.spin.direction === \"clockwise\" ? Math.cos : Math.sin,\n        y: particle.spin.direction === \"clockwise\" ? Math.sin : Math.cos,\n    };\n    particle.position.x = particle.spin.center.x + particle.spin.radius * updateFunc.x(particle.spin.angle);\n    particle.position.y = particle.spin.center.y + particle.spin.radius * updateFunc.y(particle.spin.angle);\n    particle.spin.radius += particle.spin.acceleration;\n    const maxCanvasSize = Math.max(container.canvas.size.width, container.canvas.size.height);\n    if (particle.spin.radius > maxCanvasSize / 2) {\n        particle.spin.radius = maxCanvasSize / 2;\n        particle.spin.acceleration *= -1;\n    }\n    else if (particle.spin.radius < 0) {\n        particle.spin.radius = 0;\n        particle.spin.acceleration *= -1;\n    }\n    particle.spin.angle += (moveSpeed / 100) * (1 - particle.spin.radius / maxCanvasSize);\n}\nfunction applyPath(particle, delta) {\n    const particlesOptions = particle.options, pathOptions = particlesOptions.move.path, pathEnabled = pathOptions.enable;\n    if (!pathEnabled) {\n        return;\n    }\n    if (particle.lastPathTime <= particle.pathDelay) {\n        particle.lastPathTime += delta.value;\n        return;\n    }\n    const path = particle.pathGenerator?.generate(particle, delta);\n    if (path) {\n        particle.velocity.addTo(path);\n    }\n    if (pathOptions.clamp) {\n        particle.velocity.x = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.clamp)(particle.velocity.x, -1, 1);\n        particle.velocity.y = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.clamp)(particle.velocity.y, -1, 1);\n    }\n    particle.lastPathTime -= particle.pathDelay;\n}\nfunction getProximitySpeedFactor(particle) {\n    return particle.slow.inRange ? particle.slow.factor : 1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtbW92ZS1iYXNlL2VzbS9VdGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBcUU7QUFDOUQ7QUFDUCx3REFBd0QsU0FBUyxFQUFFLGdFQUFZLHdGQUF3RixjQUFjO0FBQ3JMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiw2REFBUztBQUMvQjtBQUNBO0FBQ0Esc0JBQXNCLDZEQUFTO0FBQy9CO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLFdBQVc7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIseURBQUs7QUFDbkMsOEJBQThCLHlEQUFLO0FBQ25DO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1dHVyaXN0aWMtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL3RzcGFydGljbGVzLW1vdmUtYmFzZS9lc20vVXRpbHMuanM/NWNlYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbGFtcCwgZ2V0RGlzdGFuY2VzLCBnZXRSYW5kb20sIH0gZnJvbSBcInRzcGFydGljbGVzLWVuZ2luZVwiO1xuZXhwb3J0IGZ1bmN0aW9uIGFwcGx5RGlzdGFuY2UocGFydGljbGUpIHtcbiAgICBjb25zdCBpbml0aWFsUG9zaXRpb24gPSBwYXJ0aWNsZS5pbml0aWFsUG9zaXRpb24sIHsgZHgsIGR5IH0gPSBnZXREaXN0YW5jZXMoaW5pdGlhbFBvc2l0aW9uLCBwYXJ0aWNsZS5wb3NpdGlvbiksIGR4Rml4ZWQgPSBNYXRoLmFicyhkeCksIGR5Rml4ZWQgPSBNYXRoLmFicyhkeSksIHsgbWF4RGlzdGFuY2UgfSA9IHBhcnRpY2xlLnJldGluYSwgaERpc3RhbmNlID0gbWF4RGlzdGFuY2UuaG9yaXpvbnRhbCwgdkRpc3RhbmNlID0gbWF4RGlzdGFuY2UudmVydGljYWw7XG4gICAgaWYgKCFoRGlzdGFuY2UgJiYgIXZEaXN0YW5jZSkge1xuICAgICAgICByZXR1cm47XG4gICAgfVxuICAgIGlmICgoKGhEaXN0YW5jZSAmJiBkeEZpeGVkID49IGhEaXN0YW5jZSkgfHwgKHZEaXN0YW5jZSAmJiBkeUZpeGVkID49IHZEaXN0YW5jZSkpICYmICFwYXJ0aWNsZS5taXNwbGFjZWQpIHtcbiAgICAgICAgcGFydGljbGUubWlzcGxhY2VkID0gKCEhaERpc3RhbmNlICYmIGR4Rml4ZWQgPiBoRGlzdGFuY2UpIHx8ICghIXZEaXN0YW5jZSAmJiBkeUZpeGVkID4gdkRpc3RhbmNlKTtcbiAgICAgICAgaWYgKGhEaXN0YW5jZSkge1xuICAgICAgICAgICAgcGFydGljbGUudmVsb2NpdHkueCA9IHBhcnRpY2xlLnZlbG9jaXR5LnkgLyAyIC0gcGFydGljbGUudmVsb2NpdHkueDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodkRpc3RhbmNlKSB7XG4gICAgICAgICAgICBwYXJ0aWNsZS52ZWxvY2l0eS55ID0gcGFydGljbGUudmVsb2NpdHkueCAvIDIgLSBwYXJ0aWNsZS52ZWxvY2l0eS55O1xuICAgICAgICB9XG4gICAgfVxuICAgIGVsc2UgaWYgKCghaERpc3RhbmNlIHx8IGR4Rml4ZWQgPCBoRGlzdGFuY2UpICYmICghdkRpc3RhbmNlIHx8IGR5Rml4ZWQgPCB2RGlzdGFuY2UpICYmIHBhcnRpY2xlLm1pc3BsYWNlZCkge1xuICAgICAgICBwYXJ0aWNsZS5taXNwbGFjZWQgPSBmYWxzZTtcbiAgICB9XG4gICAgZWxzZSBpZiAocGFydGljbGUubWlzcGxhY2VkKSB7XG4gICAgICAgIGNvbnN0IHBvcyA9IHBhcnRpY2xlLnBvc2l0aW9uLCB2ZWwgPSBwYXJ0aWNsZS52ZWxvY2l0eTtcbiAgICAgICAgaWYgKGhEaXN0YW5jZSAmJiAoKHBvcy54IDwgaW5pdGlhbFBvc2l0aW9uLnggJiYgdmVsLnggPCAwKSB8fCAocG9zLnggPiBpbml0aWFsUG9zaXRpb24ueCAmJiB2ZWwueCA+IDApKSkge1xuICAgICAgICAgICAgdmVsLnggKj0gLWdldFJhbmRvbSgpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh2RGlzdGFuY2UgJiYgKChwb3MueSA8IGluaXRpYWxQb3NpdGlvbi55ICYmIHZlbC55IDwgMCkgfHwgKHBvcy55ID4gaW5pdGlhbFBvc2l0aW9uLnkgJiYgdmVsLnkgPiAwKSkpIHtcbiAgICAgICAgICAgIHZlbC55ICo9IC1nZXRSYW5kb20oKTtcbiAgICAgICAgfVxuICAgIH1cbn1cbmV4cG9ydCBmdW5jdGlvbiBtb3ZlKHBhcnRpY2xlLCBtb3ZlT3B0aW9ucywgbW92ZVNwZWVkLCBtYXhTcGVlZCwgbW92ZURyaWZ0LCBkZWx0YSkge1xuICAgIGFwcGx5UGF0aChwYXJ0aWNsZSwgZGVsdGEpO1xuICAgIGNvbnN0IGdyYXZpdHlPcHRpb25zID0gcGFydGljbGUuZ3Jhdml0eSwgZ3Jhdml0eUZhY3RvciA9IGdyYXZpdHlPcHRpb25zPy5lbmFibGUgJiYgZ3Jhdml0eU9wdGlvbnMuaW52ZXJzZSA/IC0xIDogMTtcbiAgICBpZiAobW92ZURyaWZ0ICYmIG1vdmVTcGVlZCkge1xuICAgICAgICBwYXJ0aWNsZS52ZWxvY2l0eS54ICs9IChtb3ZlRHJpZnQgKiBkZWx0YS5mYWN0b3IpIC8gKDYwICogbW92ZVNwZWVkKTtcbiAgICB9XG4gICAgaWYgKGdyYXZpdHlPcHRpb25zPy5lbmFibGUgJiYgbW92ZVNwZWVkKSB7XG4gICAgICAgIHBhcnRpY2xlLnZlbG9jaXR5LnkgKz0gKGdyYXZpdHlGYWN0b3IgKiAoZ3Jhdml0eU9wdGlvbnMuYWNjZWxlcmF0aW9uICogZGVsdGEuZmFjdG9yKSkgLyAoNjAgKiBtb3ZlU3BlZWQpO1xuICAgIH1cbiAgICBjb25zdCBkZWNheSA9IHBhcnRpY2xlLm1vdmVEZWNheTtcbiAgICBwYXJ0aWNsZS52ZWxvY2l0eS5tdWx0VG8oZGVjYXkpO1xuICAgIGNvbnN0IHZlbG9jaXR5ID0gcGFydGljbGUudmVsb2NpdHkubXVsdChtb3ZlU3BlZWQpO1xuICAgIGlmIChncmF2aXR5T3B0aW9ucz8uZW5hYmxlICYmXG4gICAgICAgIG1heFNwZWVkID4gMCAmJlxuICAgICAgICAoKCFncmF2aXR5T3B0aW9ucy5pbnZlcnNlICYmIHZlbG9jaXR5LnkgPj0gMCAmJiB2ZWxvY2l0eS55ID49IG1heFNwZWVkKSB8fFxuICAgICAgICAgICAgKGdyYXZpdHlPcHRpb25zLmludmVyc2UgJiYgdmVsb2NpdHkueSA8PSAwICYmIHZlbG9jaXR5LnkgPD0gLW1heFNwZWVkKSkpIHtcbiAgICAgICAgdmVsb2NpdHkueSA9IGdyYXZpdHlGYWN0b3IgKiBtYXhTcGVlZDtcbiAgICAgICAgaWYgKG1vdmVTcGVlZCkge1xuICAgICAgICAgICAgcGFydGljbGUudmVsb2NpdHkueSA9IHZlbG9jaXR5LnkgLyBtb3ZlU3BlZWQ7XG4gICAgICAgIH1cbiAgICB9XG4gICAgY29uc3QgekluZGV4T3B0aW9ucyA9IHBhcnRpY2xlLm9wdGlvbnMuekluZGV4LCB6VmVsb2NpdHlGYWN0b3IgPSAoMSAtIHBhcnRpY2xlLnpJbmRleEZhY3RvcikgKiogekluZGV4T3B0aW9ucy52ZWxvY2l0eVJhdGU7XG4gICAgdmVsb2NpdHkubXVsdFRvKHpWZWxvY2l0eUZhY3Rvcik7XG4gICAgY29uc3QgeyBwb3NpdGlvbiB9ID0gcGFydGljbGU7XG4gICAgcG9zaXRpb24uYWRkVG8odmVsb2NpdHkpO1xuICAgIGlmIChtb3ZlT3B0aW9ucy52aWJyYXRlKSB7XG4gICAgICAgIHBvc2l0aW9uLnggKz0gTWF0aC5zaW4ocG9zaXRpb24ueCAqIE1hdGguY29zKHBvc2l0aW9uLnkpKTtcbiAgICAgICAgcG9zaXRpb24ueSArPSBNYXRoLmNvcyhwb3NpdGlvbi55ICogTWF0aC5zaW4ocG9zaXRpb24ueCkpO1xuICAgIH1cbn1cbmV4cG9ydCBmdW5jdGlvbiBzcGluKHBhcnRpY2xlLCBtb3ZlU3BlZWQpIHtcbiAgICBjb25zdCBjb250YWluZXIgPSBwYXJ0aWNsZS5jb250YWluZXI7XG4gICAgaWYgKCFwYXJ0aWNsZS5zcGluKSB7XG4gICAgICAgIHJldHVybjtcbiAgICB9XG4gICAgY29uc3QgdXBkYXRlRnVuYyA9IHtcbiAgICAgICAgeDogcGFydGljbGUuc3Bpbi5kaXJlY3Rpb24gPT09IFwiY2xvY2t3aXNlXCIgPyBNYXRoLmNvcyA6IE1hdGguc2luLFxuICAgICAgICB5OiBwYXJ0aWNsZS5zcGluLmRpcmVjdGlvbiA9PT0gXCJjbG9ja3dpc2VcIiA/IE1hdGguc2luIDogTWF0aC5jb3MsXG4gICAgfTtcbiAgICBwYXJ0aWNsZS5wb3NpdGlvbi54ID0gcGFydGljbGUuc3Bpbi5jZW50ZXIueCArIHBhcnRpY2xlLnNwaW4ucmFkaXVzICogdXBkYXRlRnVuYy54KHBhcnRpY2xlLnNwaW4uYW5nbGUpO1xuICAgIHBhcnRpY2xlLnBvc2l0aW9uLnkgPSBwYXJ0aWNsZS5zcGluLmNlbnRlci55ICsgcGFydGljbGUuc3Bpbi5yYWRpdXMgKiB1cGRhdGVGdW5jLnkocGFydGljbGUuc3Bpbi5hbmdsZSk7XG4gICAgcGFydGljbGUuc3Bpbi5yYWRpdXMgKz0gcGFydGljbGUuc3Bpbi5hY2NlbGVyYXRpb247XG4gICAgY29uc3QgbWF4Q2FudmFzU2l6ZSA9IE1hdGgubWF4KGNvbnRhaW5lci5jYW52YXMuc2l6ZS53aWR0aCwgY29udGFpbmVyLmNhbnZhcy5zaXplLmhlaWdodCk7XG4gICAgaWYgKHBhcnRpY2xlLnNwaW4ucmFkaXVzID4gbWF4Q2FudmFzU2l6ZSAvIDIpIHtcbiAgICAgICAgcGFydGljbGUuc3Bpbi5yYWRpdXMgPSBtYXhDYW52YXNTaXplIC8gMjtcbiAgICAgICAgcGFydGljbGUuc3Bpbi5hY2NlbGVyYXRpb24gKj0gLTE7XG4gICAgfVxuICAgIGVsc2UgaWYgKHBhcnRpY2xlLnNwaW4ucmFkaXVzIDwgMCkge1xuICAgICAgICBwYXJ0aWNsZS5zcGluLnJhZGl1cyA9IDA7XG4gICAgICAgIHBhcnRpY2xlLnNwaW4uYWNjZWxlcmF0aW9uICo9IC0xO1xuICAgIH1cbiAgICBwYXJ0aWNsZS5zcGluLmFuZ2xlICs9IChtb3ZlU3BlZWQgLyAxMDApICogKDEgLSBwYXJ0aWNsZS5zcGluLnJhZGl1cyAvIG1heENhbnZhc1NpemUpO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGFwcGx5UGF0aChwYXJ0aWNsZSwgZGVsdGEpIHtcbiAgICBjb25zdCBwYXJ0aWNsZXNPcHRpb25zID0gcGFydGljbGUub3B0aW9ucywgcGF0aE9wdGlvbnMgPSBwYXJ0aWNsZXNPcHRpb25zLm1vdmUucGF0aCwgcGF0aEVuYWJsZWQgPSBwYXRoT3B0aW9ucy5lbmFibGU7XG4gICAgaWYgKCFwYXRoRW5hYmxlZCkge1xuICAgICAgICByZXR1cm47XG4gICAgfVxuICAgIGlmIChwYXJ0aWNsZS5sYXN0UGF0aFRpbWUgPD0gcGFydGljbGUucGF0aERlbGF5KSB7XG4gICAgICAgIHBhcnRpY2xlLmxhc3RQYXRoVGltZSArPSBkZWx0YS52YWx1ZTtcbiAgICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBjb25zdCBwYXRoID0gcGFydGljbGUucGF0aEdlbmVyYXRvcj8uZ2VuZXJhdGUocGFydGljbGUsIGRlbHRhKTtcbiAgICBpZiAocGF0aCkge1xuICAgICAgICBwYXJ0aWNsZS52ZWxvY2l0eS5hZGRUbyhwYXRoKTtcbiAgICB9XG4gICAgaWYgKHBhdGhPcHRpb25zLmNsYW1wKSB7XG4gICAgICAgIHBhcnRpY2xlLnZlbG9jaXR5LnggPSBjbGFtcChwYXJ0aWNsZS52ZWxvY2l0eS54LCAtMSwgMSk7XG4gICAgICAgIHBhcnRpY2xlLnZlbG9jaXR5LnkgPSBjbGFtcChwYXJ0aWNsZS52ZWxvY2l0eS55LCAtMSwgMSk7XG4gICAgfVxuICAgIHBhcnRpY2xlLmxhc3RQYXRoVGltZSAtPSBwYXJ0aWNsZS5wYXRoRGVsYXk7XG59XG5leHBvcnQgZnVuY3Rpb24gZ2V0UHJveGltaXR5U3BlZWRGYWN0b3IocGFydGljbGUpIHtcbiAgICByZXR1cm4gcGFydGljbGUuc2xvdy5pblJhbmdlID8gcGFydGljbGUuc2xvdy5mYWN0b3IgOiAxO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-move-base/esm/Utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-move-base/esm/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/tsparticles-move-base/esm/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadBaseMover: () => (/* binding */ loadBaseMover)\n/* harmony export */ });\n/* harmony import */ var _BaseMover__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BaseMover */ \"(ssr)/./node_modules/tsparticles-move-base/esm/BaseMover.js\");\n\nasync function loadBaseMover(engine, refresh = true) {\n    await engine.addMover(\"base\", () => new _BaseMover__WEBPACK_IMPORTED_MODULE_0__.BaseMover(), refresh);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtbW92ZS1iYXNlL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3QztBQUNqQztBQUNQLDRDQUE0QyxpREFBUztBQUNyRCIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1dHVyaXN0aWMtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL3RzcGFydGljbGVzLW1vdmUtYmFzZS9lc20vaW5kZXguanM/YzdlYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBCYXNlTW92ZXIgfSBmcm9tIFwiLi9CYXNlTW92ZXJcIjtcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBsb2FkQmFzZU1vdmVyKGVuZ2luZSwgcmVmcmVzaCA9IHRydWUpIHtcbiAgICBhd2FpdCBlbmdpbmUuYWRkTW92ZXIoXCJiYXNlXCIsICgpID0+IG5ldyBCYXNlTW92ZXIoKSwgcmVmcmVzaCk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-move-base/esm/index.js\n");

/***/ })

};
;