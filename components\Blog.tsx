'use client'

import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { Calendar, Clock, ArrowRight, Tag } from 'lucide-react'

const blogPosts = [
  {
    id: 1,
    title: 'Building AI-Powered Web Applications: A Complete Guide',
    excerpt: 'Learn how to integrate AI capabilities into your web applications using modern frameworks and APIs. This comprehensive guide covers everything from setup to deployment.',
    content: 'Full blog post content would go here...',
    author: 'Your Name',
    date: '2024-01-15',
    readTime: '8 min read',
    tags: ['AI', 'Web Development', 'React', 'OpenAI'],
    image: '/api/placeholder/600/300',
    featured: true,
  },
  {
    id: 2,
    title: 'The Future of Web Development: Trends to Watch in 2024',
    excerpt: 'Explore the latest trends and technologies shaping the future of web development. From AI integration to new frameworks, here\'s what you need to know.',
    content: 'Full blog post content would go here...',
    author: 'Your Name',
    date: '2024-01-10',
    readTime: '6 min read',
    tags: ['Web Development', 'Trends', 'Technology'],
    image: '/api/placeholder/600/300',
    featured: false,
  },
  {
    id: 3,
    title: 'My Journey as a Self-Taught Developer: Lessons Learned',
    excerpt: 'Sharing my experience and insights from learning to code independently. Tips, challenges, and advice for aspiring self-taught developers.',
    content: 'Full blog post content would go here...',
    author: 'Your Name',
    date: '2024-01-05',
    readTime: '10 min read',
    tags: ['Career', 'Learning', 'Personal'],
    image: '/api/placeholder/600/300',
    featured: false,
  },
  {
    id: 4,
    title: 'Optimizing React Applications for Better Performance',
    excerpt: 'Practical tips and techniques to improve your React application\'s performance. Learn about code splitting, memoization, and other optimization strategies.',
    content: 'Full blog post content would go here...',
    author: 'Your Name',
    date: '2023-12-28',
    readTime: '7 min read',
    tags: ['React', 'Performance', 'Optimization'],
    image: '/api/placeholder/600/300',
    featured: false,
  },
]

const tagColors: { [key: string]: string } = {
  'AI': 'bg-purple-500/20 text-purple-400 border-purple-500/30',
  'Web Development': 'bg-blue-500/20 text-blue-400 border-blue-500/30',
  'React': 'bg-cyan-500/20 text-cyan-400 border-cyan-500/30',
  'OpenAI': 'bg-green-500/20 text-green-400 border-green-500/30',
  'Trends': 'bg-orange-500/20 text-orange-400 border-orange-500/30',
  'Technology': 'bg-red-500/20 text-red-400 border-red-500/30',
  'Career': 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
  'Learning': 'bg-pink-500/20 text-pink-400 border-pink-500/30',
  'Personal': 'bg-indigo-500/20 text-indigo-400 border-indigo-500/30',
  'Performance': 'bg-teal-500/20 text-teal-400 border-teal-500/30',
  'Optimization': 'bg-emerald-500/20 text-emerald-400 border-emerald-500/30',
}

export default function Blog() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" ref={ref}>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        className="text-center mb-16"
      >
        <motion.h2
          variants={itemVariants}
          className="text-4xl md:text-6xl font-cyber font-bold text-neon-blue neon-glow-subtle mb-6"
        >
          Blog & Insights
        </motion.h2>
        <motion.div
          variants={itemVariants}
          className="w-24 h-1 bg-gradient-to-r from-neon-blue to-neon-green mx-auto mb-8"
        />
        <motion.p
          variants={itemVariants}
          className="text-xl text-gray-400 max-w-3xl mx-auto"
        >
          Sharing my learning journey, technical insights, and thoughts on the future of technology.
        </motion.p>
      </motion.div>

      {/* Featured Post */}
      {blogPosts.find(post => post.featured) && (
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="mb-16"
        >
          {(() => {
            const featuredPost = blogPosts.find(post => post.featured)!
            return (
              <div className="bg-gray-900/50 backdrop-blur-sm border border-neon-blue/30 rounded-lg overflow-hidden hover:border-neon-blue/50 transition-all duration-300 group">
                <div className="md:flex">
                  <div className="md:w-1/2">
                    <div className="h-64 md:h-full bg-gradient-to-br from-neon-blue/20 to-neon-green/20 flex items-center justify-center relative">
                      <div className="absolute top-4 left-4 bg-neon-blue text-black px-3 py-1 rounded-full text-sm font-tech font-semibold">
                        Featured
                      </div>
                      <div className="text-6xl">📝</div>
                    </div>
                  </div>
                  <div className="md:w-1/2 p-8">
                    <div className="flex items-center text-sm text-gray-400 mb-4">
                      <Calendar size={16} className="mr-2" />
                      {formatDate(featuredPost.date)}
                      <Clock size={16} className="ml-4 mr-2" />
                      {featuredPost.readTime}
                    </div>
                    
                    <h3 className="text-2xl font-tech font-semibold text-white mb-4 group-hover:text-neon-blue transition-colors">
                      {featuredPost.title}
                    </h3>
                    
                    <p className="text-gray-400 mb-6 leading-relaxed">
                      {featuredPost.excerpt}
                    </p>
                    
                    <div className="flex flex-wrap gap-2 mb-6">
                      {featuredPost.tags.map((tag) => (
                        <span
                          key={tag}
                          className={`px-3 py-1 text-xs font-tech font-medium rounded-full border ${
                            tagColors[tag] || 'bg-gray-500/20 text-gray-400 border-gray-500/30'
                          }`}
                        >
                          <Tag size={12} className="inline mr-1" />
                          {tag}
                        </span>
                      ))}
                    </div>
                    
                    <motion.button
                      className="flex items-center text-neon-blue hover:text-neon-green transition-colors font-tech font-medium"
                      whileHover={{ x: 5 }}
                    >
                      Read More
                      <ArrowRight size={16} className="ml-2" />
                    </motion.button>
                  </div>
                </div>
              </div>
            )
          })()}
        </motion.div>
      )}

      {/* Blog Posts Grid */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
      >
        {blogPosts.filter(post => !post.featured).map((post) => (
          <motion.article
            key={post.id}
            variants={itemVariants}
            className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg overflow-hidden hover:border-neon-blue/50 transition-all duration-300 group"
            whileHover={{ y: -5 }}
          >
            {/* Post Image */}
            <div className="h-48 bg-gradient-to-br from-neon-blue/20 to-neon-green/20 flex items-center justify-center">
              <div className="text-4xl">📄</div>
            </div>

            {/* Post Content */}
            <div className="p-6">
              <div className="flex items-center text-sm text-gray-400 mb-3">
                <Calendar size={14} className="mr-2" />
                {formatDate(post.date)}
                <Clock size={14} className="ml-4 mr-2" />
                {post.readTime}
              </div>
              
              <h3 className="text-lg font-tech font-semibold text-white mb-3 group-hover:text-neon-blue transition-colors line-clamp-2">
                {post.title}
              </h3>
              
              <p className="text-gray-400 mb-4 text-sm leading-relaxed line-clamp-3">
                {post.excerpt}
              </p>
              
              <div className="flex flex-wrap gap-2 mb-4">
                {post.tags.slice(0, 2).map((tag) => (
                  <span
                    key={tag}
                    className={`px-2 py-1 text-xs font-tech font-medium rounded-full border ${
                      tagColors[tag] || 'bg-gray-500/20 text-gray-400 border-gray-500/30'
                    }`}
                  >
                    {tag}
                  </span>
                ))}
                {post.tags.length > 2 && (
                  <span className="px-2 py-1 text-xs font-tech font-medium rounded-full border bg-gray-500/20 text-gray-400 border-gray-500/30">
                    +{post.tags.length - 2}
                  </span>
                )}
              </div>
              
              <motion.button
                className="flex items-center text-neon-blue hover:text-neon-green transition-colors font-tech font-medium text-sm"
                whileHover={{ x: 3 }}
              >
                Read Article
                <ArrowRight size={14} className="ml-2" />
              </motion.button>
            </div>
          </motion.article>
        ))}
      </motion.div>

      {/* View All Posts Button */}
      <motion.div
        variants={itemVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        className="text-center mt-12"
      >
        <motion.button
          className="btn-cyber group flex items-center gap-3 mx-auto"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          View All Posts
          <ArrowRight size={20} />
        </motion.button>
      </motion.div>
    </div>
  )
}
