"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/Testimonials.tsx":
/*!*************************************!*\
  !*** ./components/Testimonials.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Testimonials; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-intersection-observer */ \"(app-pages-browser)/./node_modules/react-intersection-observer/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Quote,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Quote,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Quote,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Quote,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst testimonials = [\n    {\n        id: 1,\n        name: \"Sarah Johnson\",\n        role: \"Product Manager\",\n        company: \"TechCorp Inc.\",\n        image: \"/api/placeholder/80/80\",\n        rating: 5,\n        text: \"Working with this developer was an absolute pleasure. Their technical expertise and attention to detail resulted in a product that exceeded our expectations. The AI integration was seamless and the user experience is fantastic.\"\n    },\n    {\n        id: 2,\n        name: \"Michael Chen\",\n        role: \"CTO\",\n        company: \"StartupXYZ\",\n        image: \"/api/placeholder/80/80\",\n        rating: 5,\n        text: \"Exceptional work on our e-commerce platform. The performance optimizations and modern architecture have significantly improved our conversion rates. Highly recommend for any complex web development project.\"\n    },\n    {\n        id: 3,\n        name: \"Emily Rodriguez\",\n        role: \"Founder\",\n        company: \"Digital Solutions\",\n        image: \"/api/placeholder/80/80\",\n        rating: 5,\n        text: \"The machine learning dashboard they built for us has transformed how we analyze our data. The visualizations are beautiful and the insights are actionable. A true professional who delivers quality work.\"\n    },\n    {\n        id: 4,\n        name: \"David Thompson\",\n        role: \"Lead Developer\",\n        company: \"Innovation Labs\",\n        image: \"/api/placeholder/80/80\",\n        rating: 5,\n        text: \"Great collaboration on our blockchain project. Their understanding of both frontend and backend technologies, combined with their problem-solving skills, made them an invaluable team member.\"\n    },\n    {\n        id: 5,\n        name: \"Lisa Wang\",\n        role: \"Marketing Director\",\n        company: \"Growth Agency\",\n        image: \"/api/placeholder/80/80\",\n        rating: 5,\n        text: \"The social media analytics platform they developed has been a game-changer for our agency. The automated reporting and sentiment analysis features have saved us countless hours while providing deeper insights.\"\n    }\n];\nfunction Testimonials() {\n    _s();\n    const [ref, inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_2__.useInView)({\n        triggerOnce: true,\n        threshold: 0.1\n    });\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAutoPlaying, setIsAutoPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Auto-play functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAutoPlaying) return;\n        const interval = setInterval(()=>{\n            setCurrentIndex((prevIndex)=>prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1);\n        }, 5000);\n        return ()=>clearInterval(interval);\n    }, [\n        isAutoPlaying\n    ]);\n    const goToPrevious = ()=>{\n        setIsAutoPlaying(false);\n        setCurrentIndex(currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1);\n    };\n    const goToNext = ()=>{\n        setIsAutoPlaying(false);\n        setCurrentIndex(currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1);\n    };\n    const goToSlide = (index)=>{\n        setIsAutoPlaying(false);\n        setCurrentIndex(index);\n    };\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6\n            }\n        }\n    };\n    const renderStars = (rating)=>{\n        return Array.from({\n            length: 5\n        }, (_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                size: 16,\n                className: \"\".concat(index < rating ? \"text-yellow-400 fill-current\" : \"text-gray-600\")\n            }, index, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        ref: ref,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                variants: containerVariants,\n                initial: \"hidden\",\n                animate: inView ? \"visible\" : \"hidden\",\n                className: \"text-center mb-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h2, {\n                        variants: itemVariants,\n                        className: \"text-4xl md:text-6xl font-cyber font-bold text-neon-blue neon-glow-subtle mb-6\",\n                        children: \"Testimonials\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        variants: itemVariants,\n                        className: \"w-24 h-1 bg-gradient-to-r from-neon-blue to-neon-green mx-auto mb-8\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                        variants: itemVariants,\n                        className: \"text-xl text-gray-400 max-w-3xl mx-auto\",\n                        children: \"What clients and collaborators say about working with me.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                variants: containerVariants,\n                initial: \"hidden\",\n                animate: inView ? \"visible\" : \"hidden\",\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        variants: itemVariants,\n                        className: \"relative bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg p-8 md:p-12 mb-8 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-4 right-4 opacity-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    size: 80,\n                                    className: \"text-neon-blue\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center mb-6\",\n                                        children: renderStars(testimonials[currentIndex].rating)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.blockquote, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            y: -20\n                                        },\n                                        transition: {\n                                            duration: 0.5\n                                        },\n                                        className: \"text-lg md:text-xl text-gray-300 text-center leading-relaxed mb-8 max-w-4xl mx-auto\",\n                                        children: [\n                                            '\"',\n                                            testimonials[currentIndex].text,\n                                            '\"'\n                                        ]\n                                    }, currentIndex, true, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.8\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.2\n                                        },\n                                        className: \"flex flex-col items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gradient-to-br from-neon-blue/30 to-neon-green/30 rounded-full flex items-center justify-center mb-4 border-2 border-neon-blue/50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"\\uD83D\\uDC64\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-tech font-semibold text-white mb-1\",\n                                                children: testimonials[currentIndex].name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-neon-blue font-medium mb-1\",\n                                                children: testimonials[currentIndex].role\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: testimonials[currentIndex].company\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, \"author-\".concat(currentIndex), true, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        variants: itemVariants,\n                        className: \"flex items-center justify-center space-x-4 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                onClick: goToPrevious,\n                                className: \"p-3 bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-full text-gray-400 hover:text-neon-blue hover:border-neon-blue/50 transition-all duration-300\",\n                                whileHover: {\n                                    scale: 1.1\n                                },\n                                whileTap: {\n                                    scale: 0.9\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: testimonials.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>goToSlide(index),\n                                        className: \"w-3 h-3 rounded-full transition-all duration-300 \".concat(index === currentIndex ? \"bg-neon-blue scale-125\" : \"bg-gray-600 hover:bg-gray-500\")\n                                    }, index, false, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                onClick: goToNext,\n                                className: \"p-3 bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-full text-gray-400 hover:text-neon-blue hover:border-neon-blue/50 transition-all duration-300\",\n                                whileHover: {\n                                    scale: 1.1\n                                },\n                                whileTap: {\n                                    scale: 0.9\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        variants: itemVariants,\n                        className: \"flex justify-center space-x-4 overflow-x-auto pb-4\",\n                        children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                onClick: ()=>goToSlide(index),\n                                className: \"flex-shrink-0 p-3 rounded-lg border transition-all duration-300 \".concat(index === currentIndex ? \"border-neon-blue bg-neon-blue/10\" : \"border-gray-700 bg-gray-900/50 hover:border-gray-600\"),\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-neon-blue/30 to-neon-green/30 rounded-full flex items-center justify-center mb-2 mx-auto border border-neon-blue/50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: \"\\uD83D\\uDC64\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-tech font-medium text-white\",\n                                            children: testimonial.name.split(\" \")[0]\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: testimonial.company\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this)\n                            }, testimonial.id, false, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        variants: itemVariants,\n                        className: \"text-center mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsAutoPlaying(!isAutoPlaying),\n                            className: \"px-4 py-2 rounded-lg font-tech text-sm transition-all duration-300 \".concat(isAutoPlaying ? \"bg-neon-blue/20 text-neon-blue border border-neon-blue/50\" : \"bg-gray-900/50 text-gray-400 border border-gray-700 hover:border-gray-600\"),\n                            children: isAutoPlaying ? \"Pause Auto-play\" : \"Resume Auto-play\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Testimonials.tsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, this);\n}\n_s(Testimonials, \"/rR5YY0ajnU+Z68pMypFnM6XoWY=\", false, function() {\n    return [\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_2__.useInView\n    ];\n});\n_c = Testimonials;\nvar _c;\n$RefreshReg$(_c, \"Testimonials\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Testimonials.tsx\n"));

/***/ })

});