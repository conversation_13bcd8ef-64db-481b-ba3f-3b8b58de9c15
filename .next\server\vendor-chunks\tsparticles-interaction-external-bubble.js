"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tsparticles-interaction-external-bubble";
exports.ids = ["vendor-chunks/tsparticles-interaction-external-bubble"];
exports.modules = {

/***/ "(ssr)/./node_modules/tsparticles-interaction-external-bubble/esm/Bubbler.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-external-bubble/esm/Bubbler.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bubbler: () => (/* binding */ Bubbler)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/ExternalInteractorBase.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/Constants.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/Utils.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/ColorUtils.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/Circle.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/Rectangle.js\");\n/* harmony import */ var _Options_Classes_Bubble__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Options/Classes/Bubble */ \"(ssr)/./node_modules/tsparticles-interaction-external-bubble/esm/Options/Classes/Bubble.js\");\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/tsparticles-interaction-external-bubble/esm/Utils.js\");\n\n\n\nclass Bubbler extends tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n        this._clickBubble = () => {\n            const container = this.container, options = container.actualOptions, mouseClickPos = container.interactivity.mouse.clickPosition, bubbleOptions = options.interactivity.modes.bubble;\n            if (!bubbleOptions || !mouseClickPos) {\n                return;\n            }\n            if (!container.bubble) {\n                container.bubble = {};\n            }\n            const distance = container.retina.bubbleModeDistance;\n            if (!distance || distance < 0) {\n                return;\n            }\n            const query = container.particles.quadTree.queryCircle(mouseClickPos, distance, (p) => this.isEnabled(p)), { bubble } = container;\n            for (const particle of query) {\n                if (!bubble.clicking) {\n                    continue;\n                }\n                particle.bubble.inRange = !bubble.durationEnd;\n                const pos = particle.getPosition(), distMouse = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getDistance)(pos, mouseClickPos), timeSpent = (new Date().getTime() - (container.interactivity.mouse.clickTime || 0)) / 1000;\n                if (timeSpent > bubbleOptions.duration) {\n                    bubble.durationEnd = true;\n                }\n                if (timeSpent > bubbleOptions.duration * 2) {\n                    bubble.clicking = false;\n                    bubble.durationEnd = false;\n                }\n                const sizeData = {\n                    bubbleObj: {\n                        optValue: container.retina.bubbleModeSize,\n                        value: particle.bubble.radius,\n                    },\n                    particlesObj: {\n                        optValue: (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getRangeMax)(particle.options.size.value) * container.retina.pixelRatio,\n                        value: particle.size.value,\n                    },\n                    type: \"size\",\n                };\n                this._process(particle, distMouse, timeSpent, sizeData);\n                const opacityData = {\n                    bubbleObj: {\n                        optValue: bubbleOptions.opacity,\n                        value: particle.bubble.opacity,\n                    },\n                    particlesObj: {\n                        optValue: (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getRangeMax)(particle.options.opacity.value),\n                        value: particle.opacity?.value ?? 1,\n                    },\n                    type: \"opacity\",\n                };\n                this._process(particle, distMouse, timeSpent, opacityData);\n                if (!bubble.durationEnd && distMouse <= distance) {\n                    this._hoverBubbleColor(particle, distMouse);\n                }\n                else {\n                    delete particle.bubble.color;\n                }\n            }\n        };\n        this._hoverBubble = () => {\n            const container = this.container, mousePos = container.interactivity.mouse.position, distance = container.retina.bubbleModeDistance;\n            if (!distance || distance < 0 || mousePos === undefined) {\n                return;\n            }\n            const query = container.particles.quadTree.queryCircle(mousePos, distance, (p) => this.isEnabled(p));\n            for (const particle of query) {\n                particle.bubble.inRange = true;\n                const pos = particle.getPosition(), pointDistance = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getDistance)(pos, mousePos), ratio = 1 - pointDistance / distance;\n                if (pointDistance <= distance) {\n                    if (ratio >= 0 && container.interactivity.status === tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.mouseMoveEvent) {\n                        this._hoverBubbleSize(particle, ratio);\n                        this._hoverBubbleOpacity(particle, ratio);\n                        this._hoverBubbleColor(particle, ratio);\n                    }\n                }\n                else {\n                    this.reset(particle);\n                }\n                if (container.interactivity.status === tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.mouseLeaveEvent) {\n                    this.reset(particle);\n                }\n            }\n        };\n        this._hoverBubbleColor = (particle, ratio, divBubble) => {\n            const options = this.container.actualOptions, bubbleOptions = divBubble ?? options.interactivity.modes.bubble;\n            if (!bubbleOptions) {\n                return;\n            }\n            if (!particle.bubble.finalColor) {\n                const modeColor = bubbleOptions.color;\n                if (!modeColor) {\n                    return;\n                }\n                const bubbleColor = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_3__.itemFromSingleOrMultiple)(modeColor);\n                particle.bubble.finalColor = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_4__.rangeColorToHsl)(bubbleColor);\n            }\n            if (!particle.bubble.finalColor) {\n                return;\n            }\n            if (bubbleOptions.mix) {\n                particle.bubble.color = undefined;\n                const pColor = particle.getFillColor();\n                particle.bubble.color = pColor\n                    ? (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_4__.rgbToHsl)((0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_4__.colorMix)(pColor, particle.bubble.finalColor, 1 - ratio, ratio))\n                    : particle.bubble.finalColor;\n            }\n            else {\n                particle.bubble.color = particle.bubble.finalColor;\n            }\n        };\n        this._hoverBubbleOpacity = (particle, ratio, divBubble) => {\n            const container = this.container, options = container.actualOptions, modeOpacity = divBubble?.opacity ?? options.interactivity.modes.bubble?.opacity;\n            if (!modeOpacity) {\n                return;\n            }\n            const optOpacity = particle.options.opacity.value, pOpacity = particle.opacity?.value ?? 1, opacity = (0,_Utils__WEBPACK_IMPORTED_MODULE_5__.calculateBubbleValue)(pOpacity, modeOpacity, (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getRangeMax)(optOpacity), ratio);\n            if (opacity !== undefined) {\n                particle.bubble.opacity = opacity;\n            }\n        };\n        this._hoverBubbleSize = (particle, ratio, divBubble) => {\n            const container = this.container, modeSize = divBubble?.size ? divBubble.size * container.retina.pixelRatio : container.retina.bubbleModeSize;\n            if (modeSize === undefined) {\n                return;\n            }\n            const optSize = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getRangeMax)(particle.options.size.value) * container.retina.pixelRatio, pSize = particle.size.value, size = (0,_Utils__WEBPACK_IMPORTED_MODULE_5__.calculateBubbleValue)(pSize, modeSize, optSize, ratio);\n            if (size !== undefined) {\n                particle.bubble.radius = size;\n            }\n        };\n        this._process = (particle, distMouse, timeSpent, data) => {\n            const container = this.container, bubbleParam = data.bubbleObj.optValue, options = container.actualOptions, bubbleOptions = options.interactivity.modes.bubble;\n            if (!bubbleOptions || bubbleParam === undefined) {\n                return;\n            }\n            const bubbleDuration = bubbleOptions.duration, bubbleDistance = container.retina.bubbleModeDistance, particlesParam = data.particlesObj.optValue, pObjBubble = data.bubbleObj.value, pObj = data.particlesObj.value || 0, type = data.type;\n            if (!bubbleDistance || bubbleDistance < 0 || bubbleParam === particlesParam) {\n                return;\n            }\n            if (!container.bubble) {\n                container.bubble = {};\n            }\n            if (container.bubble.durationEnd) {\n                if (pObjBubble) {\n                    if (type === \"size\") {\n                        delete particle.bubble.radius;\n                    }\n                    if (type === \"opacity\") {\n                        delete particle.bubble.opacity;\n                    }\n                }\n            }\n            else {\n                if (distMouse <= bubbleDistance) {\n                    const obj = pObjBubble ?? pObj;\n                    if (obj !== bubbleParam) {\n                        const value = pObj - (timeSpent * (pObj - bubbleParam)) / bubbleDuration;\n                        if (type === \"size\") {\n                            particle.bubble.radius = value;\n                        }\n                        if (type === \"opacity\") {\n                            particle.bubble.opacity = value;\n                        }\n                    }\n                }\n                else {\n                    if (type === \"size\") {\n                        delete particle.bubble.radius;\n                    }\n                    if (type === \"opacity\") {\n                        delete particle.bubble.opacity;\n                    }\n                }\n            }\n        };\n        this._singleSelectorHover = (delta, selector, div) => {\n            const container = this.container, selectors = document.querySelectorAll(selector), bubble = container.actualOptions.interactivity.modes.bubble;\n            if (!bubble || !selectors.length) {\n                return;\n            }\n            selectors.forEach((item) => {\n                const elem = item, pxRatio = container.retina.pixelRatio, pos = {\n                    x: (elem.offsetLeft + elem.offsetWidth / 2) * pxRatio,\n                    y: (elem.offsetTop + elem.offsetHeight / 2) * pxRatio,\n                }, repulseRadius = (elem.offsetWidth / 2) * pxRatio, area = div.type === \"circle\"\n                    ? new tsparticles_engine__WEBPACK_IMPORTED_MODULE_6__.Circle(pos.x, pos.y, repulseRadius)\n                    : new tsparticles_engine__WEBPACK_IMPORTED_MODULE_7__.Rectangle(elem.offsetLeft * pxRatio, elem.offsetTop * pxRatio, elem.offsetWidth * pxRatio, elem.offsetHeight * pxRatio), query = container.particles.quadTree.query(area, (p) => this.isEnabled(p));\n                for (const particle of query) {\n                    if (!area.contains(particle.getPosition())) {\n                        continue;\n                    }\n                    particle.bubble.inRange = true;\n                    const divs = bubble.divs, divBubble = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_3__.divMode)(divs, elem);\n                    if (!particle.bubble.div || particle.bubble.div !== elem) {\n                        this.clear(particle, delta, true);\n                        particle.bubble.div = elem;\n                    }\n                    this._hoverBubbleSize(particle, 1, divBubble);\n                    this._hoverBubbleOpacity(particle, 1, divBubble);\n                    this._hoverBubbleColor(particle, 1, divBubble);\n                }\n            });\n        };\n        if (!container.bubble) {\n            container.bubble = {};\n        }\n        this.handleClickMode = (mode) => {\n            if (mode !== \"bubble\") {\n                return;\n            }\n            if (!container.bubble) {\n                container.bubble = {};\n            }\n            container.bubble.clicking = true;\n        };\n    }\n    clear(particle, delta, force) {\n        if (particle.bubble.inRange && !force) {\n            return;\n        }\n        delete particle.bubble.div;\n        delete particle.bubble.opacity;\n        delete particle.bubble.radius;\n        delete particle.bubble.color;\n    }\n    init() {\n        const container = this.container, bubble = container.actualOptions.interactivity.modes.bubble;\n        if (!bubble) {\n            return;\n        }\n        container.retina.bubbleModeDistance = bubble.distance * container.retina.pixelRatio;\n        if (bubble.size !== undefined) {\n            container.retina.bubbleModeSize = bubble.size * container.retina.pixelRatio;\n        }\n    }\n    async interact(delta) {\n        const options = this.container.actualOptions, events = options.interactivity.events, onHover = events.onHover, onClick = events.onClick, hoverEnabled = onHover.enable, hoverMode = onHover.mode, clickEnabled = onClick.enable, clickMode = onClick.mode, divs = events.onDiv;\n        if (hoverEnabled && (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_3__.isInArray)(\"bubble\", hoverMode)) {\n            this._hoverBubble();\n        }\n        else if (clickEnabled && (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_3__.isInArray)(\"bubble\", clickMode)) {\n            this._clickBubble();\n        }\n        else {\n            (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_3__.divModeExecute)(\"bubble\", divs, (selector, div) => this._singleSelectorHover(delta, selector, div));\n        }\n    }\n    isEnabled(particle) {\n        const container = this.container, options = container.actualOptions, mouse = container.interactivity.mouse, events = (particle?.interactivity ?? options.interactivity).events, { onClick, onDiv, onHover } = events, divBubble = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_3__.isDivModeEnabled)(\"bubble\", onDiv);\n        if (!(divBubble || (onHover.enable && mouse.position) || (onClick.enable && mouse.clickPosition))) {\n            return false;\n        }\n        return (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_3__.isInArray)(\"bubble\", onHover.mode) || (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_3__.isInArray)(\"bubble\", onClick.mode) || divBubble;\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.bubble) {\n            options.bubble = new _Options_Classes_Bubble__WEBPACK_IMPORTED_MODULE_8__.Bubble();\n        }\n        for (const source of sources) {\n            options.bubble.load(source?.bubble);\n        }\n    }\n    reset(particle) {\n        particle.bubble.inRange = false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-external-bubble/esm/Bubbler.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-external-bubble/esm/Options/Classes/Bubble.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-external-bubble/esm/Options/Classes/Bubble.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bubble: () => (/* binding */ Bubble)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/Utils.js\");\n/* harmony import */ var _BubbleBase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BubbleBase */ \"(ssr)/./node_modules/tsparticles-interaction-external-bubble/esm/Options/Classes/BubbleBase.js\");\n/* harmony import */ var _BubbleDiv__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BubbleDiv */ \"(ssr)/./node_modules/tsparticles-interaction-external-bubble/esm/Options/Classes/BubbleDiv.js\");\n\n\n\nclass Bubble extends _BubbleBase__WEBPACK_IMPORTED_MODULE_0__.BubbleBase {\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        this.divs = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.executeOnSingleOrMultiple)(data.divs, (div) => {\n            const tmp = new _BubbleDiv__WEBPACK_IMPORTED_MODULE_2__.BubbleDiv();\n            tmp.load(div);\n            return tmp;\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tZXh0ZXJuYWwtYnViYmxlL2VzbS9PcHRpb25zL0NsYXNzZXMvQnViYmxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZ0U7QUFDdEI7QUFDRjtBQUNqQyxxQkFBcUIsbURBQVU7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQiw2RUFBeUI7QUFDN0MsNEJBQTRCLGlEQUFTO0FBQ3JDO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1dHVyaXN0aWMtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL3RzcGFydGljbGVzLWludGVyYWN0aW9uLWV4dGVybmFsLWJ1YmJsZS9lc20vT3B0aW9ucy9DbGFzc2VzL0J1YmJsZS5qcz9jNmE2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGV4ZWN1dGVPblNpbmdsZU9yTXVsdGlwbGUsIH0gZnJvbSBcInRzcGFydGljbGVzLWVuZ2luZVwiO1xuaW1wb3J0IHsgQnViYmxlQmFzZSB9IGZyb20gXCIuL0J1YmJsZUJhc2VcIjtcbmltcG9ydCB7IEJ1YmJsZURpdiB9IGZyb20gXCIuL0J1YmJsZURpdlwiO1xuZXhwb3J0IGNsYXNzIEJ1YmJsZSBleHRlbmRzIEJ1YmJsZUJhc2Uge1xuICAgIGxvYWQoZGF0YSkge1xuICAgICAgICBzdXBlci5sb2FkKGRhdGEpO1xuICAgICAgICBpZiAoIWRhdGEpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmRpdnMgPSBleGVjdXRlT25TaW5nbGVPck11bHRpcGxlKGRhdGEuZGl2cywgKGRpdikgPT4ge1xuICAgICAgICAgICAgY29uc3QgdG1wID0gbmV3IEJ1YmJsZURpdigpO1xuICAgICAgICAgICAgdG1wLmxvYWQoZGl2KTtcbiAgICAgICAgICAgIHJldHVybiB0bXA7XG4gICAgICAgIH0pO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-external-bubble/esm/Options/Classes/Bubble.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-external-bubble/esm/Options/Classes/BubbleBase.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-external-bubble/esm/Options/Classes/BubbleBase.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BubbleBase: () => (/* binding */ BubbleBase)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/Utils.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Options/Classes/OptionsColor.js\");\n\nclass BubbleBase {\n    constructor() {\n        this.distance = 200;\n        this.duration = 0.4;\n        this.mix = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n        if (data.duration !== undefined) {\n            this.duration = data.duration;\n        }\n        if (data.mix !== undefined) {\n            this.mix = data.mix;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n        if (data.color !== undefined) {\n            const sourceColor = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isArray)(this.color) ? undefined : this.color;\n            this.color = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.executeOnSingleOrMultiple)(data.color, (color) => {\n                return tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.OptionsColor.create(sourceColor, color);\n            });\n        }\n        if (data.size !== undefined) {\n            this.size = data.size;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tZXh0ZXJuYWwtYnViYmxlL2VzbS9PcHRpb25zL0NsYXNzZXMvQnViYmxlQmFzZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBdUY7QUFDaEY7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQywyREFBTztBQUN2Qyx5QkFBeUIsNkVBQXlCO0FBQ2xELHVCQUF1Qiw0REFBWTtBQUNuQyxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnV0dXJpc3RpYy1wb3J0Zm9saW8vLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tZXh0ZXJuYWwtYnViYmxlL2VzbS9PcHRpb25zL0NsYXNzZXMvQnViYmxlQmFzZS5qcz8yYTM3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE9wdGlvbnNDb2xvciwgZXhlY3V0ZU9uU2luZ2xlT3JNdWx0aXBsZSwgaXNBcnJheSwgfSBmcm9tIFwidHNwYXJ0aWNsZXMtZW5naW5lXCI7XG5leHBvcnQgY2xhc3MgQnViYmxlQmFzZSB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHRoaXMuZGlzdGFuY2UgPSAyMDA7XG4gICAgICAgIHRoaXMuZHVyYXRpb24gPSAwLjQ7XG4gICAgICAgIHRoaXMubWl4ID0gZmFsc2U7XG4gICAgfVxuICAgIGxvYWQoZGF0YSkge1xuICAgICAgICBpZiAoIWRhdGEpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5kaXN0YW5jZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aGlzLmRpc3RhbmNlID0gZGF0YS5kaXN0YW5jZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5kdXJhdGlvbiAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aGlzLmR1cmF0aW9uID0gZGF0YS5kdXJhdGlvbjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5taXggIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5taXggPSBkYXRhLm1peDtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5vcGFjaXR5ICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHRoaXMub3BhY2l0eSA9IGRhdGEub3BhY2l0eTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5jb2xvciAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICBjb25zdCBzb3VyY2VDb2xvciA9IGlzQXJyYXkodGhpcy5jb2xvcikgPyB1bmRlZmluZWQgOiB0aGlzLmNvbG9yO1xuICAgICAgICAgICAgdGhpcy5jb2xvciA9IGV4ZWN1dGVPblNpbmdsZU9yTXVsdGlwbGUoZGF0YS5jb2xvciwgKGNvbG9yKSA9PiB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIE9wdGlvbnNDb2xvci5jcmVhdGUoc291cmNlQ29sb3IsIGNvbG9yKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIGlmIChkYXRhLnNpemUgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5zaXplID0gZGF0YS5zaXplO1xuICAgICAgICB9XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-external-bubble/esm/Options/Classes/BubbleBase.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-external-bubble/esm/Options/Classes/BubbleDiv.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-external-bubble/esm/Options/Classes/BubbleDiv.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BubbleDiv: () => (/* binding */ BubbleDiv)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/Utils.js\");\n/* harmony import */ var _BubbleBase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BubbleBase */ \"(ssr)/./node_modules/tsparticles-interaction-external-bubble/esm/Options/Classes/BubbleBase.js\");\n\n\nclass BubbleDiv extends _BubbleBase__WEBPACK_IMPORTED_MODULE_0__.BubbleBase {\n    constructor() {\n        super();\n        this.selectors = [];\n    }\n    get ids() {\n        return (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.executeOnSingleOrMultiple)(this.selectors, (t) => t.replace(\"#\", \"\"));\n    }\n    set ids(value) {\n        this.selectors = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.executeOnSingleOrMultiple)(value, (t) => `#${t}`);\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        if (data.ids !== undefined) {\n            this.ids = data.ids;\n        }\n        if (data.selectors !== undefined) {\n            this.selectors = data.selectors;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tZXh0ZXJuYWwtYnViYmxlL2VzbS9PcHRpb25zL0NsYXNzZXMvQnViYmxlRGl2LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnRTtBQUN0QjtBQUNuQyx3QkFBd0IsbURBQVU7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsNkVBQXlCO0FBQ3hDO0FBQ0E7QUFDQSx5QkFBeUIsNkVBQXlCLG1CQUFtQixFQUFFO0FBQ3ZFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mdXR1cmlzdGljLXBvcnRmb2xpby8uL25vZGVfbW9kdWxlcy90c3BhcnRpY2xlcy1pbnRlcmFjdGlvbi1leHRlcm5hbC1idWJibGUvZXNtL09wdGlvbnMvQ2xhc3Nlcy9CdWJibGVEaXYuanM/NjIzOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBleGVjdXRlT25TaW5nbGVPck11bHRpcGxlLCB9IGZyb20gXCJ0c3BhcnRpY2xlcy1lbmdpbmVcIjtcbmltcG9ydCB7IEJ1YmJsZUJhc2UgfSBmcm9tIFwiLi9CdWJibGVCYXNlXCI7XG5leHBvcnQgY2xhc3MgQnViYmxlRGl2IGV4dGVuZHMgQnViYmxlQmFzZSB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHN1cGVyKCk7XG4gICAgICAgIHRoaXMuc2VsZWN0b3JzID0gW107XG4gICAgfVxuICAgIGdldCBpZHMoKSB7XG4gICAgICAgIHJldHVybiBleGVjdXRlT25TaW5nbGVPck11bHRpcGxlKHRoaXMuc2VsZWN0b3JzLCAodCkgPT4gdC5yZXBsYWNlKFwiI1wiLCBcIlwiKSk7XG4gICAgfVxuICAgIHNldCBpZHModmFsdWUpIHtcbiAgICAgICAgdGhpcy5zZWxlY3RvcnMgPSBleGVjdXRlT25TaW5nbGVPck11bHRpcGxlKHZhbHVlLCAodCkgPT4gYCMke3R9YCk7XG4gICAgfVxuICAgIGxvYWQoZGF0YSkge1xuICAgICAgICBzdXBlci5sb2FkKGRhdGEpO1xuICAgICAgICBpZiAoIWRhdGEpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5pZHMgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5pZHMgPSBkYXRhLmlkcztcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5zZWxlY3RvcnMgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5zZWxlY3RvcnMgPSBkYXRhLnNlbGVjdG9ycztcbiAgICAgICAgfVxuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-external-bubble/esm/Options/Classes/BubbleDiv.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-external-bubble/esm/Utils.js":
/*!***************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-external-bubble/esm/Utils.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateBubbleValue: () => (/* binding */ calculateBubbleValue)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n\nfunction calculateBubbleValue(particleValue, modeValue, optionsValue, ratio) {\n    if (modeValue >= optionsValue) {\n        const value = particleValue + (modeValue - optionsValue) * ratio;\n        return (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.clamp)(value, particleValue, modeValue);\n    }\n    else if (modeValue < optionsValue) {\n        const value = particleValue - (optionsValue - modeValue) * ratio;\n        return (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.clamp)(value, modeValue, particleValue);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tZXh0ZXJuYWwtYnViYmxlL2VzbS9VdGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEyQztBQUNwQztBQUNQO0FBQ0E7QUFDQSxlQUFlLHlEQUFLO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBLGVBQWUseURBQUs7QUFDcEI7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1dHVyaXN0aWMtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL3RzcGFydGljbGVzLWludGVyYWN0aW9uLWV4dGVybmFsLWJ1YmJsZS9lc20vVXRpbHMuanM/MDM4YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbGFtcCB9IGZyb20gXCJ0c3BhcnRpY2xlcy1lbmdpbmVcIjtcbmV4cG9ydCBmdW5jdGlvbiBjYWxjdWxhdGVCdWJibGVWYWx1ZShwYXJ0aWNsZVZhbHVlLCBtb2RlVmFsdWUsIG9wdGlvbnNWYWx1ZSwgcmF0aW8pIHtcbiAgICBpZiAobW9kZVZhbHVlID49IG9wdGlvbnNWYWx1ZSkge1xuICAgICAgICBjb25zdCB2YWx1ZSA9IHBhcnRpY2xlVmFsdWUgKyAobW9kZVZhbHVlIC0gb3B0aW9uc1ZhbHVlKSAqIHJhdGlvO1xuICAgICAgICByZXR1cm4gY2xhbXAodmFsdWUsIHBhcnRpY2xlVmFsdWUsIG1vZGVWYWx1ZSk7XG4gICAgfVxuICAgIGVsc2UgaWYgKG1vZGVWYWx1ZSA8IG9wdGlvbnNWYWx1ZSkge1xuICAgICAgICBjb25zdCB2YWx1ZSA9IHBhcnRpY2xlVmFsdWUgLSAob3B0aW9uc1ZhbHVlIC0gbW9kZVZhbHVlKSAqIHJhdGlvO1xuICAgICAgICByZXR1cm4gY2xhbXAodmFsdWUsIG1vZGVWYWx1ZSwgcGFydGljbGVWYWx1ZSk7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-external-bubble/esm/Utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-external-bubble/esm/index.js":
/*!***************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-external-bubble/esm/index.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bubble: () => (/* reexport safe */ _Options_Classes_Bubble__WEBPACK_IMPORTED_MODULE_3__.Bubble),\n/* harmony export */   BubbleBase: () => (/* reexport safe */ _Options_Classes_BubbleBase__WEBPACK_IMPORTED_MODULE_1__.BubbleBase),\n/* harmony export */   BubbleDiv: () => (/* reexport safe */ _Options_Classes_BubbleDiv__WEBPACK_IMPORTED_MODULE_2__.BubbleDiv),\n/* harmony export */   loadExternalBubbleInteraction: () => (/* binding */ loadExternalBubbleInteraction)\n/* harmony export */ });\n/* harmony import */ var _Bubbler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Bubbler */ \"(ssr)/./node_modules/tsparticles-interaction-external-bubble/esm/Bubbler.js\");\n/* harmony import */ var _Options_Classes_BubbleBase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Options/Classes/BubbleBase */ \"(ssr)/./node_modules/tsparticles-interaction-external-bubble/esm/Options/Classes/BubbleBase.js\");\n/* harmony import */ var _Options_Classes_BubbleDiv__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Options/Classes/BubbleDiv */ \"(ssr)/./node_modules/tsparticles-interaction-external-bubble/esm/Options/Classes/BubbleDiv.js\");\n/* harmony import */ var _Options_Classes_Bubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Options/Classes/Bubble */ \"(ssr)/./node_modules/tsparticles-interaction-external-bubble/esm/Options/Classes/Bubble.js\");\n\nasync function loadExternalBubbleInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalBubble\", (container) => new _Bubbler__WEBPACK_IMPORTED_MODULE_0__.Bubbler(container), refresh);\n}\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tZXh0ZXJuYWwtYnViYmxlL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFvQztBQUM3QjtBQUNQLG9FQUFvRSw2Q0FBTztBQUMzRTtBQUM2QztBQUNEO0FBQ0g7QUFDUTtBQUNEO0FBQ0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mdXR1cmlzdGljLXBvcnRmb2xpby8uL25vZGVfbW9kdWxlcy90c3BhcnRpY2xlcy1pbnRlcmFjdGlvbi1leHRlcm5hbC1idWJibGUvZXNtL2luZGV4LmpzP2Y4YzciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQnViYmxlciB9IGZyb20gXCIuL0J1YmJsZXJcIjtcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBsb2FkRXh0ZXJuYWxCdWJibGVJbnRlcmFjdGlvbihlbmdpbmUsIHJlZnJlc2ggPSB0cnVlKSB7XG4gICAgYXdhaXQgZW5naW5lLmFkZEludGVyYWN0b3IoXCJleHRlcm5hbEJ1YmJsZVwiLCAoY29udGFpbmVyKSA9PiBuZXcgQnViYmxlcihjb250YWluZXIpLCByZWZyZXNoKTtcbn1cbmV4cG9ydCAqIGZyb20gXCIuL09wdGlvbnMvQ2xhc3Nlcy9CdWJibGVCYXNlXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9PcHRpb25zL0NsYXNzZXMvQnViYmxlRGl2XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9PcHRpb25zL0NsYXNzZXMvQnViYmxlXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9PcHRpb25zL0ludGVyZmFjZXMvSUJ1YmJsZUJhc2VcIjtcbmV4cG9ydCAqIGZyb20gXCIuL09wdGlvbnMvSW50ZXJmYWNlcy9JQnViYmxlRGl2XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9PcHRpb25zL0ludGVyZmFjZXMvSUJ1YmJsZVwiO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-external-bubble/esm/index.js\n");

/***/ })

};
;