{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/server/shared.ts"], "names": ["badRequest", "findSourcePackage", "getOriginalCodeFrame", "internalServerError", "json", "noContent", "reactVendoredRe", "reactNodeModulesRe", "nextInternalsRe", "nextMethodRe", "isInternal", "file", "test", "methodName", "frame", "source", "includes", "codeFrameColumns", "start", "line", "lineNumber", "column", "forceColor", "res", "statusCode", "end", "e", "data", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "from", "JSON", "stringify"], "mappings": ";;;;;;;;;;;;;;;;;;;IA6FgBA,UAAU;eAAVA;;IAzDAC,iBAAiB;eAAjBA;;IA0BAC,oBAAoB;eAApBA;;IAoCAC,mBAAmB;eAAnBA;;IAKAC,IAAI;eAAJA;;IAfAC,SAAS;eAATA;;;2BAtFiB;AAWjC,2DAA2D,GAC3D,MAAMC,kBACJ;AAEF,mFAAmF,GACnF,MAAMC,qBAAqB;AAE3B,MAAMC,kBACJ;AAEF,MAAMC,eAAe;AAErB,SAASC,WAAWC,IAAmB;IACrC,IAAI,CAACA,MAAM,OAAO;IAElB,OACEH,gBAAgBI,IAAI,CAACD,SACrBL,gBAAgBM,IAAI,CAACD,SACrBJ,mBAAmBK,IAAI,CAACD;AAE5B;AAGO,SAASV,kBAAkB,KAG4B;IAH5B,IAAA,EAChCU,IAAI,EACJE,UAAU,EACkD,GAH5B;IAMhC,IAAIF,MAAM;QACR,mEAAmE;QACnE,IAAIL,gBAAgBM,IAAI,CAACD,SAASJ,mBAAmBK,IAAI,CAACD,OAAO;YAC/D,OAAO;QACT,OAAO,IAAIH,gBAAgBI,IAAI,CAACD,OAAO;YACrC,OAAO;QACT;IACF;IAEA,IAAIE,YAAY;QACd,IAAIJ,aAAaG,IAAI,CAACC,aAAa;YACjC,OAAO;QACT;IACF;AACF;AAMO,SAASX,qBACdY,KAAiB,EACjBC,MAAqB;QAInBD;IAFF,IACE,CAACC,YACDD,cAAAA,MAAMH,IAAI,qBAAVG,YAAYE,QAAQ,CAAC,oBACrBN,WAAWI,MAAMH,IAAI,GACrB;QACA,OAAO;IACT;QAOYG,mBAEEA;IAPd,OAAOG,IAAAA,2BAAgB,EACrBF,QACA;QACEG,OAAO;YACL,wDAAwD;YACxDC,MAAML,CAAAA,oBAAAA,MAAMM,UAAU,YAAhBN,oBAAoB,CAAC;YAC3B,8DAA8D;YAC9DO,QAAQP,CAAAA,gBAAAA,MAAMO,MAAM,YAAZP,gBAAgB;QAC1B;IACF,GACA;QAAEQ,YAAY;IAAK;AAEvB;AAEO,SAASjB,UAAUkB,GAAmB;IAC3CA,IAAIC,UAAU,GAAG;IACjBD,IAAIE,GAAG,CAAC;AACV;AAEO,SAASzB,WAAWuB,GAAmB;IAC5CA,IAAIC,UAAU,GAAG;IACjBD,IAAIE,GAAG,CAAC;AACV;AAEO,SAAStB,oBAAoBoB,GAAmB,EAAEG,CAAO;IAC9DH,IAAIC,UAAU,GAAG;IACjBD,IAAIE,GAAG,CAACC,YAAAA,IAAK;AACf;AAEO,SAAStB,KAAKmB,GAAmB,EAAEI,IAAS;IACjDJ,IACGK,SAAS,CAAC,gBAAgB,oBAC1BH,GAAG,CAACI,OAAOC,IAAI,CAACC,KAAKC,SAAS,CAACL;AACpC"}