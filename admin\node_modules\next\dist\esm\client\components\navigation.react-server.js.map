{"version": 3, "sources": ["../../../src/client/components/navigation.react-server.ts"], "names": ["ReadonlyURLSearchParamsError", "Error", "constructor", "ReadonlyURLSearchParams", "URLSearchParams", "append", "delete", "set", "sort", "redirect", "permanentRedirect", "RedirectType", "notFound"], "mappings": "AAAA,cAAc,GACd,MAAMA,qCAAqCC;IACzCC,aAAc;QACZ,KAAK,CACH;IAEJ;AACF;AAEA,MAAMC,gCAAgCC;IACpC,wKAAwK,GACxKC,SAAS;QACP,MAAM,IAAIL;IACZ;IACA,wKAAwK,GACxKM,SAAS;QACP,MAAM,IAAIN;IACZ;IACA,wKAAwK,GACxKO,MAAM;QACJ,MAAM,IAAIP;IACZ;IACA,wKAAwK,GACxKQ,OAAO;QACL,MAAM,IAAIR;IACZ;AACF;AAEA,SAASS,QAAQ,EAAEC,iBAAiB,EAAEC,YAAY,QAAQ,aAAY;AACtE,SAASC,QAAQ,QAAQ,cAAa;AACtC,SAAST,uBAAuB,GAAE"}