const mongoose = require('mongoose');

const skillSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  level: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  category: {
    type: String,
    required: true,
    enum: ['frontend', 'backend', 'devops', 'ai-data', 'mobile', 'other'],
    default: 'other'
  },
  icon: {
    type: String,
    default: '🔧'
  },
  color: {
    type: String,
    default: '#00d4ff'
  },
  description: {
    type: String,
    default: ''
  },
  yearsOfExperience: {
    type: Number,
    default: 0
  },
  projects: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Project'
  }],
  certifications: [{
    name: String,
    issuer: String,
    date: Date,
    url: String
  }],
  isVisible: {
    type: Boolean,
    default: true
  },
  order: {
    type: Number,
    default: 0
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }]
}, {
  timestamps: true
});

// Index for better query performance
skillSchema.index({ category: 1, order: 1 });
skillSchema.index({ isVisible: 1, level: -1 });

// Virtual for skill level description
skillSchema.virtual('levelDescription').get(function() {
  if (this.level >= 90) return 'Expert';
  if (this.level >= 75) return 'Advanced';
  if (this.level >= 50) return 'Intermediate';
  if (this.level >= 25) return 'Beginner';
  return 'Learning';
});

// Static method to get skills by category
skillSchema.statics.getByCategory = function(category) {
  return this.find({ 
    category: category,
    isVisible: true 
  }).sort({ order: 1, level: -1 });
};

// Static method to get top skills
skillSchema.statics.getTopSkills = function(limit = 10) {
  return this.find({ isVisible: true })
    .sort({ level: -1, yearsOfExperience: -1 })
    .limit(limit);
};

// Static method to get skills grouped by category
skillSchema.statics.getGroupedByCategory = async function() {
  const skills = await this.find({ isVisible: true })
    .sort({ category: 1, order: 1, level: -1 });
  
  return skills.reduce((acc, skill) => {
    if (!acc[skill.category]) {
      acc[skill.category] = [];
    }
    acc[skill.category].push(skill);
    return acc;
  }, {});
};

module.exports = mongoose.model('Skill', skillSchema);
