{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-flight-action-entry-loader.ts"], "names": ["nextFlightActionEntryLoader", "actions", "getOptions", "actionList", "JSON", "parse", "individualActions", "map", "path", "names", "name", "id", "generateActionId", "flat", "stringify", "join"], "mappings": ";;;;+BA8CA;;;eAAA;;;uBA9CiC;AAMjC,SAASA;IACP,MAAM,EAAEC,OAAO,EAAE,GAAuC,IAAI,CAACC,UAAU;IAEvE,MAAMC,aAAaC,KAAKC,KAAK,CAACJ;IAC9B,MAAMK,oBAAoBH,WACvBI,GAAG,CAAC,CAAC,CAACC,MAAMC,MAAM;QACjB,OAAOA,MAAMF,GAAG,CAAC,CAACG;YAChB,MAAMC,KAAKC,IAAAA,uBAAgB,EAACJ,MAAME;YAClC,OAAO;gBAACC;gBAAIH;gBAAME;aAAK;QACzB;IACF,GACCG,IAAI;IAEP,OAAO,CAAC;;AAEV,EAAEP,kBACCC,GAAG,CAAC,CAAC,CAACI,IAAIH,MAAME,KAAK;QACpB,OAAO,CAAC,CAAC,EAAEC,GAAG,2CAA2C,EAAEP,KAAKU,SAAS,CACvEN,MACA,kBAAkB,EAAEJ,KAAKU,SAAS,CAACJ,MAAM,GAAG,CAAC;IACjD,GACCK,IAAI,CAAC,MAAM;;;;;;;;;;AAUd,EAAET,kBACCC,GAAG,CAAC,CAAC,CAACI,GAAG;QACR,OAAO,CAAC,GAAG,EAAEA,GAAG,wBAAwB,EAAEA,GAAG,GAAG,CAAC;IACnD,GACCI,IAAI,CAAC,MAAM;;AAEd,CAAC;AACD;MAEA,WAAef"}