"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tsparticles-interaction-external-remove";
exports.ids = ["vendor-chunks/tsparticles-interaction-external-remove"];
exports.modules = {

/***/ "(ssr)/./node_modules/tsparticles-interaction-external-remove/esm/Options/Classes/Remove.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-external-remove/esm/Options/Classes/Remove.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Remove: () => (/* binding */ Remove)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n\nclass Remove {\n    constructor() {\n        this.quantity = 2;\n    }\n    get particles_nb() {\n        return this.quantity;\n    }\n    set particles_nb(value) {\n        this.quantity = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(value);\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        const quantity = data.quantity ?? data.particles_nb;\n        if (quantity !== undefined) {\n            this.quantity = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(quantity);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tZXh0ZXJuYWwtcmVtb3ZlL2VzbS9PcHRpb25zL0NsYXNzZXMvUmVtb3ZlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW1EO0FBQzVDO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsaUVBQWE7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsaUVBQWE7QUFDekM7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnV0dXJpc3RpYy1wb3J0Zm9saW8vLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tZXh0ZXJuYWwtcmVtb3ZlL2VzbS9PcHRpb25zL0NsYXNzZXMvUmVtb3ZlLmpzP2ZlZWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc2V0UmFuZ2VWYWx1ZSB9IGZyb20gXCJ0c3BhcnRpY2xlcy1lbmdpbmVcIjtcbmV4cG9ydCBjbGFzcyBSZW1vdmUge1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICB0aGlzLnF1YW50aXR5ID0gMjtcbiAgICB9XG4gICAgZ2V0IHBhcnRpY2xlc19uYigpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMucXVhbnRpdHk7XG4gICAgfVxuICAgIHNldCBwYXJ0aWNsZXNfbmIodmFsdWUpIHtcbiAgICAgICAgdGhpcy5xdWFudGl0eSA9IHNldFJhbmdlVmFsdWUodmFsdWUpO1xuICAgIH1cbiAgICBsb2FkKGRhdGEpIHtcbiAgICAgICAgaWYgKCFkYXRhKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgcXVhbnRpdHkgPSBkYXRhLnF1YW50aXR5ID8/IGRhdGEucGFydGljbGVzX25iO1xuICAgICAgICBpZiAocXVhbnRpdHkgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5xdWFudGl0eSA9IHNldFJhbmdlVmFsdWUocXVhbnRpdHkpO1xuICAgICAgICB9XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-external-remove/esm/Options/Classes/Remove.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-external-remove/esm/Remover.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-external-remove/esm/Remover.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Remover: () => (/* binding */ Remover)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/ExternalInteractorBase.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n/* harmony import */ var _Options_Classes_Remove__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Options/Classes/Remove */ \"(ssr)/./node_modules/tsparticles-interaction-external-remove/esm/Options/Classes/Remove.js\");\n\n\nclass Remover extends tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n        this.handleClickMode = (mode) => {\n            const container = this.container, options = container.actualOptions;\n            if (!options.interactivity.modes.remove || mode !== \"remove\") {\n                return;\n            }\n            const removeNb = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getRangeValue)(options.interactivity.modes.remove.quantity);\n            container.particles.removeQuantity(removeNb);\n        };\n    }\n    clear() {\n    }\n    init() {\n    }\n    async interact() {\n    }\n    isEnabled() {\n        return true;\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.remove) {\n            options.remove = new _Options_Classes_Remove__WEBPACK_IMPORTED_MODULE_2__.Remove();\n        }\n        for (const source of sources) {\n            options.remove.load(source?.remove);\n        }\n    }\n    reset() {\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tZXh0ZXJuYWwtcmVtb3ZlL2VzbS9SZW1vdmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBNEU7QUFDMUI7QUFDM0Msc0JBQXNCLHNFQUFzQjtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QixpRUFBYTtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLDJEQUFNO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mdXR1cmlzdGljLXBvcnRmb2xpby8uL25vZGVfbW9kdWxlcy90c3BhcnRpY2xlcy1pbnRlcmFjdGlvbi1leHRlcm5hbC1yZW1vdmUvZXNtL1JlbW92ZXIuanM/NDIyMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBFeHRlcm5hbEludGVyYWN0b3JCYXNlLCBnZXRSYW5nZVZhbHVlLCB9IGZyb20gXCJ0c3BhcnRpY2xlcy1lbmdpbmVcIjtcbmltcG9ydCB7IFJlbW92ZSB9IGZyb20gXCIuL09wdGlvbnMvQ2xhc3Nlcy9SZW1vdmVcIjtcbmV4cG9ydCBjbGFzcyBSZW1vdmVyIGV4dGVuZHMgRXh0ZXJuYWxJbnRlcmFjdG9yQmFzZSB7XG4gICAgY29uc3RydWN0b3IoY29udGFpbmVyKSB7XG4gICAgICAgIHN1cGVyKGNvbnRhaW5lcik7XG4gICAgICAgIHRoaXMuaGFuZGxlQ2xpY2tNb2RlID0gKG1vZGUpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGNvbnRhaW5lciA9IHRoaXMuY29udGFpbmVyLCBvcHRpb25zID0gY29udGFpbmVyLmFjdHVhbE9wdGlvbnM7XG4gICAgICAgICAgICBpZiAoIW9wdGlvbnMuaW50ZXJhY3Rpdml0eS5tb2Rlcy5yZW1vdmUgfHwgbW9kZSAhPT0gXCJyZW1vdmVcIikge1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHJlbW92ZU5iID0gZ2V0UmFuZ2VWYWx1ZShvcHRpb25zLmludGVyYWN0aXZpdHkubW9kZXMucmVtb3ZlLnF1YW50aXR5KTtcbiAgICAgICAgICAgIGNvbnRhaW5lci5wYXJ0aWNsZXMucmVtb3ZlUXVhbnRpdHkocmVtb3ZlTmIpO1xuICAgICAgICB9O1xuICAgIH1cbiAgICBjbGVhcigpIHtcbiAgICB9XG4gICAgaW5pdCgpIHtcbiAgICB9XG4gICAgYXN5bmMgaW50ZXJhY3QoKSB7XG4gICAgfVxuICAgIGlzRW5hYmxlZCgpIHtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIGxvYWRNb2RlT3B0aW9ucyhvcHRpb25zLCAuLi5zb3VyY2VzKSB7XG4gICAgICAgIGlmICghb3B0aW9ucy5yZW1vdmUpIHtcbiAgICAgICAgICAgIG9wdGlvbnMucmVtb3ZlID0gbmV3IFJlbW92ZSgpO1xuICAgICAgICB9XG4gICAgICAgIGZvciAoY29uc3Qgc291cmNlIG9mIHNvdXJjZXMpIHtcbiAgICAgICAgICAgIG9wdGlvbnMucmVtb3ZlLmxvYWQoc291cmNlPy5yZW1vdmUpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJlc2V0KCkge1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-external-remove/esm/Remover.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-external-remove/esm/index.js":
/*!***************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-external-remove/esm/index.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Remove: () => (/* reexport safe */ _Options_Classes_Remove__WEBPACK_IMPORTED_MODULE_1__.Remove),\n/* harmony export */   loadExternalRemoveInteraction: () => (/* binding */ loadExternalRemoveInteraction)\n/* harmony export */ });\n/* harmony import */ var _Remover__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Remover */ \"(ssr)/./node_modules/tsparticles-interaction-external-remove/esm/Remover.js\");\n/* harmony import */ var _Options_Classes_Remove__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Options/Classes/Remove */ \"(ssr)/./node_modules/tsparticles-interaction-external-remove/esm/Options/Classes/Remove.js\");\n\nasync function loadExternalRemoveInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalRemove\", (container) => new _Remover__WEBPACK_IMPORTED_MODULE_0__.Remover(container), refresh);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tZXh0ZXJuYWwtcmVtb3ZlL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQW9DO0FBQzdCO0FBQ1Asb0VBQW9FLDZDQUFPO0FBQzNFO0FBQ3lDO0FBQ0kiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mdXR1cmlzdGljLXBvcnRmb2xpby8uL25vZGVfbW9kdWxlcy90c3BhcnRpY2xlcy1pbnRlcmFjdGlvbi1leHRlcm5hbC1yZW1vdmUvZXNtL2luZGV4LmpzP2YwYmIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUmVtb3ZlciB9IGZyb20gXCIuL1JlbW92ZXJcIjtcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBsb2FkRXh0ZXJuYWxSZW1vdmVJbnRlcmFjdGlvbihlbmdpbmUsIHJlZnJlc2ggPSB0cnVlKSB7XG4gICAgYXdhaXQgZW5naW5lLmFkZEludGVyYWN0b3IoXCJleHRlcm5hbFJlbW92ZVwiLCAoY29udGFpbmVyKSA9PiBuZXcgUmVtb3Zlcihjb250YWluZXIpLCByZWZyZXNoKTtcbn1cbmV4cG9ydCAqIGZyb20gXCIuL09wdGlvbnMvQ2xhc3Nlcy9SZW1vdmVcIjtcbmV4cG9ydCAqIGZyb20gXCIuL09wdGlvbnMvSW50ZXJmYWNlcy9JUmVtb3ZlXCI7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-external-remove/esm/index.js\n");

/***/ })

};
;