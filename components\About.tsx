'use client'

import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { <PERSON>, <PERSON>, <PERSON>, Heart } from 'lucide-react'

const skills = [
  { name: 'HTML/CSS', level: 95, color: '#e34c26' },
  { name: 'JavaScript', level: 90, color: '#f7df1e' },
  { name: 'Python', level: 88, color: '#3776ab' },
  { name: 'React', level: 85, color: '#61dafb' },
  { name: 'FastAPI', level: 80, color: '#009688' },
  { name: 'Django', level: 75, color: '#092e20' },
  { name: 'Docker', level: 70, color: '#2496ed' },
  { name: 'PostgreSQL', level: 78, color: '#336791' },
  { name: 'Git', level: 85, color: '#f05032' },
]

const techIcons = [
  { name: 'HTML', icon: '🌐', color: 'text-orange-500' },
  { name: 'CSS', icon: '🎨', color: 'text-blue-500' },
  { name: 'Python', icon: '🐍', color: 'text-yellow-500' },
  { name: 'React', icon: '⚛️', color: 'text-cyan-500' },
  { name: 'FastAPI', icon: '⚡', color: 'text-green-500' },
  { name: 'Django', icon: '🎯', color: 'text-green-600' },
  { name: 'Docker', icon: '🐳', color: 'text-blue-600' },
  { name: 'PostgreSQL', icon: '🐘', color: 'text-blue-700' },
  { name: 'Git', icon: '📚', color: 'text-red-500' },
]

export default function About() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" ref={ref}>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        className="text-center mb-16"
      >
        <motion.h2
          variants={itemVariants}
          className="text-4xl md:text-6xl font-cyber font-bold text-neon-blue neon-glow-subtle mb-6"
        >
          About Me
        </motion.h2>
        <motion.div
          variants={itemVariants}
          className="w-24 h-1 bg-gradient-to-r from-neon-blue to-neon-green mx-auto"
        />
      </motion.div>

      <div className="grid lg:grid-cols-2 gap-16 items-center">
        {/* Left side - Story */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-8"
        >
          <motion.div
            variants={itemVariants}
            className="bg-gray-900/50 backdrop-blur-sm border border-neon-blue/20 rounded-lg p-8 hover:border-neon-blue/40 transition-all duration-300"
          >
            <div className="flex items-center mb-4">
              <Code className="text-neon-blue mr-3" size={24} />
              <h3 className="text-2xl font-tech font-semibold text-white">
                Self-Taught Developer
              </h3>
            </div>
            <p className="text-gray-300 leading-relaxed">
              I am a passionate self-taught developer who believes in the power of continuous learning. 
              My journey began with curiosity and has evolved into a deep love for creating innovative solutions.
            </p>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="bg-gray-900/50 backdrop-blur-sm border border-neon-green/20 rounded-lg p-8 hover:border-neon-green/40 transition-all duration-300"
          >
            <div className="flex items-center mb-4">
              <Brain className="text-neon-green mr-3" size={24} />
              <h3 className="text-2xl font-tech font-semibold text-white">
                AI & Tech Enthusiast
              </h3>
            </div>
            <p className="text-gray-300 leading-relaxed">
              I love AI, web development, futuristic tech, and creative coding. 
              I'm fascinated by how technology can solve real-world problems and create amazing experiences.
            </p>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="bg-gray-900/50 backdrop-blur-sm border border-neon-pink/20 rounded-lg p-8 hover:border-neon-pink/40 transition-all duration-300"
          >
            <div className="flex items-center mb-4">
              <Heart className="text-neon-pink mr-3" size={24} />
              <h3 className="text-2xl font-tech font-semibold text-white">
                Building to Inspire
              </h3>
            </div>
            <p className="text-gray-300 leading-relaxed">
              I build projects to learn and to inspire others. Every line of code is an opportunity 
              to create something meaningful and push the boundaries of what's possible.
            </p>
          </motion.div>
        </motion.div>

        {/* Right side - Skills */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-8"
        >
          {/* Rotating Tech Icons */}
          <motion.div
            variants={itemVariants}
            className="relative h-64 mb-12"
          >
            <div className="absolute inset-0 flex items-center justify-center">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                className="relative w-48 h-48"
              >
                {techIcons.map((tech, index) => {
                  const angle = (index * 360) / techIcons.length
                  const radius = 80
                  const x = Math.cos((angle * Math.PI) / 180) * radius
                  const y = Math.sin((angle * Math.PI) / 180) * radius
                  
                  return (
                    <motion.div
                      key={tech.name}
                      className={`absolute w-12 h-12 flex items-center justify-center rounded-full bg-gray-800/80 backdrop-blur-sm border border-gray-600 ${tech.color}`}
                      style={{
                        left: `calc(50% + ${x}px - 24px)`,
                        top: `calc(50% + ${y}px - 24px)`,
                      }}
                      animate={{ rotate: -360 }}
                      transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                      whileHover={{ scale: 1.2, zIndex: 10 }}
                      title={tech.name}
                    >
                      <span className="text-2xl">{tech.icon}</span>
                    </motion.div>
                  )
                })}
              </motion.div>
              
              {/* Center icon */}
              <motion.div
                className="absolute w-16 h-16 bg-neon-blue/20 rounded-full flex items-center justify-center border-2 border-neon-blue"
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <Rocket className="text-neon-blue" size={24} />
              </motion.div>
            </div>
          </motion.div>

          {/* Skill Bars */}
          <motion.div variants={itemVariants} className="space-y-6">
            <h3 className="text-2xl font-tech font-semibold text-white mb-6">
              Technical Skills
            </h3>
            {skills.map((skill, index) => (
              <motion.div
                key={skill.name}
                variants={itemVariants}
                className="space-y-2"
              >
                <div className="flex justify-between items-center">
                  <span className="text-gray-300 font-tech">{skill.name}</span>
                  <span className="text-neon-blue font-tech">{skill.level}%</span>
                </div>
                <div className="w-full bg-gray-800 rounded-full h-2 overflow-hidden">
                  <motion.div
                    className="h-full rounded-full"
                    style={{
                      background: `linear-gradient(90deg, ${skill.color}, #00d4ff)`,
                    }}
                    initial={{ width: 0 }}
                    animate={inView ? { width: `${skill.level}%` } : { width: 0 }}
                    transition={{ duration: 1.5, delay: index * 0.1 }}
                  />
                </div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}
