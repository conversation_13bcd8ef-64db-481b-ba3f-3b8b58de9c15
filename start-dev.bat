@echo off
echo.
echo 🚀 Starting Futuristic Portfolio Development Environment
echo ======================================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed. Please run setup.bat first.
    pause
    exit /b 1
)

REM Check if backend dependencies are installed
if not exist "backend\node_modules" (
    echo ❌ Backend dependencies not found. Please run setup.bat first.
    pause
    exit /b 1
)

REM Check if admin dependencies are installed
if not exist "admin\node_modules" (
    echo ❌ Admin panel dependencies not found. Please run setup.bat first.
    pause
    exit /b 1
)

REM Check if frontend dependencies are installed
if not exist "node_modules" (
    echo ❌ Frontend dependencies not found. Please run setup.bat first.
    pause
    exit /b 1
)

echo ℹ️  Starting all services...
echo.
echo 📝 This will open 3 command windows:
echo    1. Backend API (Port 5000)
echo    2. Frontend Portfolio (Port 3000)
echo    3. Admin Panel (Port 3001)
echo.
echo ⚠️  Make sure MongoDB is running if using local database!
echo.

pause

REM Start Backend API
echo 🔧 Starting Backend API...
start "Backend API" cmd /k "cd backend && echo 🔧 Backend API Server && echo ===================== && npm run dev"

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

REM Start Frontend Portfolio
echo 🎨 Starting Frontend Portfolio...
start "Frontend Portfolio" cmd /k "echo 🎨 Frontend Portfolio && echo ==================== && npm run dev"

REM Wait a moment for frontend to start
timeout /t 3 /nobreak >nul

REM Start Admin Panel
echo 🛠️  Starting Admin Panel...
start "Admin Panel" cmd /k "cd admin && echo 🛠️  Admin Panel && echo ============= && npm run dev"

echo.
echo ✅ All services are starting!
echo.
echo 🌐 Your applications will be available at:
echo    Frontend:    http://localhost:3000
echo    Admin Panel: http://localhost:3001
echo    Backend API: http://localhost:5000
echo.
echo 🔐 Admin Panel Login:
echo    Email:    <EMAIL>
echo    Password: legendhero
echo.
echo 💡 Tips:
echo    - Wait for all services to fully start (may take 1-2 minutes)
echo    - Check each terminal window for any errors
echo    - Use Ctrl+C in each terminal to stop services
echo.
echo Press any key to close this window...
pause >nul
