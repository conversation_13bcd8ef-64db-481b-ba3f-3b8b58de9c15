"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tsparticles-interaction-particles-links";
exports.ids = ["vendor-chunks/tsparticles-interaction-particles-links"];
exports.modules = {

/***/ "(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/CircleWarp.js":
/*!********************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-particles-links/esm/CircleWarp.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CircleWarp: () => (/* binding */ CircleWarp)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/Circle.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/Rectangle.js\");\n\nclass CircleWarp extends tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.Circle {\n    constructor(x, y, radius, canvasSize) {\n        super(x, y, radius);\n        this.canvasSize = canvasSize;\n        this.canvasSize = { ...canvasSize };\n    }\n    contains(point) {\n        const { width, height } = this.canvasSize;\n        const { x, y } = point;\n        return (super.contains(point) ||\n            super.contains({ x: x - width, y }) ||\n            super.contains({ x: x - width, y: y - height }) ||\n            super.contains({ x, y: y - height }));\n    }\n    intersects(range) {\n        if (super.intersects(range)) {\n            return true;\n        }\n        const rect = range, circle = range, newPos = {\n            x: range.position.x - this.canvasSize.width,\n            y: range.position.y - this.canvasSize.height,\n        };\n        if (circle.radius !== undefined) {\n            const biggerCircle = new tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.Circle(newPos.x, newPos.y, circle.radius * 2);\n            return super.intersects(biggerCircle);\n        }\n        else if (rect.size !== undefined) {\n            const rectSW = new tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.Rectangle(newPos.x, newPos.y, rect.size.width * 2, rect.size.height * 2);\n            return super.intersects(rectSW);\n        }\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/CircleWarp.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/LinkInstance.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-particles-links/esm/LinkInstance.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LinkInstance: () => (/* binding */ LinkInstance)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/ColorUtils.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/Utils.js\");\n\n\nclass LinkInstance {\n    constructor(container) {\n        this.container = container;\n        this._drawLinkLine = (p1, link) => {\n            const p1LinksOptions = p1.options.links;\n            if (!p1LinksOptions?.enable) {\n                return;\n            }\n            const container = this.container, options = container.actualOptions, p2 = link.destination, pos1 = p1.getPosition(), pos2 = p2.getPosition();\n            let opacity = link.opacity;\n            container.canvas.draw((ctx) => {\n                let colorLine;\n                const twinkle = p1.options.twinkle?.lines;\n                if (twinkle?.enable) {\n                    const twinkleFreq = twinkle.frequency, twinkleRgb = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.rangeColorToRgb)(twinkle.color), twinkling = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getRandom)() < twinkleFreq;\n                    if (twinkling && twinkleRgb) {\n                        colorLine = twinkleRgb;\n                        opacity = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getRangeValue)(twinkle.opacity);\n                    }\n                }\n                if (!colorLine) {\n                    const linkColor = p1LinksOptions.id !== undefined\n                        ? container.particles.linksColors.get(p1LinksOptions.id)\n                        : container.particles.linksColor;\n                    colorLine = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getLinkColor)(p1, p2, linkColor);\n                }\n                if (!colorLine) {\n                    return;\n                }\n                const width = p1.retina.linksWidth ?? 0, maxDistance = p1.retina.linksDistance ?? 0, { backgroundMask } = options;\n                (0,_Utils__WEBPACK_IMPORTED_MODULE_2__.drawLinkLine)({\n                    context: ctx,\n                    width,\n                    begin: pos1,\n                    end: pos2,\n                    maxDistance,\n                    canvasSize: container.canvas.size,\n                    links: p1LinksOptions,\n                    backgroundMask: backgroundMask,\n                    colorLine,\n                    opacity,\n                });\n            });\n        };\n        this._drawLinkTriangle = (p1, link1, link2) => {\n            const linksOptions = p1.options.links;\n            if (!linksOptions?.enable) {\n                return;\n            }\n            const triangleOptions = linksOptions.triangles;\n            if (!triangleOptions.enable) {\n                return;\n            }\n            const container = this.container, options = container.actualOptions, p2 = link1.destination, p3 = link2.destination, opacityTriangle = triangleOptions.opacity ?? (link1.opacity + link2.opacity) / 2;\n            if (opacityTriangle <= 0) {\n                return;\n            }\n            container.canvas.draw((ctx) => {\n                const pos1 = p1.getPosition(), pos2 = p2.getPosition(), pos3 = p3.getPosition(), linksDistance = p1.retina.linksDistance ?? 0;\n                if ((0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getDistance)(pos1, pos2) > linksDistance ||\n                    (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getDistance)(pos3, pos2) > linksDistance ||\n                    (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getDistance)(pos3, pos1) > linksDistance) {\n                    return;\n                }\n                let colorTriangle = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.rangeColorToRgb)(triangleOptions.color);\n                if (!colorTriangle) {\n                    const linkColor = linksOptions.id !== undefined\n                        ? container.particles.linksColors.get(linksOptions.id)\n                        : container.particles.linksColor;\n                    colorTriangle = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getLinkColor)(p1, p2, linkColor);\n                }\n                if (!colorTriangle) {\n                    return;\n                }\n                (0,_Utils__WEBPACK_IMPORTED_MODULE_2__.drawLinkTriangle)({\n                    context: ctx,\n                    pos1,\n                    pos2,\n                    pos3,\n                    backgroundMask: options.backgroundMask,\n                    colorTriangle,\n                    opacityTriangle,\n                });\n            });\n        };\n        this._drawTriangles = (options, p1, link, p1Links) => {\n            const p2 = link.destination;\n            if (!(options.links?.triangles.enable && p2.options.links?.triangles.enable)) {\n                return;\n            }\n            const vertices = p2.links?.filter((t) => {\n                const linkFreq = this._getLinkFrequency(p2, t.destination);\n                return (p2.options.links &&\n                    linkFreq <= p2.options.links.frequency &&\n                    p1Links.findIndex((l) => l.destination === t.destination) >= 0);\n            });\n            if (!vertices?.length) {\n                return;\n            }\n            for (const vertex of vertices) {\n                const p3 = vertex.destination, triangleFreq = this._getTriangleFrequency(p1, p2, p3);\n                if (triangleFreq > options.links.triangles.frequency) {\n                    continue;\n                }\n                this._drawLinkTriangle(p1, link, vertex);\n            }\n        };\n        this._getLinkFrequency = (p1, p2) => {\n            return (0,_Utils__WEBPACK_IMPORTED_MODULE_2__.setLinkFrequency)([p1, p2], this._freqs.links);\n        };\n        this._getTriangleFrequency = (p1, p2, p3) => {\n            return (0,_Utils__WEBPACK_IMPORTED_MODULE_2__.setLinkFrequency)([p1, p2, p3], this._freqs.triangles);\n        };\n        this._freqs = {\n            links: new Map(),\n            triangles: new Map(),\n        };\n    }\n    drawParticle(context, particle) {\n        const { links, options } = particle;\n        if (!links || links.length <= 0) {\n            return;\n        }\n        const p1Links = links.filter((l) => options.links && this._getLinkFrequency(particle, l.destination) <= options.links.frequency);\n        for (const link of p1Links) {\n            this._drawTriangles(options, particle, link, p1Links);\n            if (link.opacity > 0 && (particle.retina.linksWidth ?? 0) > 0) {\n                this._drawLinkLine(particle, link);\n            }\n        }\n    }\n    async init() {\n        this._freqs.links = new Map();\n        this._freqs.triangles = new Map();\n    }\n    particleCreated(particle) {\n        particle.links = [];\n        if (!particle.options.links) {\n            return;\n        }\n        const ratio = this.container.retina.pixelRatio, { retina } = particle, { distance, width } = particle.options.links;\n        retina.linksDistance = distance * ratio;\n        retina.linksWidth = width * ratio;\n    }\n    particleDestroyed(particle) {\n        particle.links = [];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tcGFydGljbGVzLWxpbmtzL2VzbS9MaW5rSW5zdGFuY2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUEyRztBQUNoQztBQUNwRTtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0VBQXdFLG1FQUFlLDZCQUE2Qiw2REFBUztBQUM3SDtBQUNBO0FBQ0Esa0NBQWtDLGlFQUFhO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyxnRUFBWTtBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBLHVHQUF1RyxpQkFBaUI7QUFDeEgsZ0JBQWdCLG9EQUFZO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQiwrREFBVztBQUMvQixvQkFBb0IsK0RBQVc7QUFDL0Isb0JBQW9CLCtEQUFXO0FBQy9CO0FBQ0E7QUFDQSxvQ0FBb0MsbUVBQWU7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0MsZ0VBQVk7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0Isd0RBQWdCO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQix3REFBZ0I7QUFDbkM7QUFDQTtBQUNBLG1CQUFtQix3REFBZ0I7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsaUJBQWlCO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwREFBMEQsU0FBUyxjQUFjLGtCQUFrQjtBQUNuRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1dHVyaXN0aWMtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL3RzcGFydGljbGVzLWludGVyYWN0aW9uLXBhcnRpY2xlcy1saW5rcy9lc20vTGlua0luc3RhbmNlLmpzPzIyNzciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0RGlzdGFuY2UsIGdldExpbmtDb2xvciwgZ2V0UmFuZG9tLCBnZXRSYW5nZVZhbHVlLCByYW5nZUNvbG9yVG9SZ2IsIH0gZnJvbSBcInRzcGFydGljbGVzLWVuZ2luZVwiO1xuaW1wb3J0IHsgZHJhd0xpbmtMaW5lLCBkcmF3TGlua1RyaWFuZ2xlLCBzZXRMaW5rRnJlcXVlbmN5IH0gZnJvbSBcIi4vVXRpbHNcIjtcbmV4cG9ydCBjbGFzcyBMaW5rSW5zdGFuY2Uge1xuICAgIGNvbnN0cnVjdG9yKGNvbnRhaW5lcikge1xuICAgICAgICB0aGlzLmNvbnRhaW5lciA9IGNvbnRhaW5lcjtcbiAgICAgICAgdGhpcy5fZHJhd0xpbmtMaW5lID0gKHAxLCBsaW5rKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBwMUxpbmtzT3B0aW9ucyA9IHAxLm9wdGlvbnMubGlua3M7XG4gICAgICAgICAgICBpZiAoIXAxTGlua3NPcHRpb25zPy5lbmFibGUpIHtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBjb250YWluZXIgPSB0aGlzLmNvbnRhaW5lciwgb3B0aW9ucyA9IGNvbnRhaW5lci5hY3R1YWxPcHRpb25zLCBwMiA9IGxpbmsuZGVzdGluYXRpb24sIHBvczEgPSBwMS5nZXRQb3NpdGlvbigpLCBwb3MyID0gcDIuZ2V0UG9zaXRpb24oKTtcbiAgICAgICAgICAgIGxldCBvcGFjaXR5ID0gbGluay5vcGFjaXR5O1xuICAgICAgICAgICAgY29udGFpbmVyLmNhbnZhcy5kcmF3KChjdHgpID0+IHtcbiAgICAgICAgICAgICAgICBsZXQgY29sb3JMaW5lO1xuICAgICAgICAgICAgICAgIGNvbnN0IHR3aW5rbGUgPSBwMS5vcHRpb25zLnR3aW5rbGU/LmxpbmVzO1xuICAgICAgICAgICAgICAgIGlmICh0d2lua2xlPy5lbmFibGUpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdHdpbmtsZUZyZXEgPSB0d2lua2xlLmZyZXF1ZW5jeSwgdHdpbmtsZVJnYiA9IHJhbmdlQ29sb3JUb1JnYih0d2lua2xlLmNvbG9yKSwgdHdpbmtsaW5nID0gZ2V0UmFuZG9tKCkgPCB0d2lua2xlRnJlcTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHR3aW5rbGluZyAmJiB0d2lua2xlUmdiKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb2xvckxpbmUgPSB0d2lua2xlUmdiO1xuICAgICAgICAgICAgICAgICAgICAgICAgb3BhY2l0eSA9IGdldFJhbmdlVmFsdWUodHdpbmtsZS5vcGFjaXR5KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoIWNvbG9yTGluZSkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBsaW5rQ29sb3IgPSBwMUxpbmtzT3B0aW9ucy5pZCAhPT0gdW5kZWZpbmVkXG4gICAgICAgICAgICAgICAgICAgICAgICA/IGNvbnRhaW5lci5wYXJ0aWNsZXMubGlua3NDb2xvcnMuZ2V0KHAxTGlua3NPcHRpb25zLmlkKVxuICAgICAgICAgICAgICAgICAgICAgICAgOiBjb250YWluZXIucGFydGljbGVzLmxpbmtzQ29sb3I7XG4gICAgICAgICAgICAgICAgICAgIGNvbG9yTGluZSA9IGdldExpbmtDb2xvcihwMSwgcDIsIGxpbmtDb2xvcik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmICghY29sb3JMaW5lKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3Qgd2lkdGggPSBwMS5yZXRpbmEubGlua3NXaWR0aCA/PyAwLCBtYXhEaXN0YW5jZSA9IHAxLnJldGluYS5saW5rc0Rpc3RhbmNlID8/IDAsIHsgYmFja2dyb3VuZE1hc2sgfSA9IG9wdGlvbnM7XG4gICAgICAgICAgICAgICAgZHJhd0xpbmtMaW5lKHtcbiAgICAgICAgICAgICAgICAgICAgY29udGV4dDogY3R4LFxuICAgICAgICAgICAgICAgICAgICB3aWR0aCxcbiAgICAgICAgICAgICAgICAgICAgYmVnaW46IHBvczEsXG4gICAgICAgICAgICAgICAgICAgIGVuZDogcG9zMixcbiAgICAgICAgICAgICAgICAgICAgbWF4RGlzdGFuY2UsXG4gICAgICAgICAgICAgICAgICAgIGNhbnZhc1NpemU6IGNvbnRhaW5lci5jYW52YXMuc2l6ZSxcbiAgICAgICAgICAgICAgICAgICAgbGlua3M6IHAxTGlua3NPcHRpb25zLFxuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kTWFzazogYmFja2dyb3VuZE1hc2ssXG4gICAgICAgICAgICAgICAgICAgIGNvbG9yTGluZSxcbiAgICAgICAgICAgICAgICAgICAgb3BhY2l0eSxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9O1xuICAgICAgICB0aGlzLl9kcmF3TGlua1RyaWFuZ2xlID0gKHAxLCBsaW5rMSwgbGluazIpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGxpbmtzT3B0aW9ucyA9IHAxLm9wdGlvbnMubGlua3M7XG4gICAgICAgICAgICBpZiAoIWxpbmtzT3B0aW9ucz8uZW5hYmxlKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgdHJpYW5nbGVPcHRpb25zID0gbGlua3NPcHRpb25zLnRyaWFuZ2xlcztcbiAgICAgICAgICAgIGlmICghdHJpYW5nbGVPcHRpb25zLmVuYWJsZSkge1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IGNvbnRhaW5lciA9IHRoaXMuY29udGFpbmVyLCBvcHRpb25zID0gY29udGFpbmVyLmFjdHVhbE9wdGlvbnMsIHAyID0gbGluazEuZGVzdGluYXRpb24sIHAzID0gbGluazIuZGVzdGluYXRpb24sIG9wYWNpdHlUcmlhbmdsZSA9IHRyaWFuZ2xlT3B0aW9ucy5vcGFjaXR5ID8/IChsaW5rMS5vcGFjaXR5ICsgbGluazIub3BhY2l0eSkgLyAyO1xuICAgICAgICAgICAgaWYgKG9wYWNpdHlUcmlhbmdsZSA8PSAwKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29udGFpbmVyLmNhbnZhcy5kcmF3KChjdHgpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBwb3MxID0gcDEuZ2V0UG9zaXRpb24oKSwgcG9zMiA9IHAyLmdldFBvc2l0aW9uKCksIHBvczMgPSBwMy5nZXRQb3NpdGlvbigpLCBsaW5rc0Rpc3RhbmNlID0gcDEucmV0aW5hLmxpbmtzRGlzdGFuY2UgPz8gMDtcbiAgICAgICAgICAgICAgICBpZiAoZ2V0RGlzdGFuY2UocG9zMSwgcG9zMikgPiBsaW5rc0Rpc3RhbmNlIHx8XG4gICAgICAgICAgICAgICAgICAgIGdldERpc3RhbmNlKHBvczMsIHBvczIpID4gbGlua3NEaXN0YW5jZSB8fFxuICAgICAgICAgICAgICAgICAgICBnZXREaXN0YW5jZShwb3MzLCBwb3MxKSA+IGxpbmtzRGlzdGFuY2UpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBsZXQgY29sb3JUcmlhbmdsZSA9IHJhbmdlQ29sb3JUb1JnYih0cmlhbmdsZU9wdGlvbnMuY29sb3IpO1xuICAgICAgICAgICAgICAgIGlmICghY29sb3JUcmlhbmdsZSkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBsaW5rQ29sb3IgPSBsaW5rc09wdGlvbnMuaWQgIT09IHVuZGVmaW5lZFxuICAgICAgICAgICAgICAgICAgICAgICAgPyBjb250YWluZXIucGFydGljbGVzLmxpbmtzQ29sb3JzLmdldChsaW5rc09wdGlvbnMuaWQpXG4gICAgICAgICAgICAgICAgICAgICAgICA6IGNvbnRhaW5lci5wYXJ0aWNsZXMubGlua3NDb2xvcjtcbiAgICAgICAgICAgICAgICAgICAgY29sb3JUcmlhbmdsZSA9IGdldExpbmtDb2xvcihwMSwgcDIsIGxpbmtDb2xvcik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmICghY29sb3JUcmlhbmdsZSkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGRyYXdMaW5rVHJpYW5nbGUoe1xuICAgICAgICAgICAgICAgICAgICBjb250ZXh0OiBjdHgsXG4gICAgICAgICAgICAgICAgICAgIHBvczEsXG4gICAgICAgICAgICAgICAgICAgIHBvczIsXG4gICAgICAgICAgICAgICAgICAgIHBvczMsXG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRNYXNrOiBvcHRpb25zLmJhY2tncm91bmRNYXNrLFxuICAgICAgICAgICAgICAgICAgICBjb2xvclRyaWFuZ2xlLFxuICAgICAgICAgICAgICAgICAgICBvcGFjaXR5VHJpYW5nbGUsXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5fZHJhd1RyaWFuZ2xlcyA9IChvcHRpb25zLCBwMSwgbGluaywgcDFMaW5rcykgPT4ge1xuICAgICAgICAgICAgY29uc3QgcDIgPSBsaW5rLmRlc3RpbmF0aW9uO1xuICAgICAgICAgICAgaWYgKCEob3B0aW9ucy5saW5rcz8udHJpYW5nbGVzLmVuYWJsZSAmJiBwMi5vcHRpb25zLmxpbmtzPy50cmlhbmdsZXMuZW5hYmxlKSkge1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHZlcnRpY2VzID0gcDIubGlua3M/LmZpbHRlcigodCkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IGxpbmtGcmVxID0gdGhpcy5fZ2V0TGlua0ZyZXF1ZW5jeShwMiwgdC5kZXN0aW5hdGlvbik7XG4gICAgICAgICAgICAgICAgcmV0dXJuIChwMi5vcHRpb25zLmxpbmtzICYmXG4gICAgICAgICAgICAgICAgICAgIGxpbmtGcmVxIDw9IHAyLm9wdGlvbnMubGlua3MuZnJlcXVlbmN5ICYmXG4gICAgICAgICAgICAgICAgICAgIHAxTGlua3MuZmluZEluZGV4KChsKSA9PiBsLmRlc3RpbmF0aW9uID09PSB0LmRlc3RpbmF0aW9uKSA+PSAwKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgaWYgKCF2ZXJ0aWNlcz8ubGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZm9yIChjb25zdCB2ZXJ0ZXggb2YgdmVydGljZXMpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBwMyA9IHZlcnRleC5kZXN0aW5hdGlvbiwgdHJpYW5nbGVGcmVxID0gdGhpcy5fZ2V0VHJpYW5nbGVGcmVxdWVuY3kocDEsIHAyLCBwMyk7XG4gICAgICAgICAgICAgICAgaWYgKHRyaWFuZ2xlRnJlcSA+IG9wdGlvbnMubGlua3MudHJpYW5nbGVzLmZyZXF1ZW5jeSkge1xuICAgICAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdGhpcy5fZHJhd0xpbmtUcmlhbmdsZShwMSwgbGluaywgdmVydGV4KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5fZ2V0TGlua0ZyZXF1ZW5jeSA9IChwMSwgcDIpID0+IHtcbiAgICAgICAgICAgIHJldHVybiBzZXRMaW5rRnJlcXVlbmN5KFtwMSwgcDJdLCB0aGlzLl9mcmVxcy5saW5rcyk7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMuX2dldFRyaWFuZ2xlRnJlcXVlbmN5ID0gKHAxLCBwMiwgcDMpID0+IHtcbiAgICAgICAgICAgIHJldHVybiBzZXRMaW5rRnJlcXVlbmN5KFtwMSwgcDIsIHAzXSwgdGhpcy5fZnJlcXMudHJpYW5nbGVzKTtcbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5fZnJlcXMgPSB7XG4gICAgICAgICAgICBsaW5rczogbmV3IE1hcCgpLFxuICAgICAgICAgICAgdHJpYW5nbGVzOiBuZXcgTWFwKCksXG4gICAgICAgIH07XG4gICAgfVxuICAgIGRyYXdQYXJ0aWNsZShjb250ZXh0LCBwYXJ0aWNsZSkge1xuICAgICAgICBjb25zdCB7IGxpbmtzLCBvcHRpb25zIH0gPSBwYXJ0aWNsZTtcbiAgICAgICAgaWYgKCFsaW5rcyB8fCBsaW5rcy5sZW5ndGggPD0gMCkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHAxTGlua3MgPSBsaW5rcy5maWx0ZXIoKGwpID0+IG9wdGlvbnMubGlua3MgJiYgdGhpcy5fZ2V0TGlua0ZyZXF1ZW5jeShwYXJ0aWNsZSwgbC5kZXN0aW5hdGlvbikgPD0gb3B0aW9ucy5saW5rcy5mcmVxdWVuY3kpO1xuICAgICAgICBmb3IgKGNvbnN0IGxpbmsgb2YgcDFMaW5rcykge1xuICAgICAgICAgICAgdGhpcy5fZHJhd1RyaWFuZ2xlcyhvcHRpb25zLCBwYXJ0aWNsZSwgbGluaywgcDFMaW5rcyk7XG4gICAgICAgICAgICBpZiAobGluay5vcGFjaXR5ID4gMCAmJiAocGFydGljbGUucmV0aW5hLmxpbmtzV2lkdGggPz8gMCkgPiAwKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5fZHJhd0xpbmtMaW5lKHBhcnRpY2xlLCBsaW5rKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBhc3luYyBpbml0KCkge1xuICAgICAgICB0aGlzLl9mcmVxcy5saW5rcyA9IG5ldyBNYXAoKTtcbiAgICAgICAgdGhpcy5fZnJlcXMudHJpYW5nbGVzID0gbmV3IE1hcCgpO1xuICAgIH1cbiAgICBwYXJ0aWNsZUNyZWF0ZWQocGFydGljbGUpIHtcbiAgICAgICAgcGFydGljbGUubGlua3MgPSBbXTtcbiAgICAgICAgaWYgKCFwYXJ0aWNsZS5vcHRpb25zLmxpbmtzKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgcmF0aW8gPSB0aGlzLmNvbnRhaW5lci5yZXRpbmEucGl4ZWxSYXRpbywgeyByZXRpbmEgfSA9IHBhcnRpY2xlLCB7IGRpc3RhbmNlLCB3aWR0aCB9ID0gcGFydGljbGUub3B0aW9ucy5saW5rcztcbiAgICAgICAgcmV0aW5hLmxpbmtzRGlzdGFuY2UgPSBkaXN0YW5jZSAqIHJhdGlvO1xuICAgICAgICByZXRpbmEubGlua3NXaWR0aCA9IHdpZHRoICogcmF0aW87XG4gICAgfVxuICAgIHBhcnRpY2xlRGVzdHJveWVkKHBhcnRpY2xlKSB7XG4gICAgICAgIHBhcnRpY2xlLmxpbmtzID0gW107XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/LinkInstance.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/Linker.js":
/*!****************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-particles-links/esm/Linker.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Linker: () => (/* binding */ Linker)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/ParticlesInteractorBase.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/ColorUtils.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/Circle.js\");\n/* harmony import */ var _CircleWarp__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CircleWarp */ \"(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/CircleWarp.js\");\n/* harmony import */ var _Options_Classes_Links__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Options/Classes/Links */ \"(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/Options/Classes/Links.js\");\n\n\n\nfunction getLinkDistance(pos1, pos2, optDistance, canvasSize, warp) {\n    const { dx, dy, distance } = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getDistances)(pos1, pos2);\n    if (!warp || distance <= optDistance) {\n        return distance;\n    }\n    const absDiffs = {\n        x: Math.abs(dx),\n        y: Math.abs(dy),\n    }, warpDistances = {\n        x: Math.min(absDiffs.x, canvasSize.width - absDiffs.x),\n        y: Math.min(absDiffs.y, canvasSize.height - absDiffs.y),\n    };\n    return Math.sqrt(warpDistances.x ** 2 + warpDistances.y ** 2);\n}\nclass Linker extends tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.ParticlesInteractorBase {\n    constructor(container) {\n        super(container);\n        this._setColor = (p1) => {\n            if (!p1.options.links) {\n                return;\n            }\n            const container = this.linkContainer, linksOptions = p1.options.links;\n            let linkColor = linksOptions.id === undefined\n                ? container.particles.linksColor\n                : container.particles.linksColors.get(linksOptions.id);\n            if (linkColor) {\n                return;\n            }\n            const optColor = linksOptions.color;\n            linkColor = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.getLinkRandomColor)(optColor, linksOptions.blink, linksOptions.consent);\n            if (linksOptions.id === undefined) {\n                container.particles.linksColor = linkColor;\n            }\n            else {\n                container.particles.linksColors.set(linksOptions.id, linkColor);\n            }\n        };\n        this.linkContainer = container;\n    }\n    clear() {\n    }\n    init() {\n        this.linkContainer.particles.linksColor = undefined;\n        this.linkContainer.particles.linksColors = new Map();\n    }\n    async interact(p1) {\n        if (!p1.options.links) {\n            return;\n        }\n        p1.links = [];\n        const pos1 = p1.getPosition(), container = this.container, canvasSize = container.canvas.size;\n        if (pos1.x < 0 || pos1.y < 0 || pos1.x > canvasSize.width || pos1.y > canvasSize.height) {\n            return;\n        }\n        const linkOpt1 = p1.options.links, optOpacity = linkOpt1.opacity, optDistance = p1.retina.linksDistance ?? 0, warp = linkOpt1.warp, range = warp\n            ? new _CircleWarp__WEBPACK_IMPORTED_MODULE_3__.CircleWarp(pos1.x, pos1.y, optDistance, canvasSize)\n            : new tsparticles_engine__WEBPACK_IMPORTED_MODULE_4__.Circle(pos1.x, pos1.y, optDistance), query = container.particles.quadTree.query(range);\n        for (const p2 of query) {\n            const linkOpt2 = p2.options.links;\n            if (p1 === p2 ||\n                !linkOpt2?.enable ||\n                linkOpt1.id !== linkOpt2.id ||\n                p2.spawning ||\n                p2.destroyed ||\n                !p2.links ||\n                p1.links.some((t) => t.destination === p2) ||\n                p2.links.some((t) => t.destination === p1)) {\n                continue;\n            }\n            const pos2 = p2.getPosition();\n            if (pos2.x < 0 || pos2.y < 0 || pos2.x > canvasSize.width || pos2.y > canvasSize.height) {\n                continue;\n            }\n            const distance = getLinkDistance(pos1, pos2, optDistance, canvasSize, warp && linkOpt2.warp);\n            if (distance > optDistance) {\n                continue;\n            }\n            const opacityLine = (1 - distance / optDistance) * optOpacity;\n            this._setColor(p1);\n            p1.links.push({\n                destination: p2,\n                opacity: opacityLine,\n            });\n        }\n    }\n    isEnabled(particle) {\n        return !!particle.options.links?.enable;\n    }\n    loadParticlesOptions(options, ...sources) {\n        if (!options.links) {\n            options.links = new _Options_Classes_Links__WEBPACK_IMPORTED_MODULE_5__.Links();\n        }\n        for (const source of sources) {\n            options.links.load(source?.links ?? source?.lineLinked ?? source?.line_linked);\n        }\n    }\n    reset() {\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/Linker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/Options/Classes/Links.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-particles-links/esm/Options/Classes/Links.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Links: () => (/* binding */ Links)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Options/Classes/OptionsColor.js\");\n/* harmony import */ var _LinksShadow__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./LinksShadow */ \"(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/Options/Classes/LinksShadow.js\");\n/* harmony import */ var _LinksTriangle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./LinksTriangle */ \"(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/Options/Classes/LinksTriangle.js\");\n\n\n\nclass Links {\n    constructor() {\n        this.blink = false;\n        this.color = new tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OptionsColor();\n        this.color.value = \"#fff\";\n        this.consent = false;\n        this.distance = 100;\n        this.enable = false;\n        this.frequency = 1;\n        this.opacity = 1;\n        this.shadow = new _LinksShadow__WEBPACK_IMPORTED_MODULE_1__.LinksShadow();\n        this.triangles = new _LinksTriangle__WEBPACK_IMPORTED_MODULE_2__.LinksTriangle();\n        this.width = 1;\n        this.warp = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.id !== undefined) {\n            this.id = data.id;\n        }\n        if (data.blink !== undefined) {\n            this.blink = data.blink;\n        }\n        this.color = tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OptionsColor.create(this.color, data.color);\n        if (data.consent !== undefined) {\n            this.consent = data.consent;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.frequency !== undefined) {\n            this.frequency = data.frequency;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n        this.shadow.load(data.shadow);\n        this.triangles.load(data.triangles);\n        if (data.width !== undefined) {\n            this.width = data.width;\n        }\n        if (data.warp !== undefined) {\n            this.warp = data.warp;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tcGFydGljbGVzLWxpbmtzL2VzbS9PcHRpb25zL0NsYXNzZXMvTGlua3MuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFrRDtBQUNOO0FBQ0k7QUFDekM7QUFDUDtBQUNBO0FBQ0EseUJBQXlCLDREQUFZO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQixxREFBVztBQUNyQyw2QkFBNkIseURBQWE7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsNERBQVk7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mdXR1cmlzdGljLXBvcnRmb2xpby8uL25vZGVfbW9kdWxlcy90c3BhcnRpY2xlcy1pbnRlcmFjdGlvbi1wYXJ0aWNsZXMtbGlua3MvZXNtL09wdGlvbnMvQ2xhc3Nlcy9MaW5rcy5qcz9mOTUxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE9wdGlvbnNDb2xvciB9IGZyb20gXCJ0c3BhcnRpY2xlcy1lbmdpbmVcIjtcbmltcG9ydCB7IExpbmtzU2hhZG93IH0gZnJvbSBcIi4vTGlua3NTaGFkb3dcIjtcbmltcG9ydCB7IExpbmtzVHJpYW5nbGUgfSBmcm9tIFwiLi9MaW5rc1RyaWFuZ2xlXCI7XG5leHBvcnQgY2xhc3MgTGlua3Mge1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICB0aGlzLmJsaW5rID0gZmFsc2U7XG4gICAgICAgIHRoaXMuY29sb3IgPSBuZXcgT3B0aW9uc0NvbG9yKCk7XG4gICAgICAgIHRoaXMuY29sb3IudmFsdWUgPSBcIiNmZmZcIjtcbiAgICAgICAgdGhpcy5jb25zZW50ID0gZmFsc2U7XG4gICAgICAgIHRoaXMuZGlzdGFuY2UgPSAxMDA7XG4gICAgICAgIHRoaXMuZW5hYmxlID0gZmFsc2U7XG4gICAgICAgIHRoaXMuZnJlcXVlbmN5ID0gMTtcbiAgICAgICAgdGhpcy5vcGFjaXR5ID0gMTtcbiAgICAgICAgdGhpcy5zaGFkb3cgPSBuZXcgTGlua3NTaGFkb3coKTtcbiAgICAgICAgdGhpcy50cmlhbmdsZXMgPSBuZXcgTGlua3NUcmlhbmdsZSgpO1xuICAgICAgICB0aGlzLndpZHRoID0gMTtcbiAgICAgICAgdGhpcy53YXJwID0gZmFsc2U7XG4gICAgfVxuICAgIGxvYWQoZGF0YSkge1xuICAgICAgICBpZiAoIWRhdGEpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5pZCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aGlzLmlkID0gZGF0YS5pZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5ibGluayAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aGlzLmJsaW5rID0gZGF0YS5ibGluaztcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmNvbG9yID0gT3B0aW9uc0NvbG9yLmNyZWF0ZSh0aGlzLmNvbG9yLCBkYXRhLmNvbG9yKTtcbiAgICAgICAgaWYgKGRhdGEuY29uc2VudCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aGlzLmNvbnNlbnQgPSBkYXRhLmNvbnNlbnQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGRhdGEuZGlzdGFuY2UgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5kaXN0YW5jZSA9IGRhdGEuZGlzdGFuY2U7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGRhdGEuZW5hYmxlICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHRoaXMuZW5hYmxlID0gZGF0YS5lbmFibGU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGRhdGEuZnJlcXVlbmN5ICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHRoaXMuZnJlcXVlbmN5ID0gZGF0YS5mcmVxdWVuY3k7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGRhdGEub3BhY2l0eSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aGlzLm9wYWNpdHkgPSBkYXRhLm9wYWNpdHk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5zaGFkb3cubG9hZChkYXRhLnNoYWRvdyk7XG4gICAgICAgIHRoaXMudHJpYW5nbGVzLmxvYWQoZGF0YS50cmlhbmdsZXMpO1xuICAgICAgICBpZiAoZGF0YS53aWR0aCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aGlzLndpZHRoID0gZGF0YS53aWR0aDtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS53YXJwICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHRoaXMud2FycCA9IGRhdGEud2FycDtcbiAgICAgICAgfVxuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/Options/Classes/Links.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/Options/Classes/LinksShadow.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-particles-links/esm/Options/Classes/LinksShadow.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LinksShadow: () => (/* binding */ LinksShadow)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Options/Classes/OptionsColor.js\");\n\nclass LinksShadow {\n    constructor() {\n        this.blur = 5;\n        this.color = new tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OptionsColor();\n        this.color.value = \"#000\";\n        this.enable = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.blur !== undefined) {\n            this.blur = data.blur;\n        }\n        this.color = tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OptionsColor.create(this.color, data.color);\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tcGFydGljbGVzLWxpbmtzL2VzbS9PcHRpb25zL0NsYXNzZXMvTGlua3NTaGFkb3cuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0Q7QUFDM0M7QUFDUDtBQUNBO0FBQ0EseUJBQXlCLDREQUFZO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLDREQUFZO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mdXR1cmlzdGljLXBvcnRmb2xpby8uL25vZGVfbW9kdWxlcy90c3BhcnRpY2xlcy1pbnRlcmFjdGlvbi1wYXJ0aWNsZXMtbGlua3MvZXNtL09wdGlvbnMvQ2xhc3Nlcy9MaW5rc1NoYWRvdy5qcz9kODZkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE9wdGlvbnNDb2xvciB9IGZyb20gXCJ0c3BhcnRpY2xlcy1lbmdpbmVcIjtcbmV4cG9ydCBjbGFzcyBMaW5rc1NoYWRvdyB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHRoaXMuYmx1ciA9IDU7XG4gICAgICAgIHRoaXMuY29sb3IgPSBuZXcgT3B0aW9uc0NvbG9yKCk7XG4gICAgICAgIHRoaXMuY29sb3IudmFsdWUgPSBcIiMwMDBcIjtcbiAgICAgICAgdGhpcy5lbmFibGUgPSBmYWxzZTtcbiAgICB9XG4gICAgbG9hZChkYXRhKSB7XG4gICAgICAgIGlmICghZGF0YSkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGlmIChkYXRhLmJsdXIgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5ibHVyID0gZGF0YS5ibHVyO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuY29sb3IgPSBPcHRpb25zQ29sb3IuY3JlYXRlKHRoaXMuY29sb3IsIGRhdGEuY29sb3IpO1xuICAgICAgICBpZiAoZGF0YS5lbmFibGUgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5lbmFibGUgPSBkYXRhLmVuYWJsZTtcbiAgICAgICAgfVxuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/Options/Classes/LinksShadow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/Options/Classes/LinksTriangle.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-particles-links/esm/Options/Classes/LinksTriangle.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LinksTriangle: () => (/* binding */ LinksTriangle)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Options/Classes/OptionsColor.js\");\n\nclass LinksTriangle {\n    constructor() {\n        this.enable = false;\n        this.frequency = 1;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.color !== undefined) {\n            this.color = tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OptionsColor.create(this.color, data.color);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.frequency !== undefined) {\n            this.frequency = data.frequency;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tcGFydGljbGVzLWxpbmtzL2VzbS9PcHRpb25zL0NsYXNzZXMvTGlua3NUcmlhbmdsZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrRDtBQUMzQztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5Qiw0REFBWTtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mdXR1cmlzdGljLXBvcnRmb2xpby8uL25vZGVfbW9kdWxlcy90c3BhcnRpY2xlcy1pbnRlcmFjdGlvbi1wYXJ0aWNsZXMtbGlua3MvZXNtL09wdGlvbnMvQ2xhc3Nlcy9MaW5rc1RyaWFuZ2xlLmpzPzg5OTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgT3B0aW9uc0NvbG9yIH0gZnJvbSBcInRzcGFydGljbGVzLWVuZ2luZVwiO1xuZXhwb3J0IGNsYXNzIExpbmtzVHJpYW5nbGUge1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICB0aGlzLmVuYWJsZSA9IGZhbHNlO1xuICAgICAgICB0aGlzLmZyZXF1ZW5jeSA9IDE7XG4gICAgfVxuICAgIGxvYWQoZGF0YSkge1xuICAgICAgICBpZiAoIWRhdGEpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5jb2xvciAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aGlzLmNvbG9yID0gT3B0aW9uc0NvbG9yLmNyZWF0ZSh0aGlzLmNvbG9yLCBkYXRhLmNvbG9yKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5lbmFibGUgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5lbmFibGUgPSBkYXRhLmVuYWJsZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5mcmVxdWVuY3kgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5mcmVxdWVuY3kgPSBkYXRhLmZyZXF1ZW5jeTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5vcGFjaXR5ICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHRoaXMub3BhY2l0eSA9IGRhdGEub3BhY2l0eTtcbiAgICAgICAgfVxuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/Options/Classes/LinksTriangle.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/Utils.js":
/*!***************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-particles-links/esm/Utils.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   drawLinkLine: () => (/* binding */ drawLinkLine),\n/* harmony export */   drawLinkTriangle: () => (/* binding */ drawLinkTriangle),\n/* harmony export */   getLinkKey: () => (/* binding */ getLinkKey),\n/* harmony export */   setLinkFrequency: () => (/* binding */ setLinkFrequency)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/CanvasUtils.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/ColorUtils.js\");\n\nfunction drawLinkLine(params) {\n    let drawn = false;\n    const { begin, end, maxDistance, context, canvasSize, width, backgroundMask, colorLine, opacity, links } = params;\n    if ((0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getDistance)(begin, end) <= maxDistance) {\n        (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.drawLine)(context, begin, end);\n        drawn = true;\n    }\n    else if (links.warp) {\n        let pi1;\n        let pi2;\n        const endNE = {\n            x: end.x - canvasSize.width,\n            y: end.y,\n        };\n        const d1 = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getDistances)(begin, endNE);\n        if (d1.distance <= maxDistance) {\n            const yi = begin.y - (d1.dy / d1.dx) * begin.x;\n            pi1 = { x: 0, y: yi };\n            pi2 = { x: canvasSize.width, y: yi };\n        }\n        else {\n            const endSW = {\n                x: end.x,\n                y: end.y - canvasSize.height,\n            };\n            const d2 = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getDistances)(begin, endSW);\n            if (d2.distance <= maxDistance) {\n                const yi = begin.y - (d2.dy / d2.dx) * begin.x;\n                const xi = -yi / (d2.dy / d2.dx);\n                pi1 = { x: xi, y: 0 };\n                pi2 = { x: xi, y: canvasSize.height };\n            }\n            else {\n                const endSE = {\n                    x: end.x - canvasSize.width,\n                    y: end.y - canvasSize.height,\n                };\n                const d3 = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getDistances)(begin, endSE);\n                if (d3.distance <= maxDistance) {\n                    const yi = begin.y - (d3.dy / d3.dx) * begin.x;\n                    const xi = -yi / (d3.dy / d3.dx);\n                    pi1 = { x: xi, y: yi };\n                    pi2 = { x: pi1.x + canvasSize.width, y: pi1.y + canvasSize.height };\n                }\n            }\n        }\n        if (pi1 && pi2) {\n            (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.drawLine)(context, begin, pi1);\n            (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.drawLine)(context, end, pi2);\n            drawn = true;\n        }\n    }\n    if (!drawn) {\n        return;\n    }\n    context.lineWidth = width;\n    if (backgroundMask.enable) {\n        context.globalCompositeOperation = backgroundMask.composite;\n    }\n    context.strokeStyle = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.getStyleFromRgb)(colorLine, opacity);\n    const { shadow } = links;\n    if (shadow.enable) {\n        const shadowColor = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.rangeColorToRgb)(shadow.color);\n        if (shadowColor) {\n            context.shadowBlur = shadow.blur;\n            context.shadowColor = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.getStyleFromRgb)(shadowColor);\n        }\n    }\n    context.stroke();\n}\nfunction drawLinkTriangle(params) {\n    const { context, pos1, pos2, pos3, backgroundMask, colorTriangle, opacityTriangle } = params;\n    (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.drawTriangle)(context, pos1, pos2, pos3);\n    if (backgroundMask.enable) {\n        context.globalCompositeOperation = backgroundMask.composite;\n    }\n    context.fillStyle = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.getStyleFromRgb)(colorTriangle, opacityTriangle);\n    context.fill();\n}\nfunction getLinkKey(ids) {\n    ids.sort((a, b) => a - b);\n    return ids.join(\"_\");\n}\nfunction setLinkFrequency(particles, dictionary) {\n    const key = getLinkKey(particles.map((t) => t.id));\n    let res = dictionary.get(key);\n    if (res === undefined) {\n        res = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)();\n        dictionary.set(key, res);\n    }\n    return res;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/Utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/index.js":
/*!***************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-particles-links/esm/index.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Links: () => (/* reexport safe */ _Options_Classes_Links__WEBPACK_IMPORTED_MODULE_2__.Links),\n/* harmony export */   LinksShadow: () => (/* reexport safe */ _Options_Classes_LinksShadow__WEBPACK_IMPORTED_MODULE_3__.LinksShadow),\n/* harmony export */   LinksTriangle: () => (/* reexport safe */ _Options_Classes_LinksTriangle__WEBPACK_IMPORTED_MODULE_4__.LinksTriangle),\n/* harmony export */   loadParticlesLinksInteraction: () => (/* binding */ loadParticlesLinksInteraction)\n/* harmony export */ });\n/* harmony import */ var _interaction__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interaction */ \"(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/interaction.js\");\n/* harmony import */ var _plugin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./plugin */ \"(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/plugin.js\");\n/* harmony import */ var _Options_Classes_Links__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Options/Classes/Links */ \"(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/Options/Classes/Links.js\");\n/* harmony import */ var _Options_Classes_LinksShadow__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Options/Classes/LinksShadow */ \"(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/Options/Classes/LinksShadow.js\");\n/* harmony import */ var _Options_Classes_LinksTriangle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Options/Classes/LinksTriangle */ \"(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/Options/Classes/LinksTriangle.js\");\n\n\nasync function loadParticlesLinksInteraction(engine, refresh = true) {\n    await (0,_interaction__WEBPACK_IMPORTED_MODULE_0__.loadLinksInteraction)(engine, refresh);\n    await (0,_plugin__WEBPACK_IMPORTED_MODULE_1__.loadLinksPlugin)(engine, refresh);\n}\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tcGFydGljbGVzLWxpbmtzL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBcUQ7QUFDVjtBQUNwQztBQUNQLFVBQVUsa0VBQW9CO0FBQzlCLFVBQVUsd0RBQWU7QUFDekI7QUFDd0M7QUFDTTtBQUNFO0FBQ0o7QUFDTTtBQUNFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnV0dXJpc3RpYy1wb3J0Zm9saW8vLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tcGFydGljbGVzLWxpbmtzL2VzbS9pbmRleC5qcz9jMGJmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGxvYWRMaW5rc0ludGVyYWN0aW9uIH0gZnJvbSBcIi4vaW50ZXJhY3Rpb25cIjtcbmltcG9ydCB7IGxvYWRMaW5rc1BsdWdpbiB9IGZyb20gXCIuL3BsdWdpblwiO1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGxvYWRQYXJ0aWNsZXNMaW5rc0ludGVyYWN0aW9uKGVuZ2luZSwgcmVmcmVzaCA9IHRydWUpIHtcbiAgICBhd2FpdCBsb2FkTGlua3NJbnRlcmFjdGlvbihlbmdpbmUsIHJlZnJlc2gpO1xuICAgIGF3YWl0IGxvYWRMaW5rc1BsdWdpbihlbmdpbmUsIHJlZnJlc2gpO1xufVxuZXhwb3J0ICogZnJvbSBcIi4vT3B0aW9ucy9DbGFzc2VzL0xpbmtzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9PcHRpb25zL0NsYXNzZXMvTGlua3NTaGFkb3dcIjtcbmV4cG9ydCAqIGZyb20gXCIuL09wdGlvbnMvQ2xhc3Nlcy9MaW5rc1RyaWFuZ2xlXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9PcHRpb25zL0ludGVyZmFjZXMvSUxpbmtzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9PcHRpb25zL0ludGVyZmFjZXMvSUxpbmtzU2hhZG93XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9PcHRpb25zL0ludGVyZmFjZXMvSUxpbmtzVHJpYW5nbGVcIjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/interaction.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-particles-links/esm/interaction.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadLinksInteraction: () => (/* binding */ loadLinksInteraction)\n/* harmony export */ });\n/* harmony import */ var _Linker__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Linker */ \"(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/Linker.js\");\n\nasync function loadLinksInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"particlesLinks\", (container) => new _Linker__WEBPACK_IMPORTED_MODULE_0__.Linker(container), refresh);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tcGFydGljbGVzLWxpbmtzL2VzbS9pbnRlcmFjdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrQztBQUMzQjtBQUNQLG9FQUFvRSwyQ0FBTTtBQUMxRSIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1dHVyaXN0aWMtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL3RzcGFydGljbGVzLWludGVyYWN0aW9uLXBhcnRpY2xlcy1saW5rcy9lc20vaW50ZXJhY3Rpb24uanM/MWQ2YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBMaW5rZXIgfSBmcm9tIFwiLi9MaW5rZXJcIjtcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBsb2FkTGlua3NJbnRlcmFjdGlvbihlbmdpbmUsIHJlZnJlc2ggPSB0cnVlKSB7XG4gICAgYXdhaXQgZW5naW5lLmFkZEludGVyYWN0b3IoXCJwYXJ0aWNsZXNMaW5rc1wiLCAoY29udGFpbmVyKSA9PiBuZXcgTGlua2VyKGNvbnRhaW5lciksIHJlZnJlc2gpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/interaction.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/plugin.js":
/*!****************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-particles-links/esm/plugin.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadLinksPlugin: () => (/* binding */ loadLinksPlugin)\n/* harmony export */ });\n/* harmony import */ var _LinkInstance__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./LinkInstance */ \"(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/LinkInstance.js\");\n\nclass LinksPlugin {\n    constructor() {\n        this.id = \"links\";\n    }\n    getPlugin(container) {\n        return new _LinkInstance__WEBPACK_IMPORTED_MODULE_0__.LinkInstance(container);\n    }\n    loadOptions() {\n    }\n    needsPlugin() {\n        return true;\n    }\n}\nasync function loadLinksPlugin(engine, refresh = true) {\n    const plugin = new LinksPlugin();\n    await engine.addPlugin(plugin, refresh);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tcGFydGljbGVzLWxpbmtzL2VzbS9wbHVnaW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEM7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQix1REFBWTtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnV0dXJpc3RpYy1wb3J0Zm9saW8vLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tcGFydGljbGVzLWxpbmtzL2VzbS9wbHVnaW4uanM/NjllMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBMaW5rSW5zdGFuY2UgfSBmcm9tIFwiLi9MaW5rSW5zdGFuY2VcIjtcbmNsYXNzIExpbmtzUGx1Z2luIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgdGhpcy5pZCA9IFwibGlua3NcIjtcbiAgICB9XG4gICAgZ2V0UGx1Z2luKGNvbnRhaW5lcikge1xuICAgICAgICByZXR1cm4gbmV3IExpbmtJbnN0YW5jZShjb250YWluZXIpO1xuICAgIH1cbiAgICBsb2FkT3B0aW9ucygpIHtcbiAgICB9XG4gICAgbmVlZHNQbHVnaW4oKSB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbn1cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBsb2FkTGlua3NQbHVnaW4oZW5naW5lLCByZWZyZXNoID0gdHJ1ZSkge1xuICAgIGNvbnN0IHBsdWdpbiA9IG5ldyBMaW5rc1BsdWdpbigpO1xuICAgIGF3YWl0IGVuZ2luZS5hZGRQbHVnaW4ocGx1Z2luLCByZWZyZXNoKTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-particles-links/esm/plugin.js\n");

/***/ })

};
;