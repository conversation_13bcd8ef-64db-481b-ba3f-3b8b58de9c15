@echo off
echo.
echo 🔍 Portfolio Services Status Check
echo =================================
echo.

echo 📊 Checking service availability...
echo.

REM Check Backend API
echo 🔧 Backend API (Port 5000):
curl -s http://localhost:5000/api/health >nul 2>&1
if errorlevel 1 (
    echo    ❌ Not running or not responding
) else (
    echo    ✅ Running and healthy
)

REM Check Frontend Portfolio
echo 🎨 Frontend Portfolio (Port 3000):
curl -s http://localhost:3000 >nul 2>&1
if errorlevel 1 (
    echo    ❌ Not running or not responding
) else (
    echo    ✅ Running and accessible
)

REM Check Admin Panel
echo 🛠️  Admin Panel (Port 3001):
curl -s http://localhost:3001 >nul 2>&1
if errorlevel 1 (
    echo    ❌ Not running or not responding
) else (
    echo    ✅ Running and accessible
)

echo.
echo 🌐 Quick Access Links:
echo    📱 Portfolio:  http://localhost:3000
echo    🛠️  Admin:      http://localhost:3001
echo    🔧 API:        http://localhost:5000/api/health
echo.
echo 🔐 Admin Login:
echo    Email:    <EMAIL>
echo    Password: legendhero
echo.

REM Check if any services are missing
curl -s http://localhost:5000/api/health >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Backend not running. Start with: cd backend ^&^& npm run dev
)

curl -s http://localhost:3000 >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Frontend not running. Start with: npm run dev
)

curl -s http://localhost:3001 >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Admin panel not running. Start with: cd admin ^&^& npm run dev
)

echo.
echo 💡 To start all services at once: run-all.bat
echo.
pause
