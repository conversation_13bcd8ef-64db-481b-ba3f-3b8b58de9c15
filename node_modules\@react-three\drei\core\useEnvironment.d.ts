import { Texture, Loader } from 'three';
import { PresetsType } from '../helpers/environment-assets';
import { TextureEncoding } from '../helpers/deprecated';
export type EnvironmentLoaderProps = {
    files?: string | string[];
    path?: string;
    preset?: PresetsType;
    extensions?: (loader: Loader) => void;
    encoding?: TextureEncoding;
};
export declare function useEnvironment({ files, path, preset, encoding, extensions, }?: Partial<EnvironmentLoaderProps>): Texture;
export declare namespace useEnvironment {
    var preload: (preloadOptions?: EnvironmentLoaderPreloadOptions) => void;
    var clear: (clearOptions?: EnvironmentLoaderClearOptions) => void;
}
type EnvironmentLoaderPreloadOptions = Omit<EnvironmentLoaderProps, 'encoding'>;
type EnvironmentLoaderClearOptions = Pick<EnvironmentLoaderProps, 'files' | 'preset'>;
export {};
