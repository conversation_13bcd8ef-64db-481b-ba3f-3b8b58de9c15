const express = require('express');
const { body, validationResult } = require('express-validator');
const Project = require('../models/Project');
const { auth, adminAuth, logAdminAction } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/projects
// @desc    Get all published projects
// @access  Public
router.get('/', async (req, res) => {
  try {
    const { category, featured, limit } = req.query;
    
    let query = { isPublished: true };
    
    if (category) query.category = category;
    if (featured === 'true') query.featured = true;
    
    let projectsQuery = Project.find(query)
      .sort({ featured: -1, order: 1, createdAt: -1 });
    
    if (limit) projectsQuery = projectsQuery.limit(parseInt(limit));
    
    const projects = await projectsQuery;
    
    res.json({
      success: true,
      count: projects.length,
      data: projects
    });
  } catch (error) {
    console.error('Get projects error:', error);
    res.status(500).json({ message: 'Server error fetching projects' });
  }
});

// @route   GET /api/projects/:id
// @desc    Get single project
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const project = await Project.findOne({ 
      _id: req.params.id, 
      isPublished: true 
    });
    
    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }
    
    // Increment views
    await project.incrementViews();
    
    res.json({
      success: true,
      data: project
    });
  } catch (error) {
    console.error('Get project error:', error);
    res.status(500).json({ message: 'Server error fetching project' });
  }
});

// @route   POST /api/projects/:id/like
// @desc    Like a project
// @access  Public
router.post('/:id/like', async (req, res) => {
  try {
    const project = await Project.findOne({ 
      _id: req.params.id, 
      isPublished: true 
    });
    
    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }
    
    await project.incrementLikes();
    
    res.json({
      success: true,
      message: 'Project liked',
      likes: project.likes
    });
  } catch (error) {
    console.error('Like project error:', error);
    res.status(500).json({ message: 'Server error liking project' });
  }
});

// Admin routes below require authentication

// @route   GET /api/projects/admin/all
// @desc    Get all projects (including unpublished) for admin
// @access  Private/Admin
router.get('/admin/all', auth, adminAuth, async (req, res) => {
  try {
    const projects = await Project.find()
      .sort({ createdAt: -1 });
    
    res.json({
      success: true,
      count: projects.length,
      data: projects
    });
  } catch (error) {
    console.error('Get all projects error:', error);
    res.status(500).json({ message: 'Server error fetching projects' });
  }
});

// @route   POST /api/projects
// @desc    Create new project
// @access  Private/Admin
router.post('/',
  auth,
  adminAuth,
  logAdminAction('CREATE_PROJECT'),
  [
    body('title').notEmpty().trim(),
    body('description').notEmpty(),
    body('shortDescription').notEmpty().isLength({ max: 200 }),
    body('technologies').isArray({ min: 1 })
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ 
          message: 'Invalid input',
          errors: errors.array() 
        });
      }

      const project = new Project(req.body);
      await project.save();

      res.status(201).json({
        success: true,
        message: 'Project created successfully',
        data: project
      });
    } catch (error) {
      console.error('Create project error:', error);
      res.status(500).json({ message: 'Server error creating project' });
    }
  }
);

// @route   PUT /api/projects/:id
// @desc    Update project
// @access  Private/Admin
router.put('/:id',
  auth,
  adminAuth,
  logAdminAction('UPDATE_PROJECT'),
  async (req, res) => {
    try {
      const project = await Project.findByIdAndUpdate(
        req.params.id,
        req.body,
        { new: true, runValidators: true }
      );

      if (!project) {
        return res.status(404).json({ message: 'Project not found' });
      }

      res.json({
        success: true,
        message: 'Project updated successfully',
        data: project
      });
    } catch (error) {
      console.error('Update project error:', error);
      res.status(500).json({ message: 'Server error updating project' });
    }
  }
);

// @route   DELETE /api/projects/:id
// @desc    Delete project
// @access  Private/Admin
router.delete('/:id',
  auth,
  adminAuth,
  logAdminAction('DELETE_PROJECT'),
  async (req, res) => {
    try {
      const project = await Project.findByIdAndDelete(req.params.id);

      if (!project) {
        return res.status(404).json({ message: 'Project not found' });
      }

      res.json({
        success: true,
        message: 'Project deleted successfully'
      });
    } catch (error) {
      console.error('Delete project error:', error);
      res.status(500).json({ message: 'Server error deleting project' });
    }
  }
);

// @route   PATCH /api/projects/:id/toggle-featured
// @desc    Toggle project featured status
// @access  Private/Admin
router.patch('/:id/toggle-featured',
  auth,
  adminAuth,
  logAdminAction('TOGGLE_PROJECT_FEATURED'),
  async (req, res) => {
    try {
      const project = await Project.findById(req.params.id);

      if (!project) {
        return res.status(404).json({ message: 'Project not found' });
      }

      project.featured = !project.featured;
      await project.save();

      res.json({
        success: true,
        message: `Project ${project.featured ? 'featured' : 'unfeatured'} successfully`,
        data: project
      });
    } catch (error) {
      console.error('Toggle featured error:', error);
      res.status(500).json({ message: 'Server error toggling featured status' });
    }
  }
);

// @route   PATCH /api/projects/:id/toggle-published
// @desc    Toggle project published status
// @access  Private/Admin
router.patch('/:id/toggle-published',
  auth,
  adminAuth,
  logAdminAction('TOGGLE_PROJECT_PUBLISHED'),
  async (req, res) => {
    try {
      const project = await Project.findById(req.params.id);

      if (!project) {
        return res.status(404).json({ message: 'Project not found' });
      }

      project.isPublished = !project.isPublished;
      await project.save();

      res.json({
        success: true,
        message: `Project ${project.isPublished ? 'published' : 'unpublished'} successfully`,
        data: project
      });
    } catch (error) {
      console.error('Toggle published error:', error);
      res.status(500).json({ message: 'Server error toggling published status' });
    }
  }
);

module.exports = router;
