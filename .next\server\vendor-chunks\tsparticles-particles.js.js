"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tsparticles-particles.js";
exports.ids = ["vendor-chunks/tsparticles-particles.js"];
exports.modules = {

/***/ "(ssr)/./node_modules/tsparticles-particles.js/esm/VincentGarreau/particles.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/tsparticles-particles.js/esm/VincentGarreau/particles.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initParticlesJS: () => (/* binding */ initParticlesJS)\n/* harmony export */ });\nconst initParticlesJS = (engine) => {\n    const particlesJS = (tagId, options) => {\n        return engine.load(tagId, options);\n    };\n    particlesJS.load = (tagId, pathConfigJson, callback) => {\n        engine\n            .loadJSON(tagId, pathConfigJson)\n            .then((container) => {\n            if (container) {\n                callback(container);\n            }\n        })\n            .catch(() => {\n            callback(undefined);\n        });\n    };\n    particlesJS.setOnClickHandler = (callback) => {\n        engine.setOnClickHandler(callback);\n    };\n    const pJSDom = engine.dom();\n    return { particlesJS, pJSDom };\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtcGFydGljbGVzLmpzL2VzbS9WaW5jZW50R2FycmVhdS9wYXJ0aWNsZXMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQzJCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnV0dXJpc3RpYy1wb3J0Zm9saW8vLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtcGFydGljbGVzLmpzL2VzbS9WaW5jZW50R2FycmVhdS9wYXJ0aWNsZXMuanM/Yzk4MCJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpbml0UGFydGljbGVzSlMgPSAoZW5naW5lKSA9PiB7XG4gICAgY29uc3QgcGFydGljbGVzSlMgPSAodGFnSWQsIG9wdGlvbnMpID0+IHtcbiAgICAgICAgcmV0dXJuIGVuZ2luZS5sb2FkKHRhZ0lkLCBvcHRpb25zKTtcbiAgICB9O1xuICAgIHBhcnRpY2xlc0pTLmxvYWQgPSAodGFnSWQsIHBhdGhDb25maWdKc29uLCBjYWxsYmFjaykgPT4ge1xuICAgICAgICBlbmdpbmVcbiAgICAgICAgICAgIC5sb2FkSlNPTih0YWdJZCwgcGF0aENvbmZpZ0pzb24pXG4gICAgICAgICAgICAudGhlbigoY29udGFpbmVyKSA9PiB7XG4gICAgICAgICAgICBpZiAoY29udGFpbmVyKSB7XG4gICAgICAgICAgICAgICAgY2FsbGJhY2soY29udGFpbmVyKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSlcbiAgICAgICAgICAgIC5jYXRjaCgoKSA9PiB7XG4gICAgICAgICAgICBjYWxsYmFjayh1bmRlZmluZWQpO1xuICAgICAgICB9KTtcbiAgICB9O1xuICAgIHBhcnRpY2xlc0pTLnNldE9uQ2xpY2tIYW5kbGVyID0gKGNhbGxiYWNrKSA9PiB7XG4gICAgICAgIGVuZ2luZS5zZXRPbkNsaWNrSGFuZGxlcihjYWxsYmFjayk7XG4gICAgfTtcbiAgICBjb25zdCBwSlNEb20gPSBlbmdpbmUuZG9tKCk7XG4gICAgcmV0dXJuIHsgcGFydGljbGVzSlMsIHBKU0RvbSB9O1xufTtcbmV4cG9ydCB7IGluaXRQYXJ0aWNsZXNKUyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-particles.js/esm/VincentGarreau/particles.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-particles.js/esm/index.js":
/*!************************************************************!*\
  !*** ./node_modules/tsparticles-particles.js/esm/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initPjs: () => (/* binding */ initPjs)\n/* harmony export */ });\n/* harmony import */ var _marcbruederlin_Particles__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./marcbruederlin/Particles */ \"(ssr)/./node_modules/tsparticles-particles.js/esm/marcbruederlin/Particles.js\");\n/* harmony import */ var _VincentGarreau_particles__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./VincentGarreau/particles */ \"(ssr)/./node_modules/tsparticles-particles.js/esm/VincentGarreau/particles.js\");\n\n\nconst initPjs = (engine) => {\n    const { particlesJS, pJSDom } = (0,_VincentGarreau_particles__WEBPACK_IMPORTED_MODULE_0__.initParticlesJS)(engine);\n    window.particlesJS = particlesJS;\n    window.pJSDom = pJSDom;\n    window.Particles = _marcbruederlin_Particles__WEBPACK_IMPORTED_MODULE_1__.Particles;\n    return { particlesJS, pJSDom, Particles: _marcbruederlin_Particles__WEBPACK_IMPORTED_MODULE_1__.Particles };\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtcGFydGljbGVzLmpzL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBdUQ7QUFDTTtBQUM3RDtBQUNBLFlBQVksc0JBQXNCLEVBQUUsMEVBQWU7QUFDbkQ7QUFDQTtBQUNBLHVCQUF1QixnRUFBUztBQUNoQyxhQUFhLDhCQUE4QjtBQUMzQztBQUNtQiIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1dHVyaXN0aWMtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL3RzcGFydGljbGVzLXBhcnRpY2xlcy5qcy9lc20vaW5kZXguanM/YTA3ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYXJ0aWNsZXMgfSBmcm9tIFwiLi9tYXJjYnJ1ZWRlcmxpbi9QYXJ0aWNsZXNcIjtcbmltcG9ydCB7IGluaXRQYXJ0aWNsZXNKUyB9IGZyb20gXCIuL1ZpbmNlbnRHYXJyZWF1L3BhcnRpY2xlc1wiO1xuY29uc3QgaW5pdFBqcyA9IChlbmdpbmUpID0+IHtcbiAgICBjb25zdCB7IHBhcnRpY2xlc0pTLCBwSlNEb20gfSA9IGluaXRQYXJ0aWNsZXNKUyhlbmdpbmUpO1xuICAgIHdpbmRvdy5wYXJ0aWNsZXNKUyA9IHBhcnRpY2xlc0pTO1xuICAgIHdpbmRvdy5wSlNEb20gPSBwSlNEb207XG4gICAgd2luZG93LlBhcnRpY2xlcyA9IFBhcnRpY2xlcztcbiAgICByZXR1cm4geyBwYXJ0aWNsZXNKUywgcEpTRG9tLCBQYXJ0aWNsZXMgfTtcbn07XG5leHBvcnQgeyBpbml0UGpzIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-particles.js/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-particles.js/esm/marcbruederlin/Particles.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/tsparticles-particles.js/esm/marcbruederlin/Particles.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Particles: () => (/* binding */ Particles)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/index.js\");\n\nclass Particles {\n    static init(options) {\n        const particles = new Particles(), selector = options.selector;\n        if (!selector) {\n            throw new Error(\"No selector provided\");\n        }\n        const el = document.querySelector(selector);\n        if (!el) {\n            throw new Error(\"No element found for selector\");\n        }\n        tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.tsParticles\n            .set(selector.replace(\".\", \"\").replace(\"!\", \"\"), el, {\n            fullScreen: {\n                enable: false,\n            },\n            particles: {\n                color: {\n                    value: options.color ?? \"!000000\",\n                },\n                links: {\n                    color: \"random\",\n                    distance: options.minDistance ?? 120,\n                    enable: options.connectParticles ?? false,\n                },\n                move: {\n                    enable: true,\n                    speed: options.speed ?? 0.5,\n                },\n                number: {\n                    value: options.maxParticles ?? 100,\n                },\n                size: {\n                    value: { min: 1, max: options.sizeVariations ?? 3 },\n                },\n            },\n            responsive: options.responsive?.map((responsive) => ({\n                maxWidth: responsive.breakpoint,\n                options: {\n                    particles: {\n                        color: {\n                            value: responsive.options?.color,\n                        },\n                        links: {\n                            distance: responsive.options?.minDistance,\n                            enable: responsive.options?.connectParticles,\n                        },\n                        number: {\n                            value: options.maxParticles,\n                        },\n                        move: {\n                            enable: true,\n                            speed: responsive.options?.speed,\n                        },\n                        size: {\n                            value: responsive.options?.sizeVariations,\n                        },\n                    },\n                },\n            })),\n        })\n            .then((container) => {\n            particles._container = container;\n        });\n        return particles;\n    }\n    destroy() {\n        const container = this._container;\n        container && container.destroy();\n    }\n    pauseAnimation() {\n        const container = this._container;\n        container && container.pause();\n    }\n    resumeAnimation() {\n        const container = this._container;\n        container && container.play();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-particles.js/esm/marcbruederlin/Particles.js\n");

/***/ })

};
;