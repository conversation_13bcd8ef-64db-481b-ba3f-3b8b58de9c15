"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-particles";
exports.ids = ["vendor-chunks/react-particles"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-particles/esm/Particles.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-particles/esm/Particles.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/index.js\");\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/react-particles/esm/Utils.js\");\n\n\n\nconst defaultId = \"tsparticles\";\nclass Particles extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            init: false,\n            library: undefined,\n        };\n    }\n    destroy() {\n        if (!this.state.library) {\n            return;\n        }\n        this.state.library.destroy();\n        this.setState({\n            library: undefined,\n        });\n    }\n    shouldComponentUpdate(nextProps) {\n        const nextOptions = nextProps.options ?? nextProps.params, currentOptions = this.props.options ?? this.props.params;\n        return (nextProps.url !== this.props.url ||\n            nextProps.id !== this.props.id ||\n            nextProps.canvasClassName !== this.props.canvasClassName ||\n            nextProps.className !== this.props.className ||\n            nextProps.height !== this.props.height ||\n            nextProps.width !== this.props.width ||\n            !(0,_Utils__WEBPACK_IMPORTED_MODULE_1__.deepCompare)(nextProps.style, this.props.style) ||\n            nextProps.init !== this.props.init ||\n            nextProps.loaded !== this.props.loaded ||\n            !(0,_Utils__WEBPACK_IMPORTED_MODULE_1__.deepCompare)(nextOptions, currentOptions, key => key.startsWith(\"_\")));\n    }\n    componentDidUpdate() {\n        this.refresh();\n    }\n    forceUpdate() {\n        this.refresh().then(() => {\n            super.forceUpdate();\n        });\n    }\n    componentDidMount() {\n        (async () => {\n            if (this.props.init) {\n                await this.props.init(tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.tsParticles);\n            }\n            this.setState({\n                init: true,\n            }, async () => {\n                await this.loadParticles();\n            });\n        })();\n    }\n    componentWillUnmount() {\n        this.destroy();\n    }\n    render() {\n        const { width, height, className, canvasClassName, id } = this.props;\n        return (react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", { className: className, id: id },\n            react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"canvas\", { className: canvasClassName, style: {\n                    ...this.props.style,\n                    width,\n                    height,\n                } })));\n    }\n    async refresh() {\n        this.destroy();\n        await this.loadParticles();\n    }\n    async loadParticles() {\n        if (!this.state.init) {\n            return;\n        }\n        const id = this.props.id ?? Particles.defaultProps.id ?? defaultId, container = await tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.tsParticles.load({\n            url: this.props.url,\n            id,\n            options: this.props.options ?? this.props.params,\n        });\n        if (this.props.container) {\n            this.props.container.current = container;\n        }\n        this.setState({\n            library: container,\n        });\n        if (this.props.loaded) {\n            await this.props.loaded(container);\n        }\n    }\n}\nParticles.defaultProps = {\n    width: \"100%\",\n    height: \"100%\",\n    options: {},\n    style: {},\n    url: undefined,\n    id: defaultId,\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Particles);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-particles/esm/Particles.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-particles/esm/Utils.js":
/*!***************************************************!*\
  !*** ./node_modules/react-particles/esm/Utils.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deepCompare: () => (/* binding */ deepCompare)\n/* harmony export */ });\nconst isObject = (val) => typeof val === \"object\" && val !== null;\nfunction deepCompare(obj1, obj2, excludeKeyFn = () => false) {\n    if (!isObject(obj1) || !isObject(obj2)) {\n        return obj1 === obj2;\n    }\n    const keys1 = Object.keys(obj1).filter(key => !excludeKeyFn(key)), keys2 = Object.keys(obj2).filter(key => !excludeKeyFn(key));\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    for (const key of keys1) {\n        const value1 = obj1[key], value2 = obj2[key];\n        if (isObject(value1) && isObject(value2)) {\n            if (value1 === obj2 && value2 === obj1) {\n                continue;\n            }\n            if (!deepCompare(value1, value2, excludeKeyFn)) {\n                return false;\n            }\n        }\n        else if (Array.isArray(value1) && Array.isArray(value2)) {\n            if (!deepCompareArrays(value1, value2, excludeKeyFn)) {\n                return false;\n            }\n        }\n        else if (value1 !== value2) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction deepCompareArrays(arr1, arr2, excludeKeyFn) {\n    if (arr1.length !== arr2.length) {\n        return false;\n    }\n    for (let i = 0; i < arr1.length; i++) {\n        const val1 = arr1[i], val2 = arr2[i];\n        if (Array.isArray(val1) && Array.isArray(val2)) {\n            if (!deepCompareArrays(val1, val2, excludeKeyFn)) {\n                return false;\n            }\n        }\n        else if (isObject(val1) && isObject(val2)) {\n            if (!deepCompare(val1, val2, excludeKeyFn)) {\n                return false;\n            }\n        }\n        else if (val1 !== val2) {\n            return false;\n        }\n    }\n    return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-particles/esm/Utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-particles/esm/index.js":
/*!***************************************************!*\
  !*** ./node_modules/react-particles/esm/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Particles: () => (/* reexport safe */ _Particles__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Particles__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Particles */ \"(ssr)/./node_modules/react-particles/esm/Particles.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_Particles__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGFydGljbGVzL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBb0M7QUFDcEMsaUVBQWUsa0RBQVMsRUFBQztBQUNKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnV0dXJpc3RpYy1wb3J0Zm9saW8vLi9ub2RlX21vZHVsZXMvcmVhY3QtcGFydGljbGVzL2VzbS9pbmRleC5qcz8xOWEyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBQYXJ0aWNsZXMgZnJvbSBcIi4vUGFydGljbGVzXCI7XG5leHBvcnQgZGVmYXVsdCBQYXJ0aWNsZXM7XG5leHBvcnQgeyBQYXJ0aWNsZXMgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-particles/esm/index.js\n");

/***/ })

};
;