# PowerShell script to run all portfolio services
# Run with: PowerShell -ExecutionPolicy Bypass -File run-all.ps1

Write-Host ""
Write-Host "🚀 Starting Complete Portfolio Development Environment" -ForegroundColor Cyan
Write-Host "===================================================" -ForegroundColor Cyan
Write-Host ""

# Function to print colored output
function Write-Success { param($Message); Write-Host "✅ $Message" -ForegroundColor Green }
function Write-Warning { param($Message); Write-Host "⚠️  $Message" -ForegroundColor Yellow }
function Write-Error { param($Message); Write-Host "❌ $Message" -ForegroundColor Red }
function Write-Info { param($Message); Write-Host "ℹ️  $Message" -ForegroundColor Blue }

# Check dependencies
if (-not (Test-Path "node_modules")) {
    Write-Error "Frontend dependencies not found. Please run setup.ps1 first."
    Read-Host "Press Enter to exit"; exit 1
}

if (-not (Test-Path "backend\node_modules")) {
    Write-Error "Backend dependencies not found. Please run setup.ps1 first."
    Read-Host "Press Enter to exit"; exit 1
}

if (-not (Test-Path "admin\node_modules")) {
    Write-Error "Admin dependencies not found. Please run setup.ps1 first."
    Read-Host "Press Enter to exit"; exit 1
}

Write-Success "All dependencies found!"
Write-Host ""
Write-Info "Starting all services..."
Write-Host ""
Write-Host "📝 This will open 3 PowerShell windows:" -ForegroundColor Yellow
Write-Host "   1. 🔧 Backend API Server (Port 5000)" -ForegroundColor Green
Write-Host "   2. 🎨 Frontend Portfolio (Port 3000)" -ForegroundColor Green
Write-Host "   3. 🛠️  Admin Panel (Port 3001)" -ForegroundColor Green
Write-Host ""
Write-Host "🔐 Admin Panel Login:" -ForegroundColor Magenta
Write-Host "   URL: http://localhost:3001" -ForegroundColor White
Write-Host "   Email: <EMAIL>" -ForegroundColor White
Write-Host "   Password: legendhero" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to start all services"

# Start Backend API Server
Write-Info "Starting Backend API Server..."
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd backend; Write-Host ''; Write-Host '🔧 BACKEND API SERVER' -ForegroundColor Cyan; Write-Host 'Port: 5000' -ForegroundColor Yellow; Write-Host '====================' -ForegroundColor Cyan; Write-Host ''; npm run dev"

# Wait for backend
Write-Host "⏳ Waiting for backend to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Start Frontend Portfolio
Write-Info "Starting Frontend Portfolio..."
Start-Process powershell -ArgumentList "-NoExit", "-Command", "Write-Host ''; Write-Host '🎨 FRONTEND PORTFOLIO' -ForegroundColor Cyan; Write-Host 'Port: 3000' -ForegroundColor Yellow; Write-Host '===================' -ForegroundColor Cyan; Write-Host ''; npm run dev"

# Wait for frontend
Write-Host "⏳ Waiting for frontend to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

# Start Admin Panel
Write-Info "Starting Admin Panel..."
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd admin; Write-Host ''; Write-Host '🛠️  ADMIN PANEL' -ForegroundColor Cyan; Write-Host 'Port: 3001' -ForegroundColor Yellow; Write-Host '============' -ForegroundColor Cyan; Write-Host ''; npm run dev"

Write-Host ""
Write-Success "All services are starting!"
Write-Host ""
Write-Host "🌐 Your applications will be available at:" -ForegroundColor Magenta
Write-Host "   📱 Frontend Portfolio: http://localhost:3000" -ForegroundColor Green
Write-Host "   🛠️  Admin Panel:       http://localhost:3001" -ForegroundColor Green
Write-Host "   🔧 Backend API:        http://localhost:5000" -ForegroundColor Green
Write-Host ""
Write-Host "🔐 Admin Panel Access:" -ForegroundColor Yellow
Write-Host "   Email:    <EMAIL>"
Write-Host "   Password: legendhero"
Write-Host ""
Write-Host "💡 Tips:" -ForegroundColor Cyan
Write-Host "   - Wait 1-2 minutes for all services to fully start"
Write-Host "   - Check each PowerShell window for startup status"
Write-Host "   - Use Ctrl+C in each window to stop services"
Write-Host "   - Admin panel requires backend to be running"
Write-Host ""
Write-Host "🎯 Quick Links:" -ForegroundColor Cyan
Write-Host "   - Portfolio:  http://localhost:3000"
Write-Host "   - Admin:      http://localhost:3001"
Write-Host "   - API Health: http://localhost:5000/api/health"
Write-Host ""
Write-Success "Happy coding! ✨"
Write-Host ""
Read-Host "Press Enter to close this launcher"
