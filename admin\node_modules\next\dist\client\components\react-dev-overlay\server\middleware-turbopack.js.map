{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/server/middleware-turbopack.ts"], "names": ["batchedTraceSource", "createOriginalStackFrame", "getOverlayMiddleware", "currentSourcesByFile", "Map", "project", "frame", "file", "decodeURIComponent", "undefined", "sourceFrame", "traceSource", "source", "includes", "isInternal", "sourcePromise", "get", "getSourceForAsset", "set", "setTimeout", "delete", "lineNumber", "line", "column", "methodName", "arguments", "traced", "sourcePackage", "findSourcePackage", "originalStackFrame", "originalCodeFrame", "getOriginalCodeFrame", "req", "res", "pathname", "searchParams", "URL", "url", "parseInt", "isServer", "e", "internalServerError", "message", "statusCode", "end", "json", "badRequest", "fileExists", "fs", "access", "FS", "F_OK", "then", "noContent", "launchEditor", "err", "console", "log"], "mappings": ";;;;;;;;;;;;;;;;IAiBsBA,kBAAkB;eAAlBA;;IA0CAC,wBAAwB;eAAxBA;;IAkBNC,oBAAoB;eAApBA;;;;wBApET;oEAE6B;8BACP;AAI7B,MAAMC,uBAA4D,IAAIC;AAC/D,eAAeJ,mBACpBK,OAAgB,EAChBC,KAA0B;IAE1B,MAAMC,OAAOD,MAAMC,IAAI,GAAGC,mBAAmBF,MAAMC,IAAI,IAAIE;IAC3D,IAAI,CAACF,MAAM;IAEX,MAAMG,cAAc,MAAML,QAAQM,WAAW,CAACL;IAC9C,IAAI,CAACI,aAAa;IAElB,IAAIE,SAAS;IACb,8FAA8F;IAC9F,IACEF,YAAYH,IAAI,IAChB,CAAEG,CAAAA,YAAYH,IAAI,CAACM,QAAQ,CAAC,mBAAmBH,YAAYI,UAAU,AAAD,GACpE;QACA,IAAIC,gBAAgBZ,qBAAqBa,GAAG,CAACN,YAAYH,IAAI;QAC7D,IAAI,CAACQ,eAAe;YAClBA,gBAAgBV,QAAQY,iBAAiB,CAACP,YAAYH,IAAI;YAC1DJ,qBAAqBe,GAAG,CAACR,YAAYH,IAAI,EAAEQ;YAC3CI,WAAW;gBACT,sEAAsE;gBACtE,0BAA0B;gBAC1BhB,qBAAqBiB,MAAM,CAACV,YAAYH,IAAI;YAC9C,GAAG;QACL;QAEAK,SAAS,MAAMG;IACjB;QAKgBL,mBACJA,qBACIA,yBAAAA;IALhB,OAAO;QACLJ,OAAO;YACLC,MAAMG,YAAYH,IAAI;YACtBc,YAAYX,CAAAA,oBAAAA,YAAYY,IAAI,YAAhBZ,oBAAoB;YAChCa,QAAQb,CAAAA,sBAAAA,YAAYa,MAAM,YAAlBb,sBAAsB;YAC9Bc,YAAYd,CAAAA,OAAAA,CAAAA,0BAAAA,YAAYc,UAAU,YAAtBd,0BAA0BJ,MAAMkB,UAAU,YAA1Cd,OAA8C;YAC1De,WAAW,EAAE;QACf;QACAb;IACF;AACF;AAEO,eAAeX,yBACpBI,OAAgB,EAChBC,KAA0B;IAE1B,MAAMoB,SAAS,MAAM1B,mBAAmBK,SAASC;IACjD,IAAI,CAACoB,QAAQ;QACX,MAAMC,gBAAgBC,IAAAA,yBAAiB,EAACtB;QACxC,IAAIqB,eAAe,OAAO;YAAEA;QAAc;QAC1C,OAAO;IACT;IAEA,OAAO;QACLE,oBAAoBH,OAAOpB,KAAK;QAChCwB,mBAAmBC,IAAAA,4BAAoB,EAACL,OAAOpB,KAAK,EAAEoB,OAAOd,MAAM;QACnEe,eAAeC,IAAAA,yBAAiB,EAACF,OAAOpB,KAAK;IAC/C;AACF;AAEO,SAASJ,qBAAqBG,OAAgB;IACnD,OAAO,eAAgB2B,GAAoB,EAAEC,GAAmB;QAC9D,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAAG,IAAIC,IAAIJ,IAAIK,GAAG,EAAG;YAIvCF,mBACGA,oBACEA;QAJnB,MAAM7B,QAAQ;YACZC,MAAM4B,aAAanB,GAAG,CAAC;YACvBQ,YAAYW,CAAAA,oBAAAA,aAAanB,GAAG,CAAC,yBAAjBmB,oBAAkC;YAC9Cb,MAAMgB,SAASH,CAAAA,qBAAAA,aAAanB,GAAG,CAAC,yBAAjBmB,qBAAkC,KAAK,OAAO;YAC7DZ,QAAQe,SAASH,CAAAA,qBAAAA,aAAanB,GAAG,CAAC,qBAAjBmB,qBAA8B,KAAK,OAAO;YAC3DI,UAAUJ,aAAanB,GAAG,CAAC,gBAAgB;QAC7C;QAEA,IAAIkB,aAAa,kCAAkC;YACjD,IAAIL;YACJ,IAAI;gBACFA,qBAAqB,MAAM5B,yBAAyBI,SAASC;YAC/D,EAAE,OAAOkC,GAAQ;gBACf,OAAOC,IAAAA,2BAAmB,EAACR,KAAKO,EAAEE,OAAO;YAC3C;YAEA,IAAI,CAACb,oBAAoB;gBACvBI,IAAIU,UAAU,GAAG;gBACjB,OAAOV,IAAIW,GAAG,CAAC;YACjB;YAEA,OAAOC,IAAAA,YAAI,EAACZ,KAAKJ;QACnB,OAAO,IAAIK,aAAa,2BAA2B;YACjD,IAAI,CAAC5B,MAAMC,IAAI,EAAE,OAAOuC,IAAAA,kBAAU,EAACb;YAEnC,MAAMc,aAAa,MAAMC,iBAAE,CAACC,MAAM,CAAC3C,MAAMC,IAAI,EAAE2C,mBAAE,CAACC,IAAI,EAAEC,IAAI,CAC1D,IAAM,MACN,IAAM;YAER,IAAI,CAACL,YAAY,OAAOM,IAAAA,iBAAS,EAACpB;YAElC,IAAI;oBACuB3B,aAAiBA;gBAA1CgD,IAAAA,0BAAY,EAAChD,MAAMC,IAAI,EAAED,CAAAA,cAAAA,MAAMgB,IAAI,YAAVhB,cAAc,GAAGA,CAAAA,gBAAAA,MAAMiB,MAAM,YAAZjB,gBAAgB;YAC5D,EAAE,OAAOiD,KAAK;gBACZC,QAAQC,GAAG,CAAC,4BAA4BF;gBACxC,OAAOd,IAAAA,2BAAmB,EAACR;YAC7B;YAEAoB,IAAAA,iBAAS,EAACpB;QACZ;IACF;AACF"}