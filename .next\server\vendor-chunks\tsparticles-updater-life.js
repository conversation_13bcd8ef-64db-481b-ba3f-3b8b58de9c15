"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tsparticles-updater-life";
exports.ids = ["vendor-chunks/tsparticles-updater-life"];
exports.modules = {

/***/ "(ssr)/./node_modules/tsparticles-updater-life/esm/LifeUpdater.js":
/*!******************************************************************!*\
  !*** ./node_modules/tsparticles-updater-life/esm/LifeUpdater.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LifeUpdater: () => (/* binding */ LifeUpdater)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n/* harmony import */ var _Options_Classes_Life__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Options/Classes/Life */ \"(ssr)/./node_modules/tsparticles-updater-life/esm/Options/Classes/Life.js\");\n\n\nclass LifeUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const container = this.container, particlesOptions = particle.options, lifeOptions = particlesOptions.life;\n        if (!lifeOptions) {\n            return;\n        }\n        particle.life = {\n            delay: container.retina.reduceFactor\n                ? (((0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(lifeOptions.delay.value) * (lifeOptions.delay.sync ? 1 : (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)())) /\n                    container.retina.reduceFactor) *\n                    1000\n                : 0,\n            delayTime: 0,\n            duration: container.retina.reduceFactor\n                ? (((0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(lifeOptions.duration.value) * (lifeOptions.duration.sync ? 1 : (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)())) /\n                    container.retina.reduceFactor) *\n                    1000\n                : 0,\n            time: 0,\n            count: lifeOptions.count,\n        };\n        if (particle.life.duration <= 0) {\n            particle.life.duration = -1;\n        }\n        if (particle.life.count <= 0) {\n            particle.life.count = -1;\n        }\n        if (particle.life) {\n            particle.spawning = particle.life.delay > 0;\n        }\n    }\n    isEnabled(particle) {\n        return !particle.destroyed;\n    }\n    loadOptions(options, ...sources) {\n        if (!options.life) {\n            options.life = new _Options_Classes_Life__WEBPACK_IMPORTED_MODULE_1__.Life();\n        }\n        for (const source of sources) {\n            options.life.load(source?.life);\n        }\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle) || !particle.life) {\n            return;\n        }\n        const life = particle.life;\n        let justSpawned = false;\n        if (particle.spawning) {\n            life.delayTime += delta.value;\n            if (life.delayTime >= particle.life.delay) {\n                justSpawned = true;\n                particle.spawning = false;\n                life.delayTime = 0;\n                life.time = 0;\n            }\n            else {\n                return;\n            }\n        }\n        if (life.duration === -1) {\n            return;\n        }\n        if (particle.spawning) {\n            return;\n        }\n        if (justSpawned) {\n            life.time = 0;\n        }\n        else {\n            life.time += delta.value;\n        }\n        if (life.time < life.duration) {\n            return;\n        }\n        life.time = 0;\n        if (particle.life.count > 0) {\n            particle.life.count--;\n        }\n        if (particle.life.count === 0) {\n            particle.destroy();\n            return;\n        }\n        const canvasSize = this.container.canvas.size, widthRange = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(0, canvasSize.width), heightRange = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(0, canvasSize.width);\n        particle.position.x = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.randomInRange)(widthRange);\n        particle.position.y = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.randomInRange)(heightRange);\n        particle.spawning = true;\n        life.delayTime = 0;\n        life.time = 0;\n        particle.reset();\n        const lifeOptions = particle.options.life;\n        if (lifeOptions) {\n            life.delay = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(lifeOptions.delay.value) * 1000;\n            life.duration = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(lifeOptions.duration.value) * 1000;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-life/esm/LifeUpdater.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-updater-life/esm/Options/Classes/Life.js":
/*!***************************************************************************!*\
  !*** ./node_modules/tsparticles-updater-life/esm/Options/Classes/Life.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Life: () => (/* binding */ Life)\n/* harmony export */ });\n/* harmony import */ var _LifeDelay__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./LifeDelay */ \"(ssr)/./node_modules/tsparticles-updater-life/esm/Options/Classes/LifeDelay.js\");\n/* harmony import */ var _LifeDuration__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./LifeDuration */ \"(ssr)/./node_modules/tsparticles-updater-life/esm/Options/Classes/LifeDuration.js\");\n\n\nclass Life {\n    constructor() {\n        this.count = 0;\n        this.delay = new _LifeDelay__WEBPACK_IMPORTED_MODULE_0__.LifeDelay();\n        this.duration = new _LifeDuration__WEBPACK_IMPORTED_MODULE_1__.LifeDuration();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.count !== undefined) {\n            this.count = data.count;\n        }\n        this.delay.load(data.delay);\n        this.duration.load(data.duration);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtdXBkYXRlci1saWZlL2VzbS9PcHRpb25zL0NsYXNzZXMvTGlmZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBd0M7QUFDTTtBQUN2QztBQUNQO0FBQ0E7QUFDQSx5QkFBeUIsaURBQVM7QUFDbEMsNEJBQTRCLHVEQUFZO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1dHVyaXN0aWMtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL3RzcGFydGljbGVzLXVwZGF0ZXItbGlmZS9lc20vT3B0aW9ucy9DbGFzc2VzL0xpZmUuanM/MzEyNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBMaWZlRGVsYXkgfSBmcm9tIFwiLi9MaWZlRGVsYXlcIjtcbmltcG9ydCB7IExpZmVEdXJhdGlvbiB9IGZyb20gXCIuL0xpZmVEdXJhdGlvblwiO1xuZXhwb3J0IGNsYXNzIExpZmUge1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICB0aGlzLmNvdW50ID0gMDtcbiAgICAgICAgdGhpcy5kZWxheSA9IG5ldyBMaWZlRGVsYXkoKTtcbiAgICAgICAgdGhpcy5kdXJhdGlvbiA9IG5ldyBMaWZlRHVyYXRpb24oKTtcbiAgICB9XG4gICAgbG9hZChkYXRhKSB7XG4gICAgICAgIGlmICghZGF0YSkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGlmIChkYXRhLmNvdW50ICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHRoaXMuY291bnQgPSBkYXRhLmNvdW50O1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuZGVsYXkubG9hZChkYXRhLmRlbGF5KTtcbiAgICAgICAgdGhpcy5kdXJhdGlvbi5sb2FkKGRhdGEuZHVyYXRpb24pO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-life/esm/Options/Classes/Life.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-updater-life/esm/Options/Classes/LifeDelay.js":
/*!********************************************************************************!*\
  !*** ./node_modules/tsparticles-updater-life/esm/Options/Classes/LifeDelay.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LifeDelay: () => (/* binding */ LifeDelay)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Options/Classes/ValueWithRandom.js\");\n\nclass LifeDelay extends tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ValueWithRandom {\n    constructor() {\n        super();\n        this.sync = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        super.load(data);\n        if (data.sync !== undefined) {\n            this.sync = data.sync;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtdXBkYXRlci1saWZlL2VzbS9PcHRpb25zL0NsYXNzZXMvTGlmZURlbGF5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXFEO0FBQzlDLHdCQUF3QiwrREFBZTtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnV0dXJpc3RpYy1wb3J0Zm9saW8vLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtdXBkYXRlci1saWZlL2VzbS9PcHRpb25zL0NsYXNzZXMvTGlmZURlbGF5LmpzP2Q4MDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVmFsdWVXaXRoUmFuZG9tIH0gZnJvbSBcInRzcGFydGljbGVzLWVuZ2luZVwiO1xuZXhwb3J0IGNsYXNzIExpZmVEZWxheSBleHRlbmRzIFZhbHVlV2l0aFJhbmRvbSB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHN1cGVyKCk7XG4gICAgICAgIHRoaXMuc3luYyA9IGZhbHNlO1xuICAgIH1cbiAgICBsb2FkKGRhdGEpIHtcbiAgICAgICAgaWYgKCFkYXRhKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgc3VwZXIubG9hZChkYXRhKTtcbiAgICAgICAgaWYgKGRhdGEuc3luYyAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aGlzLnN5bmMgPSBkYXRhLnN5bmM7XG4gICAgICAgIH1cbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-life/esm/Options/Classes/LifeDelay.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-updater-life/esm/Options/Classes/LifeDuration.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/tsparticles-updater-life/esm/Options/Classes/LifeDuration.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LifeDuration: () => (/* binding */ LifeDuration)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Options/Classes/ValueWithRandom.js\");\n\nclass LifeDuration extends tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ValueWithRandom {\n    constructor() {\n        super();\n        this.random.minimumValue = 0.0001;\n        this.sync = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        super.load(data);\n        if (data.sync !== undefined) {\n            this.sync = data.sync;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtdXBkYXRlci1saWZlL2VzbS9PcHRpb25zL0NsYXNzZXMvTGlmZUR1cmF0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXFEO0FBQzlDLDJCQUEyQiwrREFBZTtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mdXR1cmlzdGljLXBvcnRmb2xpby8uL25vZGVfbW9kdWxlcy90c3BhcnRpY2xlcy11cGRhdGVyLWxpZmUvZXNtL09wdGlvbnMvQ2xhc3Nlcy9MaWZlRHVyYXRpb24uanM/OTdlNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBWYWx1ZVdpdGhSYW5kb20gfSBmcm9tIFwidHNwYXJ0aWNsZXMtZW5naW5lXCI7XG5leHBvcnQgY2xhc3MgTGlmZUR1cmF0aW9uIGV4dGVuZHMgVmFsdWVXaXRoUmFuZG9tIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgc3VwZXIoKTtcbiAgICAgICAgdGhpcy5yYW5kb20ubWluaW11bVZhbHVlID0gMC4wMDAxO1xuICAgICAgICB0aGlzLnN5bmMgPSBmYWxzZTtcbiAgICB9XG4gICAgbG9hZChkYXRhKSB7XG4gICAgICAgIGlmICghZGF0YSkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHN1cGVyLmxvYWQoZGF0YSk7XG4gICAgICAgIGlmIChkYXRhLnN5bmMgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5zeW5jID0gZGF0YS5zeW5jO1xuICAgICAgICB9XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-life/esm/Options/Classes/LifeDuration.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-updater-life/esm/index.js":
/*!************************************************************!*\
  !*** ./node_modules/tsparticles-updater-life/esm/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadLifeUpdater: () => (/* binding */ loadLifeUpdater)\n/* harmony export */ });\n/* harmony import */ var _LifeUpdater__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./LifeUpdater */ \"(ssr)/./node_modules/tsparticles-updater-life/esm/LifeUpdater.js\");\n\nasync function loadLifeUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"life\", (container) => new _LifeUpdater__WEBPACK_IMPORTED_MODULE_0__.LifeUpdater(container), refresh);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtdXBkYXRlci1saWZlL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0QztBQUNyQztBQUNQLCtEQUErRCxxREFBVztBQUMxRSIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1dHVyaXN0aWMtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL3RzcGFydGljbGVzLXVwZGF0ZXItbGlmZS9lc20vaW5kZXguanM/ZTFiYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBMaWZlVXBkYXRlciB9IGZyb20gXCIuL0xpZmVVcGRhdGVyXCI7XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gbG9hZExpZmVVcGRhdGVyKGVuZ2luZSwgcmVmcmVzaCA9IHRydWUpIHtcbiAgICBhd2FpdCBlbmdpbmUuYWRkUGFydGljbGVVcGRhdGVyKFwibGlmZVwiLCAoY29udGFpbmVyKSA9PiBuZXcgTGlmZVVwZGF0ZXIoY29udGFpbmVyKSwgcmVmcmVzaCk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-life/esm/index.js\n");

/***/ })

};
;