import axios from 'axios'
import Cookies from 'js-cookie'

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api'

export const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get('admin_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      Cookies.remove('admin_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// API functions
export const authAPI = {
  login: (email: string, password: string) =>
    api.post('/auth/login', { email, password }),
  verify: () => api.post('/auth/verify'),
  logout: () => api.post('/auth/logout'),
  changePassword: (currentPassword: string, newPassword: string) =>
    api.post('/auth/change-password', { currentPassword, newPassword }),
}

export const projectsAPI = {
  getAll: () => api.get('/projects/admin/all'),
  getPublic: (params?: any) => api.get('/projects', { params }),
  create: (data: any) => api.post('/projects', data),
  update: (id: string, data: any) => api.put(`/projects/${id}`, data),
  delete: (id: string) => api.delete(`/projects/${id}`),
  toggleFeatured: (id: string) => api.patch(`/projects/${id}/toggle-featured`),
  togglePublished: (id: string) => api.patch(`/projects/${id}/toggle-published`),
}

export const blogAPI = {
  getAll: () => api.get('/blog/admin/all'),
  getPublic: (params?: any) => api.get('/blog', { params }),
  create: (data: any) => api.post('/blog', data),
  update: (id: string, data: any) => api.put(`/blog/${id}`, data),
  delete: (id: string) => api.delete(`/blog/${id}`),
  toggleFeatured: (id: string) => api.patch(`/blog/${id}/toggle-featured`),
  togglePublished: (id: string) => api.patch(`/blog/${id}/toggle-published`),
}

export const testimonialsAPI = {
  getAll: () => api.get('/testimonials/admin/all'),
  getPublic: (params?: any) => api.get('/testimonials', { params }),
  create: (data: any) => api.post('/testimonials', data),
  update: (id: string, data: any) => api.put(`/testimonials/${id}`, data),
  delete: (id: string) => api.delete(`/testimonials/${id}`),
  toggleFeatured: (id: string) => api.patch(`/testimonials/${id}/toggle-featured`),
  togglePublished: (id: string) => api.patch(`/testimonials/${id}/toggle-published`),
}

export const skillsAPI = {
  getAll: () => api.get('/skills/admin/all'),
  getPublic: (params?: any) => api.get('/skills', { params }),
  create: (data: any) => api.post('/skills', data),
  update: (id: string, data: any) => api.put(`/skills/${id}`, data),
  delete: (id: string) => api.delete(`/skills/${id}`),
  toggleVisible: (id: string) => api.patch(`/skills/${id}/toggle-visible`),
  reorder: (skills: any[]) => api.patch('/skills/reorder', { skills }),
}

export const contactAPI = {
  getAll: (params?: any) => api.get('/contact/admin/all', { params }),
  getStats: () => api.get('/contact/admin/stats'),
  getById: (id: string) => api.get(`/contact/admin/${id}`),
  updateStatus: (id: string, status: string, notes?: string) =>
    api.patch(`/contact/admin/${id}/status`, { status, notes }),
  updatePriority: (id: string, priority: string) =>
    api.patch(`/contact/admin/${id}/priority`, { priority }),
  delete: (id: string) => api.delete(`/contact/admin/${id}`),
  markAsSpam: (id: string) => api.patch(`/contact/admin/${id}/spam`),
}

export const adminAPI = {
  getDashboard: () => api.get('/admin/dashboard'),
  getAnalytics: (period?: string) => api.get('/admin/analytics', { params: { period } }),
  getSystemInfo: () => api.get('/admin/system-info'),
  createBackup: () => api.post('/admin/backup'),
}
