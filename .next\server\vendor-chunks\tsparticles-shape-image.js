"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tsparticles-shape-image";
exports.ids = ["vendor-chunks/tsparticles-shape-image"];
exports.modules = {

/***/ "(ssr)/./node_modules/tsparticles-shape-image/esm/GifUtils/ByteStream.js":
/*!*************************************************************************!*\
  !*** ./node_modules/tsparticles-shape-image/esm/GifUtils/ByteStream.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ByteStream: () => (/* binding */ ByteStream)\n/* harmony export */ });\nclass ByteStream {\n    constructor(bytes) {\n        this.pos = 0;\n        this.data = new Uint8ClampedArray(bytes);\n    }\n    getString(count) {\n        const slice = this.data.slice(this.pos, this.pos + count);\n        this.pos += slice.length;\n        return slice.reduce((acc, curr) => acc + String.fromCharCode(curr), \"\");\n    }\n    nextByte() {\n        return this.data[this.pos++];\n    }\n    nextTwoBytes() {\n        this.pos += 2;\n        return this.data[this.pos - 2] + (this.data[this.pos - 1] << 8);\n    }\n    readSubBlocks() {\n        let blockString = \"\", size = 0;\n        do {\n            size = this.data[this.pos++];\n            for (let count = size; --count >= 0; blockString += String.fromCharCode(this.data[this.pos++])) {\n            }\n        } while (size !== 0);\n        return blockString;\n    }\n    readSubBlocksBin() {\n        let size = 0, len = 0;\n        for (let offset = 0; (size = this.data[this.pos + offset]) !== 0; offset += size + 1) {\n            len += size;\n        }\n        const blockData = new Uint8Array(len);\n        for (let i = 0; (size = this.data[this.pos++]) !== 0;) {\n            for (let count = size; --count >= 0; blockData[i++] = this.data[this.pos++]) {\n            }\n        }\n        return blockData;\n    }\n    skipSubBlocks() {\n        for (; this.data[this.pos] !== 0; this.pos += this.data[this.pos] + 1) {\n        }\n        this.pos++;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-shape-image/esm/GifUtils/ByteStream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-shape-image/esm/GifUtils/Constants.js":
/*!************************************************************************!*\
  !*** ./node_modules/tsparticles-shape-image/esm/GifUtils/Constants.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InterlaceOffsets: () => (/* binding */ InterlaceOffsets),\n/* harmony export */   InterlaceSteps: () => (/* binding */ InterlaceSteps)\n/* harmony export */ });\nconst InterlaceOffsets = [0, 4, 2, 1];\nconst InterlaceSteps = [8, 8, 4, 2];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtc2hhcGUtaW1hZ2UvZXNtL0dpZlV0aWxzL0NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mdXR1cmlzdGljLXBvcnRmb2xpby8uL25vZGVfbW9kdWxlcy90c3BhcnRpY2xlcy1zaGFwZS1pbWFnZS9lc20vR2lmVXRpbHMvQ29uc3RhbnRzLmpzP2JmMjYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IEludGVybGFjZU9mZnNldHMgPSBbMCwgNCwgMiwgMV07XG5leHBvcnQgY29uc3QgSW50ZXJsYWNlU3RlcHMgPSBbOCwgOCwgNCwgMl07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-shape-image/esm/GifUtils/Constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-shape-image/esm/GifUtils/Utils.js":
/*!********************************************************************!*\
  !*** ./node_modules/tsparticles-shape-image/esm/GifUtils/Utils.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decodeGIF: () => (/* binding */ decodeGIF),\n/* harmony export */   getGIFLoopAmount: () => (/* binding */ getGIFLoopAmount)\n/* harmony export */ });\n/* harmony import */ var _Constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Constants */ \"(ssr)/./node_modules/tsparticles-shape-image/esm/GifUtils/Constants.js\");\n/* harmony import */ var _ByteStream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ByteStream */ \"(ssr)/./node_modules/tsparticles-shape-image/esm/GifUtils/ByteStream.js\");\n\n\nfunction parseColorTable(byteStream, count) {\n    const colors = [];\n    for (let i = 0; i < count; i++) {\n        colors.push({\n            r: byteStream.data[byteStream.pos],\n            g: byteStream.data[byteStream.pos + 1],\n            b: byteStream.data[byteStream.pos + 2],\n        });\n        byteStream.pos += 3;\n    }\n    return colors;\n}\nasync function parseExtensionBlock(byteStream, gif, getFrameIndex, getTransparencyIndex) {\n    switch (byteStream.nextByte()) {\n        case 249: {\n            const frame = gif.frames[getFrameIndex(false)];\n            byteStream.pos++;\n            const packedByte = byteStream.nextByte();\n            frame.GCreserved = (packedByte & 0xe0) >>> 5;\n            frame.disposalMethod = (packedByte & 0x1c) >>> 2;\n            frame.userInputDelayFlag = (packedByte & 2) === 2;\n            const transparencyFlag = (packedByte & 1) === 1;\n            frame.delayTime = byteStream.nextTwoBytes() * 0xa;\n            const transparencyIndex = byteStream.nextByte();\n            if (transparencyFlag) {\n                getTransparencyIndex(transparencyIndex);\n            }\n            byteStream.pos++;\n            break;\n        }\n        case 255: {\n            byteStream.pos++;\n            const applicationExtension = {\n                identifier: byteStream.getString(8),\n                authenticationCode: byteStream.getString(3),\n                data: byteStream.readSubBlocksBin(),\n            };\n            gif.applicationExtensions.push(applicationExtension);\n            break;\n        }\n        case 254: {\n            gif.comments.push([getFrameIndex(false), byteStream.readSubBlocks()]);\n            break;\n        }\n        case 1: {\n            if (gif.globalColorTable.length === 0) {\n                throw new EvalError(\"plain text extension without global color table\");\n            }\n            byteStream.pos++;\n            gif.frames[getFrameIndex(false)].plainTextData = {\n                left: byteStream.nextTwoBytes(),\n                top: byteStream.nextTwoBytes(),\n                width: byteStream.nextTwoBytes(),\n                height: byteStream.nextTwoBytes(),\n                charSize: {\n                    width: byteStream.nextTwoBytes(),\n                    height: byteStream.nextTwoBytes(),\n                },\n                foregroundColor: byteStream.nextByte(),\n                backgroundColor: byteStream.nextByte(),\n                text: byteStream.readSubBlocks(),\n            };\n            break;\n        }\n        default:\n            byteStream.skipSubBlocks();\n            break;\n    }\n}\nasync function parseImageBlock(byteStream, gif, avgAlpha, getFrameIndex, getTransparencyIndex, progressCallback) {\n    const frame = gif.frames[getFrameIndex(true)];\n    frame.left = byteStream.nextTwoBytes();\n    frame.top = byteStream.nextTwoBytes();\n    frame.width = byteStream.nextTwoBytes();\n    frame.height = byteStream.nextTwoBytes();\n    const packedByte = byteStream.nextByte(), localColorTableFlag = (packedByte & 0x80) === 0x80, interlacedFlag = (packedByte & 0x40) === 0x40;\n    frame.sortFlag = (packedByte & 0x20) === 0x20;\n    frame.reserved = (packedByte & 0x18) >>> 3;\n    const localColorCount = 1 << ((packedByte & 7) + 1);\n    if (localColorTableFlag) {\n        frame.localColorTable = parseColorTable(byteStream, localColorCount);\n    }\n    const getColor = (index) => {\n        const { r, g, b } = (localColorTableFlag ? frame.localColorTable : gif.globalColorTable)[index];\n        return { r, g, b, a: index === getTransparencyIndex(null) ? (avgAlpha ? ~~((r + g + b) / 3) : 0) : 255 };\n    };\n    const image = (() => {\n        try {\n            return new ImageData(frame.width, frame.height, { colorSpace: \"srgb\" });\n        }\n        catch (error) {\n            if (error instanceof DOMException && error.name === \"IndexSizeError\") {\n                return null;\n            }\n            throw error;\n        }\n    })();\n    if (image == null) {\n        throw new EvalError(\"GIF frame size is to large\");\n    }\n    const minCodeSize = byteStream.nextByte(), imageData = byteStream.readSubBlocksBin(), clearCode = 1 << minCodeSize;\n    const readBits = (pos, len) => {\n        const bytePos = pos >>> 3, bitPos = pos & 7;\n        return (((imageData[bytePos] + (imageData[bytePos + 1] << 8) + (imageData[bytePos + 2] << 16)) &\n            (((1 << len) - 1) << bitPos)) >>>\n            bitPos);\n    };\n    if (interlacedFlag) {\n        for (let code = 0, size = minCodeSize + 1, pos = 0, dic = [[0]], pass = 0; pass < 4; pass++) {\n            if (_Constants__WEBPACK_IMPORTED_MODULE_0__.InterlaceOffsets[pass] < frame.height) {\n                for (let pixelPos = 0, lineIndex = 0;;) {\n                    const last = code;\n                    code = readBits(pos, size);\n                    pos += size + 1;\n                    if (code === clearCode) {\n                        size = minCodeSize + 1;\n                        dic.length = clearCode + 2;\n                        for (let i = 0; i < dic.length; i++) {\n                            dic[i] = i < clearCode ? [i] : [];\n                        }\n                    }\n                    else {\n                        if (code >= dic.length) {\n                            dic.push(dic[last].concat(dic[last][0]));\n                        }\n                        else if (last !== clearCode) {\n                            dic.push(dic[last].concat(dic[code][0]));\n                        }\n                        for (let i = 0; i < dic[code].length; i++) {\n                            const { r, g, b, a } = getColor(dic[code][i]);\n                            image.data.set([r, g, b, a], _Constants__WEBPACK_IMPORTED_MODULE_0__.InterlaceOffsets[pass] * frame.width +\n                                _Constants__WEBPACK_IMPORTED_MODULE_0__.InterlaceSteps[pass] * lineIndex +\n                                (pixelPos % (frame.width * 4)));\n                            pixelPos += 4;\n                        }\n                        if (dic.length === 1 << size && size < 0xc) {\n                            size++;\n                        }\n                    }\n                    if (pixelPos === frame.width * 4 * (lineIndex + 1)) {\n                        lineIndex++;\n                        if (_Constants__WEBPACK_IMPORTED_MODULE_0__.InterlaceOffsets[pass] + _Constants__WEBPACK_IMPORTED_MODULE_0__.InterlaceSteps[pass] * lineIndex >= frame.height) {\n                            break;\n                        }\n                    }\n                }\n            }\n            progressCallback?.(byteStream.pos / (byteStream.data.length - 1), getFrameIndex(false) + 1, image, { x: frame.left, y: frame.top }, { width: gif.width, height: gif.height });\n        }\n        frame.image = image;\n        frame.bitmap = await createImageBitmap(image);\n    }\n    else {\n        for (let code = 0, size = minCodeSize + 1, pos = 0, dic = [[0]], pixelPos = -4;;) {\n            const last = code;\n            code = readBits(pos, size);\n            pos += size;\n            if (code === clearCode) {\n                size = minCodeSize + 1;\n                dic.length = clearCode + 2;\n                for (let i = 0; i < dic.length; i++) {\n                    dic[i] = i < clearCode ? [i] : [];\n                }\n            }\n            else {\n                if (code === clearCode + 1) {\n                    break;\n                }\n                if (code >= dic.length) {\n                    dic.push(dic[last].concat(dic[last][0]));\n                }\n                else if (last !== clearCode) {\n                    dic.push(dic[last].concat(dic[code][0]));\n                }\n                for (let i = 0; i < dic[code].length; i++) {\n                    const { r, g, b, a } = getColor(dic[code][i]);\n                    image.data.set([r, g, b, a], (pixelPos += 4));\n                }\n                if (dic.length >= 1 << size && size < 0xc) {\n                    size++;\n                }\n            }\n        }\n        frame.image = image;\n        frame.bitmap = await createImageBitmap(image);\n        progressCallback?.((byteStream.pos + 1) / byteStream.data.length, getFrameIndex(false) + 1, frame.image, { x: frame.left, y: frame.top }, { width: gif.width, height: gif.height });\n    }\n}\nasync function parseBlock(byteStream, gif, avgAlpha, getFrameIndex, getTransparencyIndex, progressCallback) {\n    switch (byteStream.nextByte()) {\n        case 59:\n            return true;\n        case 44:\n            await parseImageBlock(byteStream, gif, avgAlpha, getFrameIndex, getTransparencyIndex, progressCallback);\n            break;\n        case 33:\n            await parseExtensionBlock(byteStream, gif, getFrameIndex, getTransparencyIndex);\n            break;\n        default:\n            throw new EvalError(\"undefined block found\");\n    }\n    return false;\n}\nfunction getGIFLoopAmount(gif) {\n    for (const extension of gif.applicationExtensions) {\n        if (extension.identifier + extension.authenticationCode !== \"NETSCAPE2.0\") {\n            continue;\n        }\n        return extension.data[1] + (extension.data[2] << 8);\n    }\n    return NaN;\n}\nasync function decodeGIF(gifURL, progressCallback, avgAlpha) {\n    if (!avgAlpha)\n        avgAlpha = false;\n    const res = await fetch(gifURL);\n    if (!res.ok && res.status === 404) {\n        throw new EvalError(\"file not found\");\n    }\n    const buffer = await res.arrayBuffer();\n    const gif = {\n        width: 0,\n        height: 0,\n        totalTime: 0,\n        colorRes: 0,\n        pixelAspectRatio: 0,\n        frames: [],\n        sortFlag: false,\n        globalColorTable: [],\n        backgroundImage: new ImageData(1, 1, { colorSpace: \"srgb\" }),\n        comments: [],\n        applicationExtensions: [],\n    }, byteStream = new _ByteStream__WEBPACK_IMPORTED_MODULE_1__.ByteStream(new Uint8ClampedArray(buffer));\n    if (byteStream.getString(6) !== \"GIF89a\") {\n        throw new Error(\"not a supported GIF file\");\n    }\n    gif.width = byteStream.nextTwoBytes();\n    gif.height = byteStream.nextTwoBytes();\n    const packedByte = byteStream.nextByte(), globalColorTableFlag = (packedByte & 0x80) === 0x80;\n    gif.colorRes = (packedByte & 0x70) >>> 4;\n    gif.sortFlag = (packedByte & 8) === 8;\n    const globalColorCount = 1 << ((packedByte & 7) + 1), backgroundColorIndex = byteStream.nextByte();\n    gif.pixelAspectRatio = byteStream.nextByte();\n    if (gif.pixelAspectRatio !== 0) {\n        gif.pixelAspectRatio = (gif.pixelAspectRatio + 0xf) / 0x40;\n    }\n    if (globalColorTableFlag) {\n        gif.globalColorTable = parseColorTable(byteStream, globalColorCount);\n    }\n    const backgroundImage = (() => {\n        try {\n            return new ImageData(gif.width, gif.height, { colorSpace: \"srgb\" });\n        }\n        catch (error) {\n            if (error instanceof DOMException && error.name === \"IndexSizeError\") {\n                return null;\n            }\n            throw error;\n        }\n    })();\n    if (backgroundImage == null) {\n        throw new Error(\"GIF frame size is to large\");\n    }\n    const { r, g, b } = gif.globalColorTable[backgroundColorIndex];\n    backgroundImage.data.set(globalColorTableFlag ? [r, g, b, 255] : [0, 0, 0, 0]);\n    for (let i = 4; i < backgroundImage.data.length; i *= 2) {\n        backgroundImage.data.copyWithin(i, 0, i);\n    }\n    gif.backgroundImage = backgroundImage;\n    let frameIndex = -1, incrementFrameIndex = true, transparencyIndex = -1;\n    const getframeIndex = (increment) => {\n        if (increment) {\n            incrementFrameIndex = true;\n        }\n        return frameIndex;\n    };\n    const getTransparencyIndex = (newValue) => {\n        if (newValue != null) {\n            transparencyIndex = newValue;\n        }\n        return transparencyIndex;\n    };\n    try {\n        do {\n            if (incrementFrameIndex) {\n                gif.frames.push({\n                    left: 0,\n                    top: 0,\n                    width: 0,\n                    height: 0,\n                    disposalMethod: 0,\n                    image: new ImageData(1, 1, { colorSpace: \"srgb\" }),\n                    plainTextData: null,\n                    userInputDelayFlag: false,\n                    delayTime: 0,\n                    sortFlag: false,\n                    localColorTable: [],\n                    reserved: 0,\n                    GCreserved: 0,\n                });\n                frameIndex++;\n                transparencyIndex = -1;\n                incrementFrameIndex = false;\n            }\n        } while (!(await parseBlock(byteStream, gif, avgAlpha, getframeIndex, getTransparencyIndex, progressCallback)));\n        gif.frames.length--;\n        for (const frame of gif.frames) {\n            if (frame.userInputDelayFlag && frame.delayTime === 0) {\n                gif.totalTime = Infinity;\n                break;\n            }\n            gif.totalTime += frame.delayTime;\n        }\n        return gif;\n    }\n    catch (error) {\n        if (error instanceof EvalError) {\n            throw new Error(`error while parsing frame ${frameIndex} \"${error.message}\"`);\n        }\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtc2hhcGUtaW1hZ2UvZXNtL0dpZlV0aWxzL1V0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBK0Q7QUFDckI7QUFDMUM7QUFDQTtBQUNBLG9CQUFvQixXQUFXO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLFVBQVU7QUFDMUIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLDhEQUE4RCxvQkFBb0I7QUFDbEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1GQUFtRixVQUFVO0FBQzdGLGdCQUFnQix3REFBZ0I7QUFDaEMsc0RBQXNEO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdDQUF3QyxnQkFBZ0I7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0Msc0JBQXNCO0FBQzlELG9DQUFvQyxhQUFhO0FBQ2pELHlEQUF5RCx3REFBZ0I7QUFDekUsZ0NBQWdDLHNEQUFjO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0Qix3REFBZ0IsU0FBUyxzREFBYztBQUNuRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUhBQWlILDZCQUE2QixJQUFJLHNDQUFzQztBQUN4TDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0ZBQXdGO0FBQ3hGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyxnQkFBZ0I7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0Msc0JBQXNCO0FBQ3RELDRCQUE0QixhQUFhO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1IQUFtSCw2QkFBNkIsSUFBSSxzQ0FBc0M7QUFDMUw7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQ0FBK0Msb0JBQW9CO0FBQ25FO0FBQ0E7QUFDQSxLQUFLLG1CQUFtQixtREFBVTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwREFBMEQsb0JBQW9CO0FBQzlFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLFlBQVksVUFBVTtBQUN0QjtBQUNBLG9CQUFvQixpQ0FBaUM7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpREFBaUQsb0JBQW9CO0FBQ3JFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlEQUF5RCxZQUFZLEdBQUcsY0FBYztBQUN0RjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1dHVyaXN0aWMtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL3RzcGFydGljbGVzLXNoYXBlLWltYWdlL2VzbS9HaWZVdGlscy9VdGlscy5qcz9kYTE3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEludGVybGFjZU9mZnNldHMsIEludGVybGFjZVN0ZXBzIH0gZnJvbSBcIi4vQ29uc3RhbnRzXCI7XG5pbXBvcnQgeyBCeXRlU3RyZWFtIH0gZnJvbSBcIi4vQnl0ZVN0cmVhbVwiO1xuZnVuY3Rpb24gcGFyc2VDb2xvclRhYmxlKGJ5dGVTdHJlYW0sIGNvdW50KSB7XG4gICAgY29uc3QgY29sb3JzID0gW107XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjb3VudDsgaSsrKSB7XG4gICAgICAgIGNvbG9ycy5wdXNoKHtcbiAgICAgICAgICAgIHI6IGJ5dGVTdHJlYW0uZGF0YVtieXRlU3RyZWFtLnBvc10sXG4gICAgICAgICAgICBnOiBieXRlU3RyZWFtLmRhdGFbYnl0ZVN0cmVhbS5wb3MgKyAxXSxcbiAgICAgICAgICAgIGI6IGJ5dGVTdHJlYW0uZGF0YVtieXRlU3RyZWFtLnBvcyArIDJdLFxuICAgICAgICB9KTtcbiAgICAgICAgYnl0ZVN0cmVhbS5wb3MgKz0gMztcbiAgICB9XG4gICAgcmV0dXJuIGNvbG9ycztcbn1cbmFzeW5jIGZ1bmN0aW9uIHBhcnNlRXh0ZW5zaW9uQmxvY2soYnl0ZVN0cmVhbSwgZ2lmLCBnZXRGcmFtZUluZGV4LCBnZXRUcmFuc3BhcmVuY3lJbmRleCkge1xuICAgIHN3aXRjaCAoYnl0ZVN0cmVhbS5uZXh0Qnl0ZSgpKSB7XG4gICAgICAgIGNhc2UgMjQ5OiB7XG4gICAgICAgICAgICBjb25zdCBmcmFtZSA9IGdpZi5mcmFtZXNbZ2V0RnJhbWVJbmRleChmYWxzZSldO1xuICAgICAgICAgICAgYnl0ZVN0cmVhbS5wb3MrKztcbiAgICAgICAgICAgIGNvbnN0IHBhY2tlZEJ5dGUgPSBieXRlU3RyZWFtLm5leHRCeXRlKCk7XG4gICAgICAgICAgICBmcmFtZS5HQ3Jlc2VydmVkID0gKHBhY2tlZEJ5dGUgJiAweGUwKSA+Pj4gNTtcbiAgICAgICAgICAgIGZyYW1lLmRpc3Bvc2FsTWV0aG9kID0gKHBhY2tlZEJ5dGUgJiAweDFjKSA+Pj4gMjtcbiAgICAgICAgICAgIGZyYW1lLnVzZXJJbnB1dERlbGF5RmxhZyA9IChwYWNrZWRCeXRlICYgMikgPT09IDI7XG4gICAgICAgICAgICBjb25zdCB0cmFuc3BhcmVuY3lGbGFnID0gKHBhY2tlZEJ5dGUgJiAxKSA9PT0gMTtcbiAgICAgICAgICAgIGZyYW1lLmRlbGF5VGltZSA9IGJ5dGVTdHJlYW0ubmV4dFR3b0J5dGVzKCkgKiAweGE7XG4gICAgICAgICAgICBjb25zdCB0cmFuc3BhcmVuY3lJbmRleCA9IGJ5dGVTdHJlYW0ubmV4dEJ5dGUoKTtcbiAgICAgICAgICAgIGlmICh0cmFuc3BhcmVuY3lGbGFnKSB7XG4gICAgICAgICAgICAgICAgZ2V0VHJhbnNwYXJlbmN5SW5kZXgodHJhbnNwYXJlbmN5SW5kZXgpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYnl0ZVN0cmVhbS5wb3MrKztcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICAgIGNhc2UgMjU1OiB7XG4gICAgICAgICAgICBieXRlU3RyZWFtLnBvcysrO1xuICAgICAgICAgICAgY29uc3QgYXBwbGljYXRpb25FeHRlbnNpb24gPSB7XG4gICAgICAgICAgICAgICAgaWRlbnRpZmllcjogYnl0ZVN0cmVhbS5nZXRTdHJpbmcoOCksXG4gICAgICAgICAgICAgICAgYXV0aGVudGljYXRpb25Db2RlOiBieXRlU3RyZWFtLmdldFN0cmluZygzKSxcbiAgICAgICAgICAgICAgICBkYXRhOiBieXRlU3RyZWFtLnJlYWRTdWJCbG9ja3NCaW4oKSxcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICBnaWYuYXBwbGljYXRpb25FeHRlbnNpb25zLnB1c2goYXBwbGljYXRpb25FeHRlbnNpb24pO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgICAgY2FzZSAyNTQ6IHtcbiAgICAgICAgICAgIGdpZi5jb21tZW50cy5wdXNoKFtnZXRGcmFtZUluZGV4KGZhbHNlKSwgYnl0ZVN0cmVhbS5yZWFkU3ViQmxvY2tzKCldKTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICAgIGNhc2UgMToge1xuICAgICAgICAgICAgaWYgKGdpZi5nbG9iYWxDb2xvclRhYmxlLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFdmFsRXJyb3IoXCJwbGFpbiB0ZXh0IGV4dGVuc2lvbiB3aXRob3V0IGdsb2JhbCBjb2xvciB0YWJsZVwiKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGJ5dGVTdHJlYW0ucG9zKys7XG4gICAgICAgICAgICBnaWYuZnJhbWVzW2dldEZyYW1lSW5kZXgoZmFsc2UpXS5wbGFpblRleHREYXRhID0ge1xuICAgICAgICAgICAgICAgIGxlZnQ6IGJ5dGVTdHJlYW0ubmV4dFR3b0J5dGVzKCksXG4gICAgICAgICAgICAgICAgdG9wOiBieXRlU3RyZWFtLm5leHRUd29CeXRlcygpLFxuICAgICAgICAgICAgICAgIHdpZHRoOiBieXRlU3RyZWFtLm5leHRUd29CeXRlcygpLFxuICAgICAgICAgICAgICAgIGhlaWdodDogYnl0ZVN0cmVhbS5uZXh0VHdvQnl0ZXMoKSxcbiAgICAgICAgICAgICAgICBjaGFyU2l6ZToge1xuICAgICAgICAgICAgICAgICAgICB3aWR0aDogYnl0ZVN0cmVhbS5uZXh0VHdvQnl0ZXMoKSxcbiAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiBieXRlU3RyZWFtLm5leHRUd29CeXRlcygpLFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgZm9yZWdyb3VuZENvbG9yOiBieXRlU3RyZWFtLm5leHRCeXRlKCksXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBieXRlU3RyZWFtLm5leHRCeXRlKCksXG4gICAgICAgICAgICAgICAgdGV4dDogYnl0ZVN0cmVhbS5yZWFkU3ViQmxvY2tzKCksXG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIGJ5dGVTdHJlYW0uc2tpcFN1YkJsb2NrcygpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgfVxufVxuYXN5bmMgZnVuY3Rpb24gcGFyc2VJbWFnZUJsb2NrKGJ5dGVTdHJlYW0sIGdpZiwgYXZnQWxwaGEsIGdldEZyYW1lSW5kZXgsIGdldFRyYW5zcGFyZW5jeUluZGV4LCBwcm9ncmVzc0NhbGxiYWNrKSB7XG4gICAgY29uc3QgZnJhbWUgPSBnaWYuZnJhbWVzW2dldEZyYW1lSW5kZXgodHJ1ZSldO1xuICAgIGZyYW1lLmxlZnQgPSBieXRlU3RyZWFtLm5leHRUd29CeXRlcygpO1xuICAgIGZyYW1lLnRvcCA9IGJ5dGVTdHJlYW0ubmV4dFR3b0J5dGVzKCk7XG4gICAgZnJhbWUud2lkdGggPSBieXRlU3RyZWFtLm5leHRUd29CeXRlcygpO1xuICAgIGZyYW1lLmhlaWdodCA9IGJ5dGVTdHJlYW0ubmV4dFR3b0J5dGVzKCk7XG4gICAgY29uc3QgcGFja2VkQnl0ZSA9IGJ5dGVTdHJlYW0ubmV4dEJ5dGUoKSwgbG9jYWxDb2xvclRhYmxlRmxhZyA9IChwYWNrZWRCeXRlICYgMHg4MCkgPT09IDB4ODAsIGludGVybGFjZWRGbGFnID0gKHBhY2tlZEJ5dGUgJiAweDQwKSA9PT0gMHg0MDtcbiAgICBmcmFtZS5zb3J0RmxhZyA9IChwYWNrZWRCeXRlICYgMHgyMCkgPT09IDB4MjA7XG4gICAgZnJhbWUucmVzZXJ2ZWQgPSAocGFja2VkQnl0ZSAmIDB4MTgpID4+PiAzO1xuICAgIGNvbnN0IGxvY2FsQ29sb3JDb3VudCA9IDEgPDwgKChwYWNrZWRCeXRlICYgNykgKyAxKTtcbiAgICBpZiAobG9jYWxDb2xvclRhYmxlRmxhZykge1xuICAgICAgICBmcmFtZS5sb2NhbENvbG9yVGFibGUgPSBwYXJzZUNvbG9yVGFibGUoYnl0ZVN0cmVhbSwgbG9jYWxDb2xvckNvdW50KTtcbiAgICB9XG4gICAgY29uc3QgZ2V0Q29sb3IgPSAoaW5kZXgpID0+IHtcbiAgICAgICAgY29uc3QgeyByLCBnLCBiIH0gPSAobG9jYWxDb2xvclRhYmxlRmxhZyA/IGZyYW1lLmxvY2FsQ29sb3JUYWJsZSA6IGdpZi5nbG9iYWxDb2xvclRhYmxlKVtpbmRleF07XG4gICAgICAgIHJldHVybiB7IHIsIGcsIGIsIGE6IGluZGV4ID09PSBnZXRUcmFuc3BhcmVuY3lJbmRleChudWxsKSA/IChhdmdBbHBoYSA/IH5+KChyICsgZyArIGIpIC8gMykgOiAwKSA6IDI1NSB9O1xuICAgIH07XG4gICAgY29uc3QgaW1hZ2UgPSAoKCkgPT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgcmV0dXJuIG5ldyBJbWFnZURhdGEoZnJhbWUud2lkdGgsIGZyYW1lLmhlaWdodCwgeyBjb2xvclNwYWNlOiBcInNyZ2JcIiB9KTtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIERPTUV4Y2VwdGlvbiAmJiBlcnJvci5uYW1lID09PSBcIkluZGV4U2l6ZUVycm9yXCIpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICB9XG4gICAgfSkoKTtcbiAgICBpZiAoaW1hZ2UgPT0gbnVsbCkge1xuICAgICAgICB0aHJvdyBuZXcgRXZhbEVycm9yKFwiR0lGIGZyYW1lIHNpemUgaXMgdG8gbGFyZ2VcIik7XG4gICAgfVxuICAgIGNvbnN0IG1pbkNvZGVTaXplID0gYnl0ZVN0cmVhbS5uZXh0Qnl0ZSgpLCBpbWFnZURhdGEgPSBieXRlU3RyZWFtLnJlYWRTdWJCbG9ja3NCaW4oKSwgY2xlYXJDb2RlID0gMSA8PCBtaW5Db2RlU2l6ZTtcbiAgICBjb25zdCByZWFkQml0cyA9IChwb3MsIGxlbikgPT4ge1xuICAgICAgICBjb25zdCBieXRlUG9zID0gcG9zID4+PiAzLCBiaXRQb3MgPSBwb3MgJiA3O1xuICAgICAgICByZXR1cm4gKCgoaW1hZ2VEYXRhW2J5dGVQb3NdICsgKGltYWdlRGF0YVtieXRlUG9zICsgMV0gPDwgOCkgKyAoaW1hZ2VEYXRhW2J5dGVQb3MgKyAyXSA8PCAxNikpICZcbiAgICAgICAgICAgICgoKDEgPDwgbGVuKSAtIDEpIDw8IGJpdFBvcykpID4+PlxuICAgICAgICAgICAgYml0UG9zKTtcbiAgICB9O1xuICAgIGlmIChpbnRlcmxhY2VkRmxhZykge1xuICAgICAgICBmb3IgKGxldCBjb2RlID0gMCwgc2l6ZSA9IG1pbkNvZGVTaXplICsgMSwgcG9zID0gMCwgZGljID0gW1swXV0sIHBhc3MgPSAwOyBwYXNzIDwgNDsgcGFzcysrKSB7XG4gICAgICAgICAgICBpZiAoSW50ZXJsYWNlT2Zmc2V0c1twYXNzXSA8IGZyYW1lLmhlaWdodCkge1xuICAgICAgICAgICAgICAgIGZvciAobGV0IHBpeGVsUG9zID0gMCwgbGluZUluZGV4ID0gMDs7KSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGxhc3QgPSBjb2RlO1xuICAgICAgICAgICAgICAgICAgICBjb2RlID0gcmVhZEJpdHMocG9zLCBzaXplKTtcbiAgICAgICAgICAgICAgICAgICAgcG9zICs9IHNpemUgKyAxO1xuICAgICAgICAgICAgICAgICAgICBpZiAoY29kZSA9PT0gY2xlYXJDb2RlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBzaXplID0gbWluQ29kZVNpemUgKyAxO1xuICAgICAgICAgICAgICAgICAgICAgICAgZGljLmxlbmd0aCA9IGNsZWFyQ29kZSArIDI7XG4gICAgICAgICAgICAgICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGRpYy5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpY1tpXSA9IGkgPCBjbGVhckNvZGUgPyBbaV0gOiBbXTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChjb2RlID49IGRpYy5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaWMucHVzaChkaWNbbGFzdF0uY29uY2F0KGRpY1tsYXN0XVswXSkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgZWxzZSBpZiAobGFzdCAhPT0gY2xlYXJDb2RlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGljLnB1c2goZGljW2xhc3RdLmNvbmNhdChkaWNbY29kZV1bMF0pKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZGljW2NvZGVdLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgeyByLCBnLCBiLCBhIH0gPSBnZXRDb2xvcihkaWNbY29kZV1baV0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGltYWdlLmRhdGEuc2V0KFtyLCBnLCBiLCBhXSwgSW50ZXJsYWNlT2Zmc2V0c1twYXNzXSAqIGZyYW1lLndpZHRoICtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgSW50ZXJsYWNlU3RlcHNbcGFzc10gKiBsaW5lSW5kZXggK1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAocGl4ZWxQb3MgJSAoZnJhbWUud2lkdGggKiA0KSkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBpeGVsUG9zICs9IDQ7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoZGljLmxlbmd0aCA9PT0gMSA8PCBzaXplICYmIHNpemUgPCAweGMpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplKys7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgaWYgKHBpeGVsUG9zID09PSBmcmFtZS53aWR0aCAqIDQgKiAobGluZUluZGV4ICsgMSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGxpbmVJbmRleCsrO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKEludGVybGFjZU9mZnNldHNbcGFzc10gKyBJbnRlcmxhY2VTdGVwc1twYXNzXSAqIGxpbmVJbmRleCA+PSBmcmFtZS5oZWlnaHQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHByb2dyZXNzQ2FsbGJhY2s/LihieXRlU3RyZWFtLnBvcyAvIChieXRlU3RyZWFtLmRhdGEubGVuZ3RoIC0gMSksIGdldEZyYW1lSW5kZXgoZmFsc2UpICsgMSwgaW1hZ2UsIHsgeDogZnJhbWUubGVmdCwgeTogZnJhbWUudG9wIH0sIHsgd2lkdGg6IGdpZi53aWR0aCwgaGVpZ2h0OiBnaWYuaGVpZ2h0IH0pO1xuICAgICAgICB9XG4gICAgICAgIGZyYW1lLmltYWdlID0gaW1hZ2U7XG4gICAgICAgIGZyYW1lLmJpdG1hcCA9IGF3YWl0IGNyZWF0ZUltYWdlQml0bWFwKGltYWdlKTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIGZvciAobGV0IGNvZGUgPSAwLCBzaXplID0gbWluQ29kZVNpemUgKyAxLCBwb3MgPSAwLCBkaWMgPSBbWzBdXSwgcGl4ZWxQb3MgPSAtNDs7KSB7XG4gICAgICAgICAgICBjb25zdCBsYXN0ID0gY29kZTtcbiAgICAgICAgICAgIGNvZGUgPSByZWFkQml0cyhwb3MsIHNpemUpO1xuICAgICAgICAgICAgcG9zICs9IHNpemU7XG4gICAgICAgICAgICBpZiAoY29kZSA9PT0gY2xlYXJDb2RlKSB7XG4gICAgICAgICAgICAgICAgc2l6ZSA9IG1pbkNvZGVTaXplICsgMTtcbiAgICAgICAgICAgICAgICBkaWMubGVuZ3RoID0gY2xlYXJDb2RlICsgMjtcbiAgICAgICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGRpYy5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgICAgICAgICBkaWNbaV0gPSBpIDwgY2xlYXJDb2RlID8gW2ldIDogW107XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgaWYgKGNvZGUgPT09IGNsZWFyQ29kZSArIDEpIHtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChjb2RlID49IGRpYy5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICAgICAgZGljLnB1c2goZGljW2xhc3RdLmNvbmNhdChkaWNbbGFzdF1bMF0pKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSBpZiAobGFzdCAhPT0gY2xlYXJDb2RlKSB7XG4gICAgICAgICAgICAgICAgICAgIGRpYy5wdXNoKGRpY1tsYXN0XS5jb25jYXQoZGljW2NvZGVdWzBdKSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZGljW2NvZGVdLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgciwgZywgYiwgYSB9ID0gZ2V0Q29sb3IoZGljW2NvZGVdW2ldKTtcbiAgICAgICAgICAgICAgICAgICAgaW1hZ2UuZGF0YS5zZXQoW3IsIGcsIGIsIGFdLCAocGl4ZWxQb3MgKz0gNCkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoZGljLmxlbmd0aCA+PSAxIDw8IHNpemUgJiYgc2l6ZSA8IDB4Yykge1xuICAgICAgICAgICAgICAgICAgICBzaXplKys7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGZyYW1lLmltYWdlID0gaW1hZ2U7XG4gICAgICAgIGZyYW1lLmJpdG1hcCA9IGF3YWl0IGNyZWF0ZUltYWdlQml0bWFwKGltYWdlKTtcbiAgICAgICAgcHJvZ3Jlc3NDYWxsYmFjaz8uKChieXRlU3RyZWFtLnBvcyArIDEpIC8gYnl0ZVN0cmVhbS5kYXRhLmxlbmd0aCwgZ2V0RnJhbWVJbmRleChmYWxzZSkgKyAxLCBmcmFtZS5pbWFnZSwgeyB4OiBmcmFtZS5sZWZ0LCB5OiBmcmFtZS50b3AgfSwgeyB3aWR0aDogZ2lmLndpZHRoLCBoZWlnaHQ6IGdpZi5oZWlnaHQgfSk7XG4gICAgfVxufVxuYXN5bmMgZnVuY3Rpb24gcGFyc2VCbG9jayhieXRlU3RyZWFtLCBnaWYsIGF2Z0FscGhhLCBnZXRGcmFtZUluZGV4LCBnZXRUcmFuc3BhcmVuY3lJbmRleCwgcHJvZ3Jlc3NDYWxsYmFjaykge1xuICAgIHN3aXRjaCAoYnl0ZVN0cmVhbS5uZXh0Qnl0ZSgpKSB7XG4gICAgICAgIGNhc2UgNTk6XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgY2FzZSA0NDpcbiAgICAgICAgICAgIGF3YWl0IHBhcnNlSW1hZ2VCbG9jayhieXRlU3RyZWFtLCBnaWYsIGF2Z0FscGhhLCBnZXRGcmFtZUluZGV4LCBnZXRUcmFuc3BhcmVuY3lJbmRleCwgcHJvZ3Jlc3NDYWxsYmFjayk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAzMzpcbiAgICAgICAgICAgIGF3YWl0IHBhcnNlRXh0ZW5zaW9uQmxvY2soYnl0ZVN0cmVhbSwgZ2lmLCBnZXRGcmFtZUluZGV4LCBnZXRUcmFuc3BhcmVuY3lJbmRleCk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHRocm93IG5ldyBFdmFsRXJyb3IoXCJ1bmRlZmluZWQgYmxvY2sgZm91bmRcIik7XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBnZXRHSUZMb29wQW1vdW50KGdpZikge1xuICAgIGZvciAoY29uc3QgZXh0ZW5zaW9uIG9mIGdpZi5hcHBsaWNhdGlvbkV4dGVuc2lvbnMpIHtcbiAgICAgICAgaWYgKGV4dGVuc2lvbi5pZGVudGlmaWVyICsgZXh0ZW5zaW9uLmF1dGhlbnRpY2F0aW9uQ29kZSAhPT0gXCJORVRTQ0FQRTIuMFwiKSB7XG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZXh0ZW5zaW9uLmRhdGFbMV0gKyAoZXh0ZW5zaW9uLmRhdGFbMl0gPDwgOCk7XG4gICAgfVxuICAgIHJldHVybiBOYU47XG59XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZGVjb2RlR0lGKGdpZlVSTCwgcHJvZ3Jlc3NDYWxsYmFjaywgYXZnQWxwaGEpIHtcbiAgICBpZiAoIWF2Z0FscGhhKVxuICAgICAgICBhdmdBbHBoYSA9IGZhbHNlO1xuICAgIGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoKGdpZlVSTCk7XG4gICAgaWYgKCFyZXMub2sgJiYgcmVzLnN0YXR1cyA9PT0gNDA0KSB7XG4gICAgICAgIHRocm93IG5ldyBFdmFsRXJyb3IoXCJmaWxlIG5vdCBmb3VuZFwiKTtcbiAgICB9XG4gICAgY29uc3QgYnVmZmVyID0gYXdhaXQgcmVzLmFycmF5QnVmZmVyKCk7XG4gICAgY29uc3QgZ2lmID0ge1xuICAgICAgICB3aWR0aDogMCxcbiAgICAgICAgaGVpZ2h0OiAwLFxuICAgICAgICB0b3RhbFRpbWU6IDAsXG4gICAgICAgIGNvbG9yUmVzOiAwLFxuICAgICAgICBwaXhlbEFzcGVjdFJhdGlvOiAwLFxuICAgICAgICBmcmFtZXM6IFtdLFxuICAgICAgICBzb3J0RmxhZzogZmFsc2UsXG4gICAgICAgIGdsb2JhbENvbG9yVGFibGU6IFtdLFxuICAgICAgICBiYWNrZ3JvdW5kSW1hZ2U6IG5ldyBJbWFnZURhdGEoMSwgMSwgeyBjb2xvclNwYWNlOiBcInNyZ2JcIiB9KSxcbiAgICAgICAgY29tbWVudHM6IFtdLFxuICAgICAgICBhcHBsaWNhdGlvbkV4dGVuc2lvbnM6IFtdLFxuICAgIH0sIGJ5dGVTdHJlYW0gPSBuZXcgQnl0ZVN0cmVhbShuZXcgVWludDhDbGFtcGVkQXJyYXkoYnVmZmVyKSk7XG4gICAgaWYgKGJ5dGVTdHJlYW0uZ2V0U3RyaW5nKDYpICE9PSBcIkdJRjg5YVwiKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIm5vdCBhIHN1cHBvcnRlZCBHSUYgZmlsZVwiKTtcbiAgICB9XG4gICAgZ2lmLndpZHRoID0gYnl0ZVN0cmVhbS5uZXh0VHdvQnl0ZXMoKTtcbiAgICBnaWYuaGVpZ2h0ID0gYnl0ZVN0cmVhbS5uZXh0VHdvQnl0ZXMoKTtcbiAgICBjb25zdCBwYWNrZWRCeXRlID0gYnl0ZVN0cmVhbS5uZXh0Qnl0ZSgpLCBnbG9iYWxDb2xvclRhYmxlRmxhZyA9IChwYWNrZWRCeXRlICYgMHg4MCkgPT09IDB4ODA7XG4gICAgZ2lmLmNvbG9yUmVzID0gKHBhY2tlZEJ5dGUgJiAweDcwKSA+Pj4gNDtcbiAgICBnaWYuc29ydEZsYWcgPSAocGFja2VkQnl0ZSAmIDgpID09PSA4O1xuICAgIGNvbnN0IGdsb2JhbENvbG9yQ291bnQgPSAxIDw8ICgocGFja2VkQnl0ZSAmIDcpICsgMSksIGJhY2tncm91bmRDb2xvckluZGV4ID0gYnl0ZVN0cmVhbS5uZXh0Qnl0ZSgpO1xuICAgIGdpZi5waXhlbEFzcGVjdFJhdGlvID0gYnl0ZVN0cmVhbS5uZXh0Qnl0ZSgpO1xuICAgIGlmIChnaWYucGl4ZWxBc3BlY3RSYXRpbyAhPT0gMCkge1xuICAgICAgICBnaWYucGl4ZWxBc3BlY3RSYXRpbyA9IChnaWYucGl4ZWxBc3BlY3RSYXRpbyArIDB4ZikgLyAweDQwO1xuICAgIH1cbiAgICBpZiAoZ2xvYmFsQ29sb3JUYWJsZUZsYWcpIHtcbiAgICAgICAgZ2lmLmdsb2JhbENvbG9yVGFibGUgPSBwYXJzZUNvbG9yVGFibGUoYnl0ZVN0cmVhbSwgZ2xvYmFsQ29sb3JDb3VudCk7XG4gICAgfVxuICAgIGNvbnN0IGJhY2tncm91bmRJbWFnZSA9ICgoKSA9PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICByZXR1cm4gbmV3IEltYWdlRGF0YShnaWYud2lkdGgsIGdpZi5oZWlnaHQsIHsgY29sb3JTcGFjZTogXCJzcmdiXCIgfSk7XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBET01FeGNlcHRpb24gJiYgZXJyb3IubmFtZSA9PT0gXCJJbmRleFNpemVFcnJvclwiKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgICAgfVxuICAgIH0pKCk7XG4gICAgaWYgKGJhY2tncm91bmRJbWFnZSA9PSBudWxsKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIkdJRiBmcmFtZSBzaXplIGlzIHRvIGxhcmdlXCIpO1xuICAgIH1cbiAgICBjb25zdCB7IHIsIGcsIGIgfSA9IGdpZi5nbG9iYWxDb2xvclRhYmxlW2JhY2tncm91bmRDb2xvckluZGV4XTtcbiAgICBiYWNrZ3JvdW5kSW1hZ2UuZGF0YS5zZXQoZ2xvYmFsQ29sb3JUYWJsZUZsYWcgPyBbciwgZywgYiwgMjU1XSA6IFswLCAwLCAwLCAwXSk7XG4gICAgZm9yIChsZXQgaSA9IDQ7IGkgPCBiYWNrZ3JvdW5kSW1hZ2UuZGF0YS5sZW5ndGg7IGkgKj0gMikge1xuICAgICAgICBiYWNrZ3JvdW5kSW1hZ2UuZGF0YS5jb3B5V2l0aGluKGksIDAsIGkpO1xuICAgIH1cbiAgICBnaWYuYmFja2dyb3VuZEltYWdlID0gYmFja2dyb3VuZEltYWdlO1xuICAgIGxldCBmcmFtZUluZGV4ID0gLTEsIGluY3JlbWVudEZyYW1lSW5kZXggPSB0cnVlLCB0cmFuc3BhcmVuY3lJbmRleCA9IC0xO1xuICAgIGNvbnN0IGdldGZyYW1lSW5kZXggPSAoaW5jcmVtZW50KSA9PiB7XG4gICAgICAgIGlmIChpbmNyZW1lbnQpIHtcbiAgICAgICAgICAgIGluY3JlbWVudEZyYW1lSW5kZXggPSB0cnVlO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBmcmFtZUluZGV4O1xuICAgIH07XG4gICAgY29uc3QgZ2V0VHJhbnNwYXJlbmN5SW5kZXggPSAobmV3VmFsdWUpID0+IHtcbiAgICAgICAgaWYgKG5ld1ZhbHVlICE9IG51bGwpIHtcbiAgICAgICAgICAgIHRyYW5zcGFyZW5jeUluZGV4ID0gbmV3VmFsdWU7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRyYW5zcGFyZW5jeUluZGV4O1xuICAgIH07XG4gICAgdHJ5IHtcbiAgICAgICAgZG8ge1xuICAgICAgICAgICAgaWYgKGluY3JlbWVudEZyYW1lSW5kZXgpIHtcbiAgICAgICAgICAgICAgICBnaWYuZnJhbWVzLnB1c2goe1xuICAgICAgICAgICAgICAgICAgICBsZWZ0OiAwLFxuICAgICAgICAgICAgICAgICAgICB0b3A6IDAsXG4gICAgICAgICAgICAgICAgICAgIHdpZHRoOiAwLFxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IDAsXG4gICAgICAgICAgICAgICAgICAgIGRpc3Bvc2FsTWV0aG9kOiAwLFxuICAgICAgICAgICAgICAgICAgICBpbWFnZTogbmV3IEltYWdlRGF0YSgxLCAxLCB7IGNvbG9yU3BhY2U6IFwic3JnYlwiIH0pLFxuICAgICAgICAgICAgICAgICAgICBwbGFpblRleHREYXRhOiBudWxsLFxuICAgICAgICAgICAgICAgICAgICB1c2VySW5wdXREZWxheUZsYWc6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICBkZWxheVRpbWU6IDAsXG4gICAgICAgICAgICAgICAgICAgIHNvcnRGbGFnOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgbG9jYWxDb2xvclRhYmxlOiBbXSxcbiAgICAgICAgICAgICAgICAgICAgcmVzZXJ2ZWQ6IDAsXG4gICAgICAgICAgICAgICAgICAgIEdDcmVzZXJ2ZWQ6IDAsXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgZnJhbWVJbmRleCsrO1xuICAgICAgICAgICAgICAgIHRyYW5zcGFyZW5jeUluZGV4ID0gLTE7XG4gICAgICAgICAgICAgICAgaW5jcmVtZW50RnJhbWVJbmRleCA9IGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9IHdoaWxlICghKGF3YWl0IHBhcnNlQmxvY2soYnl0ZVN0cmVhbSwgZ2lmLCBhdmdBbHBoYSwgZ2V0ZnJhbWVJbmRleCwgZ2V0VHJhbnNwYXJlbmN5SW5kZXgsIHByb2dyZXNzQ2FsbGJhY2spKSk7XG4gICAgICAgIGdpZi5mcmFtZXMubGVuZ3RoLS07XG4gICAgICAgIGZvciAoY29uc3QgZnJhbWUgb2YgZ2lmLmZyYW1lcykge1xuICAgICAgICAgICAgaWYgKGZyYW1lLnVzZXJJbnB1dERlbGF5RmxhZyAmJiBmcmFtZS5kZWxheVRpbWUgPT09IDApIHtcbiAgICAgICAgICAgICAgICBnaWYudG90YWxUaW1lID0gSW5maW5pdHk7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBnaWYudG90YWxUaW1lICs9IGZyYW1lLmRlbGF5VGltZTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZ2lmO1xuICAgIH1cbiAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgRXZhbEVycm9yKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYGVycm9yIHdoaWxlIHBhcnNpbmcgZnJhbWUgJHtmcmFtZUluZGV4fSBcIiR7ZXJyb3IubWVzc2FnZX1cImApO1xuICAgICAgICB9XG4gICAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-shape-image/esm/GifUtils/Utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-shape-image/esm/ImageDrawer.js":
/*!*****************************************************************!*\
  !*** ./node_modules/tsparticles-shape-image/esm/ImageDrawer.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageDrawer: () => (/* binding */ ImageDrawer)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/Constants.js\");\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/tsparticles-shape-image/esm/Utils.js\");\n\n\nclass ImageDrawer {\n    constructor(engine) {\n        this.loadImageShape = async (imageShape) => {\n            if (!this._engine.loadImage) {\n                throw new Error(`${tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.errorPrefix} image shape not initialized`);\n            }\n            await this._engine.loadImage({\n                gif: imageShape.gif,\n                name: imageShape.name,\n                replaceColor: imageShape.replaceColor ?? imageShape.replace_color ?? false,\n                src: imageShape.src,\n            });\n        };\n        this._engine = engine;\n    }\n    addImage(image) {\n        if (!this._engine.images) {\n            this._engine.images = [];\n        }\n        this._engine.images.push(image);\n    }\n    draw(context, particle, radius, opacity, delta) {\n        const image = particle.image, element = image?.element;\n        if (!image) {\n            return;\n        }\n        context.globalAlpha = opacity;\n        if (image.gif && image.gifData) {\n            const offscreenCanvas = new OffscreenCanvas(image.gifData.width, image.gifData.height), offscreenContext = offscreenCanvas.getContext(\"2d\");\n            if (!offscreenContext) {\n                throw new Error(\"could not create offscreen canvas context\");\n            }\n            offscreenContext.imageSmoothingQuality = \"low\";\n            offscreenContext.imageSmoothingEnabled = false;\n            offscreenContext.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n            if (particle.gifLoopCount === undefined) {\n                particle.gifLoopCount = image.gifLoopCount ?? 0;\n            }\n            let frameIndex = particle.gifFrame ?? 0;\n            const pos = { x: -image.gifData.width * 0.5, y: -image.gifData.height * 0.5 }, frame = image.gifData.frames[frameIndex];\n            if (particle.gifTime === undefined) {\n                particle.gifTime = 0;\n            }\n            if (!frame.bitmap) {\n                return;\n            }\n            context.scale(radius / image.gifData.width, radius / image.gifData.height);\n            switch (frame.disposalMethod) {\n                case 4:\n                case 5:\n                case 6:\n                case 7:\n                case 0:\n                    offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n                    context.drawImage(offscreenCanvas, pos.x, pos.y);\n                    offscreenContext.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n                    break;\n                case 1:\n                    offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n                    context.drawImage(offscreenCanvas, pos.x, pos.y);\n                    break;\n                case 2:\n                    offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n                    context.drawImage(offscreenCanvas, pos.x, pos.y);\n                    offscreenContext.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n                    if (image.gifData.globalColorTable.length === 0) {\n                        offscreenContext.putImageData(image.gifData.frames[0].image, pos.x + frame.left, pos.y + frame.top);\n                    }\n                    else {\n                        offscreenContext.putImageData(image.gifData.backgroundImage, pos.x, pos.y);\n                    }\n                    break;\n                case 3:\n                    {\n                        const previousImageData = offscreenContext.getImageData(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n                        offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n                        context.drawImage(offscreenCanvas, pos.x, pos.y);\n                        offscreenContext.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n                        offscreenContext.putImageData(previousImageData, 0, 0);\n                    }\n                    break;\n            }\n            particle.gifTime += delta.value;\n            if (particle.gifTime > frame.delayTime) {\n                particle.gifTime -= frame.delayTime;\n                if (++frameIndex >= image.gifData.frames.length) {\n                    if (--particle.gifLoopCount <= 0) {\n                        return;\n                    }\n                    frameIndex = 0;\n                    offscreenContext.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n                }\n                particle.gifFrame = frameIndex;\n            }\n            context.scale(image.gifData.width / radius, image.gifData.height / radius);\n        }\n        else if (element) {\n            const ratio = image.ratio, pos = {\n                x: -radius,\n                y: -radius,\n            };\n            context.drawImage(element, pos.x, pos.y, radius * 2, (radius * 2) / ratio);\n        }\n        context.globalAlpha = 1;\n    }\n    getSidesCount() {\n        return 12;\n    }\n    async init(container) {\n        const options = container.actualOptions;\n        if (!options.preload || !this._engine.loadImage) {\n            return;\n        }\n        for (const imageData of options.preload) {\n            await this._engine.loadImage(imageData);\n        }\n    }\n    loadShape(particle) {\n        if (particle.shape !== \"image\" && particle.shape !== \"images\") {\n            return;\n        }\n        if (!this._engine.images) {\n            this._engine.images = [];\n        }\n        const imageData = particle.shapeData, image = this._engine.images.find((t) => t.name === imageData.name || t.source === imageData.src);\n        if (!image) {\n            this.loadImageShape(imageData).then(() => {\n                this.loadShape(particle);\n            });\n        }\n    }\n    particleInit(container, particle) {\n        if (particle.shape !== \"image\" && particle.shape !== \"images\") {\n            return;\n        }\n        if (!this._engine.images) {\n            this._engine.images = [];\n        }\n        const images = this._engine.images, imageData = particle.shapeData, color = particle.getFillColor(), image = images.find((t) => t.name === imageData.name || t.source === imageData.src);\n        if (!image) {\n            return;\n        }\n        const replaceColor = imageData.replaceColor ?? imageData.replace_color ?? image.replaceColor;\n        if (image.loading) {\n            setTimeout(() => {\n                this.particleInit(container, particle);\n            });\n            return;\n        }\n        (async () => {\n            let imageRes;\n            if (image.svgData && color) {\n                imageRes = await (0,_Utils__WEBPACK_IMPORTED_MODULE_1__.replaceImageColor)(image, imageData, color, particle);\n            }\n            else {\n                imageRes = {\n                    color,\n                    data: image,\n                    element: image.element,\n                    gif: image.gif,\n                    gifData: image.gifData,\n                    gifLoopCount: image.gifLoopCount,\n                    loaded: true,\n                    ratio: imageData.width && imageData.height ? imageData.width / imageData.height : image.ratio ?? 1,\n                    replaceColor: replaceColor,\n                    source: imageData.src,\n                };\n            }\n            if (!imageRes.ratio) {\n                imageRes.ratio = 1;\n            }\n            const fill = imageData.fill ?? particle.fill, close = imageData.close ?? particle.close, imageShape = {\n                image: imageRes,\n                fill,\n                close,\n            };\n            particle.image = imageShape.image;\n            particle.fill = imageShape.fill;\n            particle.close = imageShape.close;\n        })();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-shape-image/esm/ImageDrawer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-shape-image/esm/ImagePreloader.js":
/*!********************************************************************!*\
  !*** ./node_modules/tsparticles-shape-image/esm/ImagePreloader.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImagePreloaderPlugin: () => (/* binding */ ImagePreloaderPlugin)\n/* harmony export */ });\n/* harmony import */ var _Options_Classes_Preload__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Options/Classes/Preload */ \"(ssr)/./node_modules/tsparticles-shape-image/esm/Options/Classes/Preload.js\");\n\nclass ImagePreloaderPlugin {\n    constructor(engine) {\n        this.id = \"imagePreloader\";\n        this._engine = engine;\n    }\n    getPlugin() {\n        return {};\n    }\n    loadOptions(options, source) {\n        if (!source || !source.preload) {\n            return;\n        }\n        if (!options.preload) {\n            options.preload = [];\n        }\n        const preloadOptions = options.preload;\n        for (const item of source.preload) {\n            const existing = preloadOptions.find((t) => t.name === item.name || t.src === item.src);\n            if (existing) {\n                existing.load(item);\n            }\n            else {\n                const preload = new _Options_Classes_Preload__WEBPACK_IMPORTED_MODULE_0__.Preload();\n                preload.load(item);\n                preloadOptions.push(preload);\n            }\n        }\n    }\n    needsPlugin() {\n        return true;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtc2hhcGUtaW1hZ2UvZXNtL0ltYWdlUHJlbG9hZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9EO0FBQzdDO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLDZEQUFPO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1dHVyaXN0aWMtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL3RzcGFydGljbGVzLXNoYXBlLWltYWdlL2VzbS9JbWFnZVByZWxvYWRlci5qcz9jZmZiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByZWxvYWQgfSBmcm9tIFwiLi9PcHRpb25zL0NsYXNzZXMvUHJlbG9hZFwiO1xuZXhwb3J0IGNsYXNzIEltYWdlUHJlbG9hZGVyUGx1Z2luIHtcbiAgICBjb25zdHJ1Y3RvcihlbmdpbmUpIHtcbiAgICAgICAgdGhpcy5pZCA9IFwiaW1hZ2VQcmVsb2FkZXJcIjtcbiAgICAgICAgdGhpcy5fZW5naW5lID0gZW5naW5lO1xuICAgIH1cbiAgICBnZXRQbHVnaW4oKSB7XG4gICAgICAgIHJldHVybiB7fTtcbiAgICB9XG4gICAgbG9hZE9wdGlvbnMob3B0aW9ucywgc291cmNlKSB7XG4gICAgICAgIGlmICghc291cmNlIHx8ICFzb3VyY2UucHJlbG9hZCkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGlmICghb3B0aW9ucy5wcmVsb2FkKSB7XG4gICAgICAgICAgICBvcHRpb25zLnByZWxvYWQgPSBbXTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBwcmVsb2FkT3B0aW9ucyA9IG9wdGlvbnMucHJlbG9hZDtcbiAgICAgICAgZm9yIChjb25zdCBpdGVtIG9mIHNvdXJjZS5wcmVsb2FkKSB7XG4gICAgICAgICAgICBjb25zdCBleGlzdGluZyA9IHByZWxvYWRPcHRpb25zLmZpbmQoKHQpID0+IHQubmFtZSA9PT0gaXRlbS5uYW1lIHx8IHQuc3JjID09PSBpdGVtLnNyYyk7XG4gICAgICAgICAgICBpZiAoZXhpc3RpbmcpIHtcbiAgICAgICAgICAgICAgICBleGlzdGluZy5sb2FkKGl0ZW0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgY29uc3QgcHJlbG9hZCA9IG5ldyBQcmVsb2FkKCk7XG4gICAgICAgICAgICAgICAgcHJlbG9hZC5sb2FkKGl0ZW0pO1xuICAgICAgICAgICAgICAgIHByZWxvYWRPcHRpb25zLnB1c2gocHJlbG9hZCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgbmVlZHNQbHVnaW4oKSB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-shape-image/esm/ImagePreloader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-shape-image/esm/Options/Classes/Preload.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/tsparticles-shape-image/esm/Options/Classes/Preload.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Preload: () => (/* binding */ Preload)\n/* harmony export */ });\nclass Preload {\n    constructor() {\n        this.src = \"\";\n        this.gif = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.gif !== undefined) {\n            this.gif = data.gif;\n        }\n        if (data.height !== undefined) {\n            this.height = data.height;\n        }\n        if (data.name !== undefined) {\n            this.name = data.name;\n        }\n        if (data.replaceColor !== undefined) {\n            this.replaceColor = data.replaceColor;\n        }\n        if (data.src !== undefined) {\n            this.src = data.src;\n        }\n        if (data.width !== undefined) {\n            this.width = data.width;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtc2hhcGUtaW1hZ2UvZXNtL09wdGlvbnMvQ2xhc3Nlcy9QcmVsb2FkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnV0dXJpc3RpYy1wb3J0Zm9saW8vLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtc2hhcGUtaW1hZ2UvZXNtL09wdGlvbnMvQ2xhc3Nlcy9QcmVsb2FkLmpzPzEyYjkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNsYXNzIFByZWxvYWQge1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICB0aGlzLnNyYyA9IFwiXCI7XG4gICAgICAgIHRoaXMuZ2lmID0gZmFsc2U7XG4gICAgfVxuICAgIGxvYWQoZGF0YSkge1xuICAgICAgICBpZiAoIWRhdGEpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5naWYgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5naWYgPSBkYXRhLmdpZjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5oZWlnaHQgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5oZWlnaHQgPSBkYXRhLmhlaWdodDtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5uYW1lICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHRoaXMubmFtZSA9IGRhdGEubmFtZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5yZXBsYWNlQ29sb3IgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5yZXBsYWNlQ29sb3IgPSBkYXRhLnJlcGxhY2VDb2xvcjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5zcmMgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5zcmMgPSBkYXRhLnNyYztcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS53aWR0aCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aGlzLndpZHRoID0gZGF0YS53aWR0aDtcbiAgICAgICAgfVxuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-shape-image/esm/Options/Classes/Preload.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-shape-image/esm/Utils.js":
/*!***********************************************************!*\
  !*** ./node_modules/tsparticles-shape-image/esm/Utils.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   downloadSvgImage: () => (/* binding */ downloadSvgImage),\n/* harmony export */   loadGifImage: () => (/* binding */ loadGifImage),\n/* harmony export */   loadImage: () => (/* binding */ loadImage),\n/* harmony export */   replaceImageColor: () => (/* binding */ replaceImageColor)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/ColorUtils.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/Utils.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/Constants.js\");\n/* harmony import */ var _GifUtils_Utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./GifUtils/Utils */ \"(ssr)/./node_modules/tsparticles-shape-image/esm/GifUtils/Utils.js\");\n\n\nconst currentColorRegex = /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\\((-?\\d+%?[,\\s]+){2,3}\\s*[\\d.]+%?\\))|currentcolor/gi;\nfunction replaceColorSvg(imageShape, color, opacity) {\n    const { svgData } = imageShape;\n    if (!svgData) {\n        return \"\";\n    }\n    const colorStyle = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getStyleFromHsl)(color, opacity);\n    if (svgData.includes(\"fill\")) {\n        return svgData.replace(currentColorRegex, () => colorStyle);\n    }\n    const preFillIndex = svgData.indexOf(\">\");\n    return `${svgData.substring(0, preFillIndex)} fill=\"${colorStyle}\"${svgData.substring(preFillIndex)}`;\n}\nasync function loadImage(image) {\n    return new Promise((resolve) => {\n        image.loading = true;\n        const img = new Image();\n        image.element = img;\n        img.addEventListener(\"load\", () => {\n            image.loading = false;\n            resolve();\n        });\n        img.addEventListener(\"error\", () => {\n            image.element = undefined;\n            image.error = true;\n            image.loading = false;\n            (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getLogger)().error(`${tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.errorPrefix} loading image: ${image.source}`);\n            resolve();\n        });\n        img.src = image.source;\n    });\n}\nasync function loadGifImage(image) {\n    if (image.type !== \"gif\") {\n        await loadImage(image);\n        return;\n    }\n    image.loading = true;\n    try {\n        image.gifData = await (0,_GifUtils_Utils__WEBPACK_IMPORTED_MODULE_3__.decodeGIF)(image.source);\n        image.gifLoopCount = (0,_GifUtils_Utils__WEBPACK_IMPORTED_MODULE_3__.getGIFLoopAmount)(image.gifData) ?? 0;\n        if (image.gifLoopCount === 0) {\n            image.gifLoopCount = Infinity;\n        }\n    }\n    catch {\n        image.error = true;\n    }\n    image.loading = false;\n}\nasync function downloadSvgImage(image) {\n    if (image.type !== \"svg\") {\n        await loadImage(image);\n        return;\n    }\n    image.loading = true;\n    const response = await fetch(image.source);\n    if (!response.ok) {\n        (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getLogger)().error(`${tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.errorPrefix} Image not found`);\n        image.error = true;\n    }\n    else {\n        image.svgData = await response.text();\n    }\n    image.loading = false;\n}\nfunction replaceImageColor(image, imageData, color, particle) {\n    const svgColoredData = replaceColorSvg(image, color, particle.opacity?.value ?? 1), imageRes = {\n        color,\n        gif: imageData.gif,\n        data: {\n            ...image,\n            svgData: svgColoredData,\n        },\n        loaded: false,\n        ratio: imageData.width / imageData.height,\n        replaceColor: imageData.replaceColor ?? imageData.replace_color,\n        source: imageData.src,\n    };\n    return new Promise((resolve) => {\n        const svg = new Blob([svgColoredData], { type: \"image/svg+xml\" }), domUrl = URL || window.URL || window.webkitURL || window, url = domUrl.createObjectURL(svg), img = new Image();\n        img.addEventListener(\"load\", () => {\n            imageRes.loaded = true;\n            imageRes.element = img;\n            resolve(imageRes);\n            domUrl.revokeObjectURL(url);\n        });\n        img.addEventListener(\"error\", async () => {\n            domUrl.revokeObjectURL(url);\n            const img2 = {\n                ...image,\n                error: false,\n                loading: true,\n            };\n            await loadImage(img2);\n            imageRes.loaded = true;\n            imageRes.element = img2.element;\n            resolve(imageRes);\n        });\n        img.src = url;\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-shape-image/esm/Utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-shape-image/esm/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/tsparticles-shape-image/esm/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadImageShape: () => (/* binding */ loadImageShape)\n/* harmony export */ });\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/tsparticles-shape-image/esm/Utils.js\");\n/* harmony import */ var _ImageDrawer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ImageDrawer */ \"(ssr)/./node_modules/tsparticles-shape-image/esm/ImageDrawer.js\");\n/* harmony import */ var _ImagePreloader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ImagePreloader */ \"(ssr)/./node_modules/tsparticles-shape-image/esm/ImagePreloader.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/Constants.js\");\n\n\n\n\nfunction addLoadImageToEngine(engine) {\n    if (engine.loadImage) {\n        return;\n    }\n    engine.loadImage = async (data) => {\n        if (!data.name && !data.src) {\n            throw new Error(`${tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.errorPrefix} no image source provided`);\n        }\n        if (!engine.images) {\n            engine.images = [];\n        }\n        if (engine.images.find((t) => t.name === data.name || t.source === data.src)) {\n            return;\n        }\n        try {\n            const image = {\n                gif: data.gif ?? false,\n                name: data.name ?? data.src,\n                source: data.src,\n                type: data.src.substring(data.src.length - 3),\n                error: false,\n                loading: true,\n                replaceColor: data.replaceColor,\n                ratio: data.width && data.height ? data.width / data.height : undefined,\n            };\n            engine.images.push(image);\n            const imageFunc = data.gif ? _Utils__WEBPACK_IMPORTED_MODULE_1__.loadGifImage : data.replaceColor ? _Utils__WEBPACK_IMPORTED_MODULE_1__.downloadSvgImage : _Utils__WEBPACK_IMPORTED_MODULE_1__.loadImage;\n            await imageFunc(image);\n        }\n        catch {\n            throw new Error(`${tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.errorPrefix} ${data.name ?? data.src} not found`);\n        }\n    };\n}\nasync function loadImageShape(engine, refresh = true) {\n    addLoadImageToEngine(engine);\n    const preloader = new _ImagePreloader__WEBPACK_IMPORTED_MODULE_2__.ImagePreloaderPlugin(engine);\n    await engine.addPlugin(preloader, refresh);\n    await engine.addShape([\"image\", \"images\"], new _ImageDrawer__WEBPACK_IMPORTED_MODULE_3__.ImageDrawer(engine), refresh);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-shape-image/esm/index.js\n");

/***/ })

};
;