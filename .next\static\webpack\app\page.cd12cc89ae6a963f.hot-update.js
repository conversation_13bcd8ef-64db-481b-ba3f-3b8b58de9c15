"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Footer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Code_Coffee_Github_Heart_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Coffee,Github,Heart,Linkedin,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Coffee_Github_Heart_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Coffee,Github,Heart,Linkedin,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Coffee_Github_Heart_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Coffee,Github,Heart,Linkedin,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Coffee_Github_Heart_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Coffee,Github,Heart,Linkedin,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Coffee_Github_Heart_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Coffee,Github,Heart,Linkedin,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Coffee_Github_Heart_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Coffee,Github,Heart,Linkedin,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Coffee_Github_Heart_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Coffee,Github,Heart,Linkedin,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Coffee_Github_Heart_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Coffee,Github,Heart,Linkedin,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coffee.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Coffee_Github_Heart_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Coffee,Github,Heart,Linkedin,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst socialLinks = [\n    {\n        name: \"GitHub\",\n        icon: _barrel_optimize_names_Code_Coffee_Github_Heart_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        url: \"https://github.com/yourusername\",\n        color: \"hover:text-white\"\n    },\n    {\n        name: \"LinkedIn\",\n        icon: _barrel_optimize_names_Code_Coffee_Github_Heart_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        url: \"https://linkedin.com/in/yourusername\",\n        color: \"hover:text-blue-400\"\n    },\n    {\n        name: \"Twitter\",\n        icon: _barrel_optimize_names_Code_Coffee_Github_Heart_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        url: \"https://twitter.com/yourusername\",\n        color: \"hover:text-blue-400\"\n    },\n    {\n        name: \"Email\",\n        icon: _barrel_optimize_names_Code_Coffee_Github_Heart_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        url: \"mailto:<EMAIL>\",\n        color: \"hover:text-neon-blue\"\n    }\n];\nconst quickLinks = [\n    {\n        name: \"Home\",\n        href: \"#home\"\n    },\n    {\n        name: \"About\",\n        href: \"#about\"\n    },\n    {\n        name: \"Projects\",\n        href: \"#projects\"\n    },\n    {\n        name: \"Skills\",\n        href: \"#skills\"\n    },\n    {\n        name: \"Resume\",\n        href: \"#resume\"\n    },\n    {\n        name: \"Blog\",\n        href: \"#blog\"\n    },\n    {\n        name: \"Contact\",\n        href: \"#contact\"\n    }\n];\nconst services = [\n    \"Web Development\",\n    \"AI Integration\",\n    \"Full-Stack Solutions\",\n    \"Performance Optimization\",\n    \"Technical Consulting\",\n    \"Code Review\"\n];\nfunction Footer() {\n    const scrollToSection = (href)=>{\n        const element = document.querySelector(href);\n        if (element) {\n            element.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    };\n    const currentYear = new Date().getFullYear();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"relative bg-black border-t border-gray-800 mt-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 cyber-grid opacity-5\"\n            }, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent\"\n            }, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid lg:grid-cols-4 md:grid-cols-2 gap-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-cyber font-bold text-neon-blue neon-glow-subtle mb-4\",\n                                            children: \"<YourName />\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 mb-6 leading-relaxed\",\n                                            children: \"Passionate self-taught developer building the future with AI, web technologies, and creative coding solutions.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-gray-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Coffee_Github_Heart_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"mr-3 text-neon-blue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 89,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"San Francisco, CA\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-gray-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Coffee_Github_Heart_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"mr-3 text-neon-blue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 93,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"+****************\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-gray-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Coffee_Github_Heart_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"mr-3 text-neon-blue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 97,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"<EMAIL>\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-tech font-semibold text-white mb-6\",\n                                            children: \"Quick Links\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3\",\n                                            children: quickLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>scrollToSection(link.href),\n                                                        className: \"text-gray-400 hover:text-neon-blue transition-colors duration-300 font-tech\",\n                                                        children: link.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, link.name, false, {\n                                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.2\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-tech font-semibold text-white mb-6\",\n                                            children: \"Services\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3\",\n                                            children: services.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"text-gray-400 font-tech\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-neon-green mr-2\",\n                                                            children: \"▸\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        service\n                                                    ]\n                                                }, service, true, {\n                                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.3\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-tech font-semibold text-white mb-6\",\n                                            children: \"Connect With Me\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4 mb-8\",\n                                            children: socialLinks.map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.a, {\n                                                    href: social.url,\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"p-3 bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg text-gray-400 \".concat(social.color, \" transition-all duration-300\"),\n                                                    whileHover: {\n                                                        scale: 1.1,\n                                                        y: -2\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(social.icon, {\n                                                        size: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, social.name, false, {\n                                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-900/30 backdrop-blur-sm border border-gray-700 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"text-white font-tech font-medium mb-3\",\n                                                    children: \"Stay Updated\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm mb-4\",\n                                                    children: \"Get notified about new projects and blog posts.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"email\",\n                                                            placeholder: \"Enter your email\",\n                                                            className: \"flex-1 px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-l-md text-white text-sm placeholder-gray-400 focus:outline-none focus:border-neon-blue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"px-4 py-2 bg-neon-blue text-black rounded-r-md hover:bg-white transition-colors font-tech font-medium text-sm\",\n                                                            children: \"Subscribe\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.4\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"border-t border-gray-800 mt-12 pt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-gray-400 text-sm mb-4 md:mb-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"\\xa9 \",\n                                                    currentYear,\n                                                    \" Your Name. Made with\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Coffee_Github_Heart_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                size: 16,\n                                                className: \"mx-2 text-red-500 fill-current\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"and\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Coffee_Github_Heart_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: 16,\n                                                className: \"mx-2 text-yellow-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"using\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Coffee_Github_Heart_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                size: 16,\n                                                className: \"ml-2 text-neon-blue\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 text-xs text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Next.js\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"w-2 h-2 bg-cyan-500 rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Tailwind CSS\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"w-2 h-2 bg-purple-500 rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Framer Motion\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"w-2 h-2 bg-orange-500 rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Three.js\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/30 backdrop-blur-sm border border-gray-700 rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-neon-blue font-cyber font-bold text-lg\",\n                                                children: \"☕\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-400 text-xs font-tech\",\n                                                children: \"Cups of Coffee\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white text-sm font-tech\",\n                                                children: \"∞\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/30 backdrop-blur-sm border border-gray-700 rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-neon-green font-cyber font-bold text-lg\",\n                                                children: \"\\uD83D\\uDE80\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-400 text-xs font-tech\",\n                                                children: \"Projects Launched\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white text-sm font-tech\",\n                                                children: \"50+\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/30 backdrop-blur-sm border border-gray-700 rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-neon-pink font-cyber font-bold text-lg\",\n                                                children: \"\\uD83E\\uDDE0\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-400 text-xs font-tech\",\n                                                children: \"Lines of Code\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white text-sm font-tech\",\n                                                children: \"100K+\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/30 backdrop-blur-sm border border-gray-700 rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-neon-purple font-cyber font-bold text-lg\",\n                                                children: \"⭐\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-400 text-xs font-tech\",\n                                                children: \"GitHub Stars\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white text-sm font-tech\",\n                                                children: \"500+\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-10 left-10 w-4 h-4 border border-neon-blue/30 rotate-45 animate-pulse\"\n            }, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-20 right-20 w-6 h-6 border border-neon-green/30 rounded-full animate-bounce\"\n            }, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-10 left-1/4 w-3 h-3 bg-neon-pink/30 rounded-full animate-ping\"\n            }, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Footer.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n_c = Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Footer.tsx\n"));

/***/ })

});