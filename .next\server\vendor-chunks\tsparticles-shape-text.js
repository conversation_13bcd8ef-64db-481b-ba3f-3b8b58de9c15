"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tsparticles-shape-text";
exports.ids = ["vendor-chunks/tsparticles-shape-text"];
exports.modules = {

/***/ "(ssr)/./node_modules/tsparticles-shape-text/esm/TextDrawer.js":
/*!***************************************************************!*\
  !*** ./node_modules/tsparticles-shape-text/esm/TextDrawer.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TextDrawer: () => (/* binding */ TextDrawer),\n/* harmony export */   validTypes: () => (/* binding */ validTypes)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/Utils.js\");\n\nconst validTypes = [\"text\", \"character\", \"char\"];\nclass TextDrawer {\n    draw(context, particle, radius, opacity) {\n        const character = particle.shapeData;\n        if (character === undefined) {\n            return;\n        }\n        const textData = character.value;\n        if (textData === undefined) {\n            return;\n        }\n        if (particle.text === undefined) {\n            particle.text = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.itemFromSingleOrMultiple)(textData, particle.randomIndexData);\n        }\n        const text = particle.text, style = character.style ?? \"\", weight = character.weight ?? \"400\", size = Math.round(radius) * 2, font = character.font ?? \"Verdana\", fill = particle.fill, offsetX = (text.length * radius) / 2;\n        context.font = `${style} ${weight} ${size}px \"${font}\"`;\n        const pos = {\n            x: -offsetX,\n            y: radius / 2,\n        };\n        context.globalAlpha = opacity;\n        if (fill) {\n            context.fillText(text, pos.x, pos.y);\n        }\n        else {\n            context.strokeText(text, pos.x, pos.y);\n        }\n        context.globalAlpha = 1;\n    }\n    getSidesCount() {\n        return 12;\n    }\n    async init(container) {\n        const options = container.actualOptions;\n        if (validTypes.find((t) => (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isInArray)(t, options.particles.shape.type))) {\n            const shapeOptions = validTypes\n                .map((t) => options.particles.shape.options[t])\n                .find((t) => !!t), promises = [];\n            (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.executeOnSingleOrMultiple)(shapeOptions, (shape) => {\n                promises.push((0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.loadFont)(shape.font, shape.weight));\n            });\n            await Promise.all(promises);\n        }\n    }\n    particleInit(container, particle) {\n        if (!particle.shape || !validTypes.includes(particle.shape)) {\n            return;\n        }\n        const character = particle.shapeData;\n        if (character === undefined) {\n            return;\n        }\n        const textData = character.value;\n        if (textData === undefined) {\n            return;\n        }\n        particle.text = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.itemFromSingleOrMultiple)(textData, particle.randomIndexData);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-shape-text/esm/TextDrawer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-shape-text/esm/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/tsparticles-shape-text/esm/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadTextShape: () => (/* binding */ loadTextShape)\n/* harmony export */ });\n/* harmony import */ var _TextDrawer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TextDrawer */ \"(ssr)/./node_modules/tsparticles-shape-text/esm/TextDrawer.js\");\n\nasync function loadTextShape(engine, refresh = true) {\n    await engine.addShape(_TextDrawer__WEBPACK_IMPORTED_MODULE_0__.validTypes, new _TextDrawer__WEBPACK_IMPORTED_MODULE_0__.TextDrawer(), refresh);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtc2hhcGUtdGV4dC9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBc0Q7QUFDL0M7QUFDUCwwQkFBMEIsbURBQVUsTUFBTSxtREFBVTtBQUNwRCIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1dHVyaXN0aWMtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL3RzcGFydGljbGVzLXNoYXBlLXRleHQvZXNtL2luZGV4LmpzPzI0OTIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVGV4dERyYXdlciwgdmFsaWRUeXBlcyB9IGZyb20gXCIuL1RleHREcmF3ZXJcIjtcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBsb2FkVGV4dFNoYXBlKGVuZ2luZSwgcmVmcmVzaCA9IHRydWUpIHtcbiAgICBhd2FpdCBlbmdpbmUuYWRkU2hhcGUodmFsaWRUeXBlcywgbmV3IFRleHREcmF3ZXIoKSwgcmVmcmVzaCk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-shape-text/esm/index.js\n");

/***/ })

};
;