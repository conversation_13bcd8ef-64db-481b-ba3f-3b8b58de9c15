const mongoose = require('mongoose');

const blogPostSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true
  },
  excerpt: {
    type: String,
    required: true,
    maxlength: 300
  },
  content: {
    type: String,
    required: true
  },
  image: {
    type: String,
    default: ''
  },
  images: [{
    url: String,
    alt: String,
    caption: String
  }],
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  category: {
    type: String,
    required: true,
    enum: ['web-development', 'ai', 'technology', 'career', 'tutorial', 'personal', 'review'],
    default: 'web-development'
  },
  author: {
    name: {
      type: String,
      default: 'Your Name'
    },
    bio: {
      type: String,
      default: 'Passionate self-taught developer'
    },
    avatar: {
      type: String,
      default: ''
    }
  },
  readTime: {
    type: Number, // in minutes
    default: 5
  },
  isPublished: {
    type: Boolean,
    default: false
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  publishedAt: {
    type: Date
  },
  views: {
    type: Number,
    default: 0
  },
  likes: {
    type: Number,
    default: 0
  },
  shares: {
    type: Number,
    default: 0
  },
  seo: {
    metaTitle: String,
    metaDescription: String,
    keywords: [String]
  }
}, {
  timestamps: true
});

// Index for better query performance
blogPostSchema.index({ isPublished: 1, publishedAt: -1 });
blogPostSchema.index({ slug: 1 });
blogPostSchema.index({ tags: 1, isPublished: 1 });
blogPostSchema.index({ category: 1, isPublished: 1 });

// Pre-save middleware to generate slug
blogPostSchema.pre('save', function(next) {
  if (this.isModified('title') && !this.slug) {
    this.slug = this.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }
  
  // Set publishedAt when publishing
  if (this.isModified('isPublished') && this.isPublished && !this.publishedAt) {
    this.publishedAt = new Date();
  }
  
  // Calculate read time based on content
  if (this.isModified('content')) {
    const wordsPerMinute = 200;
    const wordCount = this.content.split(/\s+/).length;
    this.readTime = Math.ceil(wordCount / wordsPerMinute);
  }
  
  next();
});

// Virtual for formatted date
blogPostSchema.virtual('formattedDate').get(function() {
  const date = this.publishedAt || this.createdAt;
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
});

// Method to increment views
blogPostSchema.methods.incrementViews = function() {
  this.views += 1;
  return this.save();
};

// Method to increment likes
blogPostSchema.methods.incrementLikes = function() {
  this.likes += 1;
  return this.save();
};

// Method to increment shares
blogPostSchema.methods.incrementShares = function() {
  this.shares += 1;
  return this.save();
};

// Static method to get published posts
blogPostSchema.statics.getPublished = function() {
  return this.find({ isPublished: true })
    .sort({ publishedAt: -1 });
};

// Static method to get featured posts
blogPostSchema.statics.getFeatured = function() {
  return this.find({ 
    isPublished: true, 
    isFeatured: true 
  }).sort({ publishedAt: -1 });
};

// Static method to get posts by category
blogPostSchema.statics.getByCategory = function(category) {
  return this.find({ 
    isPublished: true, 
    category: category 
  }).sort({ publishedAt: -1 });
};

// Static method to search posts
blogPostSchema.statics.search = function(query) {
  return this.find({
    isPublished: true,
    $or: [
      { title: { $regex: query, $options: 'i' } },
      { excerpt: { $regex: query, $options: 'i' } },
      { content: { $regex: query, $options: 'i' } },
      { tags: { $in: [new RegExp(query, 'i')] } }
    ]
  }).sort({ publishedAt: -1 });
};

module.exports = mongoose.model('BlogPost', blogPostSchema);
