'use client'

import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { useState } from 'react'

const skillCategories = [
  {
    name: 'Frontend',
    color: 'neon-blue',
    skills: [
      { name: 'HTML/CSS', level: 95, icon: '🌐' },
      { name: 'JavaScript', level: 90, icon: '⚡' },
      { name: 'React', level: 85, icon: '⚛️' },
      { name: 'Next.js', level: 80, icon: '🔺' },
      { name: 'Tailwind CSS', level: 88, icon: '🎨' },
      { name: 'TypeScript', level: 75, icon: '📘' },
    ]
  },
  {
    name: 'Backend',
    color: 'neon-green',
    skills: [
      { name: 'Python', level: 88, icon: '🐍' },
      { name: 'FastAPI', level: 80, icon: '⚡' },
      { name: 'Django', level: 75, icon: '🎯' },
      { name: 'Node.js', level: 70, icon: '🟢' },
      { name: 'PostgreSQL', level: 78, icon: '🐘' },
      { name: 'MongoD<PERSON>', level: 72, icon: '🍃' },
    ]
  },
  {
    name: 'DevOps & Tools',
    color: 'neon-pink',
    skills: [
      { name: 'Docker', level: 70, icon: '🐳' },
      { name: 'Git', level: 85, icon: '📚' },
      { name: 'Linux', level: 75, icon: '🐧' },
      { name: 'AWS', level: 65, icon: '☁️' },
      { name: 'CI/CD', level: 68, icon: '🔄' },
      { name: 'Nginx', level: 60, icon: '🌐' },
    ]
  },
  {
    name: 'AI & Data',
    color: 'neon-purple',
    skills: [
      { name: 'Machine Learning', level: 75, icon: '🤖' },
      { name: 'TensorFlow', level: 70, icon: '🧠' },
      { name: 'OpenAI API', level: 80, icon: '🎭' },
      { name: 'Data Analysis', level: 72, icon: '📊' },
      { name: 'Pandas', level: 78, icon: '🐼' },
      { name: 'NumPy', level: 75, icon: '🔢' },
    ]
  }
]

export default function Skills() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })
  
  const [activeCategory, setActiveCategory] = useState(0)

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  }

  const getColorClasses = (color: string) => {
    const colorMap: { [key: string]: { bg: string, text: string, border: string } } = {
      'neon-blue': { bg: 'bg-neon-blue', text: 'text-neon-blue', border: 'border-neon-blue' },
      'neon-green': { bg: 'bg-neon-green', text: 'text-neon-green', border: 'border-neon-green' },
      'neon-pink': { bg: 'bg-neon-pink', text: 'text-neon-pink', border: 'border-neon-pink' },
      'neon-purple': { bg: 'bg-neon-purple', text: 'text-neon-purple', border: 'border-neon-purple' },
    }
    return colorMap[color] || colorMap['neon-blue']
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" ref={ref}>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        className="text-center mb-16"
      >
        <motion.h2
          variants={itemVariants}
          className="text-4xl md:text-6xl font-cyber font-bold text-neon-blue neon-glow-subtle mb-6"
        >
          Skills & Expertise
        </motion.h2>
        <motion.div
          variants={itemVariants}
          className="w-24 h-1 bg-gradient-to-r from-neon-blue to-neon-green mx-auto mb-8"
        />
        <motion.p
          variants={itemVariants}
          className="text-xl text-gray-400 max-w-3xl mx-auto"
        >
          A comprehensive overview of my technical skills and expertise across different domains.
        </motion.p>
      </motion.div>

      {/* Category Tabs */}
      <motion.div
        variants={itemVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        className="flex flex-wrap justify-center mb-12 gap-4"
      >
        {skillCategories.map((category, index) => {
          const colors = getColorClasses(category.color)
          return (
            <button
              key={category.name}
              onClick={() => setActiveCategory(index)}
              className={`px-6 py-3 rounded-lg font-tech font-semibold transition-all duration-300 ${
                activeCategory === index
                  ? `${colors.bg} text-black shadow-lg`
                  : `bg-gray-900/50 ${colors.text} border ${colors.border} hover:bg-gray-800/50`
              }`}
            >
              {category.name}
            </button>
          )
        })}
      </motion.div>

      {/* Skills Grid */}
      <motion.div
        key={activeCategory}
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid md:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        {skillCategories[activeCategory].skills.map((skill, index) => {
          const colors = getColorClasses(skillCategories[activeCategory].color)
          return (
            <motion.div
              key={skill.name}
              variants={itemVariants}
              className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg p-6 hover:border-opacity-50 transition-all duration-300 group"
              style={{
                borderColor: activeCategory === 0 ? '#00d4ff' : 
                           activeCategory === 1 ? '#14b8a6' :
                           activeCategory === 2 ? '#ff10f0' : '#bf00ff'
              }}
              whileHover={{ scale: 1.05, y: -5 }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{skill.icon}</span>
                  <h3 className="text-lg font-tech font-semibold text-white group-hover:text-opacity-90">
                    {skill.name}
                  </h3>
                </div>
                <span className={`text-sm font-tech font-bold ${colors.text}`}>
                  {skill.level}%
                </span>
              </div>
              
              {/* Circular Progress */}
              <div className="relative w-24 h-24 mx-auto mb-4">
                <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                  {/* Background circle */}
                  <circle
                    cx="50"
                    cy="50"
                    r="40"
                    stroke="currentColor"
                    strokeWidth="8"
                    fill="transparent"
                    className="text-gray-700"
                  />
                  {/* Progress circle */}
                  <motion.circle
                    cx="50"
                    cy="50"
                    r="40"
                    stroke="currentColor"
                    strokeWidth="8"
                    fill="transparent"
                    strokeLinecap="round"
                    className={colors.text}
                    initial={{ pathLength: 0 }}
                    animate={inView ? { pathLength: skill.level / 100 } : { pathLength: 0 }}
                    transition={{ duration: 1.5, delay: index * 0.1 }}
                    style={{
                      strokeDasharray: "251.2",
                      strokeDashoffset: "251.2",
                    }}
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className={`text-lg font-tech font-bold ${colors.text}`}>
                    {skill.level}%
                  </span>
                </div>
              </div>

              {/* Linear Progress Bar */}
              <div className="w-full bg-gray-800 rounded-full h-2 overflow-hidden">
                <motion.div
                  className={`h-full rounded-full ${colors.bg}`}
                  initial={{ width: 0 }}
                  animate={inView ? { width: `${skill.level}%` } : { width: 0 }}
                  transition={{ duration: 1.5, delay: index * 0.1 }}
                />
              </div>
            </motion.div>
          )
        })}
      </motion.div>

      {/* Additional Info */}
      <motion.div
        variants={itemVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        className="mt-16 text-center"
      >
        <div className="bg-gray-900/30 backdrop-blur-sm border border-gray-700 rounded-lg p-8 max-w-4xl mx-auto">
          <h3 className="text-2xl font-tech font-semibold text-white mb-4">
            Continuous Learning
          </h3>
          <p className="text-gray-400 leading-relaxed">
            I believe in continuous learning and staying updated with the latest technologies. 
            These skills represent my current expertise, but I'm always exploring new tools and frameworks 
            to expand my knowledge and deliver better solutions.
          </p>
        </div>
      </motion.div>
    </div>
  )
}
