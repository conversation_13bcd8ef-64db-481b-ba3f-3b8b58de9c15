{"version": 3, "sources": ["../../../src/server/dev/log-app-dir-error.ts"], "names": ["isError", "Log", "logAppDirError", "err", "stack", "cleanedStack", "split", "map", "line", "replace", "filteredStack", "filter", "test", "length", "error", "join", "digest", "console", "JSON", "stringify", "cause"], "mappings": "AAAA,OAAOA,aAAa,qBAAoB;AACxC,YAAYC,SAAS,yBAAwB;AAE7C,OAAO,SAASC,eAAeC,GAAY;IACzC,IAAIH,QAAQG,SAAQA,uBAAAA,IAAKC,KAAK,GAAE;QAC9B,MAAMC,eAAeF,IAAIC,KAAK,CAACE,KAAK,CAAC,MAAMC,GAAG,CAAC,CAACC,OAC9C,iDAAiD;YACjDA,KAAKC,OAAO,CAAC,kDAAkD;QAEjE,MAAMC,gBAAgBL,YACpB,iDAAiD;SAChDM,MAAM,CACL,CAACH,OACC,CAAC,6BAA6BI,IAAI,CAACJ,SACnC,CAAC,oBAAoBI,IAAI,CAACJ,SAC1B,CAAC,qBAAqBI,IAAI,CAACJ;QAEjC,IAAIE,cAAcG,MAAM,KAAK,GAAG;YAC9B,uEAAuE;YACvEZ,IAAIa,KAAK,CAAC,CAAC,gBAAgB,EAAET,aAAaU,IAAI,CAAC,MAAM,CAAC;QACxD,OAAO;YACLd,IAAIa,KAAK,CAACJ,cAAcK,IAAI,CAAC;QAC/B;QACA,IAAI,OAAO,AAACZ,IAAYa,MAAM,KAAK,aAAa;YAC9CC,QAAQH,KAAK,CAAC,CAAC,QAAQ,EAAEI,KAAKC,SAAS,CAAC,AAAChB,IAAYa,MAAM,EAAE,CAAC;QAChE;QAEA,IAAIb,IAAIiB,KAAK,EAAEH,QAAQH,KAAK,CAAC,UAAUX,IAAIiB,KAAK;IAClD,OAAO;QACLnB,IAAIa,KAAK,CAACX;IACZ;AACF"}