'use client'

import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { ExternalLink, Github, Play } from 'lucide-react'
import { useState } from 'react'

const projects = [
  {
    id: 1,
    title: 'AI-Powered Chat Application',
    description: 'A real-time chat application with AI integration, built with React, Node.js, and OpenAI API. Features include smart responses, sentiment analysis, and modern UI.',
    image: '/api/placeholder/600/400',
    tech: ['React', 'Node.js', 'OpenAI', 'Socket.io', 'MongoDB'],
    liveUrl: 'https://example.com',
    githubUrl: 'https://github.com/yourusername/ai-chat',
    featured: true,
  },
  {
    id: 2,
    title: 'E-Commerce Platform',
    description: 'Full-stack e-commerce solution with payment integration, inventory management, and admin dashboard. Built with modern technologies.',
    image: '/api/placeholder/600/400',
    tech: ['Next.js', 'PostgreSQL', 'Stripe', 'Tailwind CSS'],
    liveUrl: 'https://example.com',
    githubUrl: 'https://github.com/yourusername/ecommerce',
    featured: true,
  },
  {
    id: 3,
    title: 'Machine Learning Dashboard',
    description: 'Interactive dashboard for visualizing ML model performance with real-time data processing and beautiful charts.',
    image: '/api/placeholder/600/400',
    tech: ['Python', 'FastAPI', 'React', 'D3.js', 'TensorFlow'],
    liveUrl: 'https://example.com',
    githubUrl: 'https://github.com/yourusername/ml-dashboard',
    featured: false,
  },
  {
    id: 4,
    title: 'Blockchain Voting System',
    description: 'Secure and transparent voting system built on blockchain technology with smart contracts and modern web interface.',
    image: '/api/placeholder/600/400',
    tech: ['Solidity', 'Web3.js', 'React', 'Ethereum', 'IPFS'],
    liveUrl: 'https://example.com',
    githubUrl: 'https://github.com/yourusername/blockchain-voting',
    featured: false,
  },
  {
    id: 5,
    title: 'IoT Home Automation',
    description: 'Smart home automation system with mobile app control, sensor integration, and AI-powered energy optimization.',
    image: '/api/placeholder/600/400',
    tech: ['React Native', 'Python', 'Raspberry Pi', 'MQTT', 'TensorFlow'],
    liveUrl: 'https://example.com',
    githubUrl: 'https://github.com/yourusername/iot-home',
    featured: false,
  },
  {
    id: 6,
    title: 'Social Media Analytics',
    description: 'Comprehensive social media analytics platform with sentiment analysis, trend detection, and automated reporting.',
    image: '/api/placeholder/600/400',
    tech: ['Django', 'React', 'PostgreSQL', 'Celery', 'NLP'],
    liveUrl: 'https://example.com',
    githubUrl: 'https://github.com/yourusername/social-analytics',
    featured: false,
  },
]

const techColors: { [key: string]: string } = {
  'React': 'bg-blue-500',
  'Node.js': 'bg-green-500',
  'Python': 'bg-yellow-500',
  'Next.js': 'bg-gray-800',
  'FastAPI': 'bg-teal-500',
  'Django': 'bg-green-700',
  'PostgreSQL': 'bg-blue-700',
  'MongoDB': 'bg-green-600',
  'TensorFlow': 'bg-orange-500',
  'OpenAI': 'bg-purple-500',
  'Solidity': 'bg-gray-600',
  'Web3.js': 'bg-orange-600',
}

export default function Projects() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })
  
  const [filter, setFilter] = useState('all')
  const [hoveredProject, setHoveredProject] = useState<number | null>(null)

  const filteredProjects = filter === 'all' 
    ? projects 
    : filter === 'featured' 
    ? projects.filter(p => p.featured)
    : projects.filter(p => !p.featured)

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" ref={ref}>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        className="text-center mb-16"
      >
        <motion.h2
          variants={itemVariants}
          className="text-4xl md:text-6xl font-cyber font-bold text-neon-blue neon-glow-subtle mb-6"
        >
          My Projects
        </motion.h2>
        <motion.div
          variants={itemVariants}
          className="w-24 h-1 bg-gradient-to-r from-neon-blue to-neon-green mx-auto mb-8"
        />
        <motion.p
          variants={itemVariants}
          className="text-xl text-gray-400 max-w-3xl mx-auto"
        >
          Here are some of my recent projects that showcase my skills and passion for creating innovative solutions.
        </motion.p>
      </motion.div>

      {/* Filter Buttons */}
      <motion.div
        variants={itemVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        className="flex justify-center mb-12"
      >
        <div className="flex space-x-4 bg-gray-900/50 backdrop-blur-sm rounded-lg p-2 border border-gray-700">
          {['all', 'featured', 'other'].map((filterType) => (
            <button
              key={filterType}
              onClick={() => setFilter(filterType)}
              className={`px-6 py-2 rounded-md font-tech font-medium transition-all duration-300 ${
                filter === filterType
                  ? 'bg-neon-blue text-black'
                  : 'text-gray-400 hover:text-neon-blue'
              }`}
            >
              {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
            </button>
          ))}
        </div>
      </motion.div>

      {/* Projects Grid */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
      >
        {filteredProjects.map((project) => (
          <motion.div
            key={project.id}
            variants={itemVariants}
            className="group relative bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg overflow-hidden hover:border-neon-blue/50 transition-all duration-300"
            onMouseEnter={() => setHoveredProject(project.id)}
            onMouseLeave={() => setHoveredProject(null)}
            whileHover={{ y: -10 }}
          >
            {/* Project Image */}
            <div className="relative h-48 bg-gradient-to-br from-neon-blue/20 to-neon-green/20 overflow-hidden">
              <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                <Play className="text-neon-blue" size={48} />
              </div>
              {project.featured && (
                <div className="absolute top-4 left-4 bg-neon-blue text-black px-3 py-1 rounded-full text-sm font-tech font-semibold">
                  Featured
                </div>
              )}
              
              {/* Hover overlay */}
              <motion.div
                className="absolute inset-0 bg-neon-blue/20 flex items-center justify-center space-x-4"
                initial={{ opacity: 0 }}
                animate={{ opacity: hoveredProject === project.id ? 1 : 0 }}
                transition={{ duration: 0.3 }}
              >
                <motion.a
                  href={project.liveUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-3 bg-neon-blue text-black rounded-full hover:bg-white transition-colors"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <ExternalLink size={20} />
                </motion.a>
                <motion.a
                  href={project.githubUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-3 bg-gray-800 text-white rounded-full hover:bg-gray-700 transition-colors"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <Github size={20} />
                </motion.a>
              </motion.div>
            </div>

            {/* Project Content */}
            <div className="p-6">
              <h3 className="text-xl font-tech font-semibold text-white mb-3 group-hover:text-neon-blue transition-colors">
                {project.title}
              </h3>
              <p className="text-gray-400 mb-4 line-clamp-3">
                {project.description}
              </p>
              
              {/* Tech Stack */}
              <div className="flex flex-wrap gap-2 mb-4">
                {project.tech.map((tech) => (
                  <span
                    key={tech}
                    className={`px-3 py-1 text-xs font-tech font-medium rounded-full text-white ${
                      techColors[tech] || 'bg-gray-600'
                    }`}
                  >
                    {tech}
                  </span>
                ))}
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <a
                  href={project.liveUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex-1 bg-neon-blue text-black py-2 px-4 rounded-md font-tech font-medium text-center hover:bg-white transition-colors"
                >
                  Live Demo
                </a>
                <a
                  href={project.githubUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex-1 border border-gray-600 text-gray-300 py-2 px-4 rounded-md font-tech font-medium text-center hover:border-neon-blue hover:text-neon-blue transition-colors"
                >
                  Source Code
                </a>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>
    </div>
  )
}
