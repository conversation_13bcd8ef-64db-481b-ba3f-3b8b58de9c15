"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tsparticles-interaction-external-slow";
exports.ids = ["vendor-chunks/tsparticles-interaction-external-slow"];
exports.modules = {

/***/ "(ssr)/./node_modules/tsparticles-interaction-external-slow/esm/Options/Classes/Slow.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-external-slow/esm/Options/Classes/Slow.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Slow: () => (/* binding */ Slow)\n/* harmony export */ });\nclass Slow {\n    constructor() {\n        this.factor = 3;\n        this.radius = 200;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.factor !== undefined) {\n            this.factor = data.factor;\n        }\n        if (data.radius !== undefined) {\n            this.radius = data.radius;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tZXh0ZXJuYWwtc2xvdy9lc20vT3B0aW9ucy9DbGFzc2VzL1Nsb3cuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mdXR1cmlzdGljLXBvcnRmb2xpby8uL25vZGVfbW9kdWxlcy90c3BhcnRpY2xlcy1pbnRlcmFjdGlvbi1leHRlcm5hbC1zbG93L2VzbS9PcHRpb25zL0NsYXNzZXMvU2xvdy5qcz9kYjBmIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBTbG93IHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgdGhpcy5mYWN0b3IgPSAzO1xuICAgICAgICB0aGlzLnJhZGl1cyA9IDIwMDtcbiAgICB9XG4gICAgbG9hZChkYXRhKSB7XG4gICAgICAgIGlmICghZGF0YSkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGlmIChkYXRhLmZhY3RvciAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aGlzLmZhY3RvciA9IGRhdGEuZmFjdG9yO1xuICAgICAgICB9XG4gICAgICAgIGlmIChkYXRhLnJhZGl1cyAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aGlzLnJhZGl1cyA9IGRhdGEucmFkaXVzO1xuICAgICAgICB9XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-external-slow/esm/Options/Classes/Slow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-external-slow/esm/Slower.js":
/*!**************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-external-slow/esm/Slower.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Slower: () => (/* binding */ Slower)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/ExternalInteractorBase.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/Utils.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n/* harmony import */ var _Options_Classes_Slow__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Options/Classes/Slow */ \"(ssr)/./node_modules/tsparticles-interaction-external-slow/esm/Options/Classes/Slow.js\");\n\n\nclass Slower extends tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    clear(particle, delta, force) {\n        if (particle.slow.inRange && !force) {\n            return;\n        }\n        particle.slow.factor = 1;\n    }\n    init() {\n        const container = this.container, slow = container.actualOptions.interactivity.modes.slow;\n        if (!slow) {\n            return;\n        }\n        container.retina.slowModeRadius = slow.radius * container.retina.pixelRatio;\n    }\n    async interact() {\n    }\n    isEnabled(particle) {\n        const container = this.container, mouse = container.interactivity.mouse, events = (particle?.interactivity ?? container.actualOptions.interactivity).events;\n        return events.onHover.enable && !!mouse.position && (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.isInArray)(\"slow\", events.onHover.mode);\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.slow) {\n            options.slow = new _Options_Classes_Slow__WEBPACK_IMPORTED_MODULE_2__.Slow();\n        }\n        for (const source of sources) {\n            options.slow.load(source?.slow);\n        }\n    }\n    reset(particle) {\n        particle.slow.inRange = false;\n        const container = this.container, options = container.actualOptions, mousePos = container.interactivity.mouse.position, radius = container.retina.slowModeRadius, slowOptions = options.interactivity.modes.slow;\n        if (!slowOptions || !radius || radius < 0 || !mousePos) {\n            return;\n        }\n        const particlePos = particle.getPosition(), dist = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_3__.getDistance)(mousePos, particlePos), proximityFactor = dist / radius, slowFactor = slowOptions.factor, { slow } = particle;\n        if (dist > radius) {\n            return;\n        }\n        slow.inRange = true;\n        slow.factor = proximityFactor / slowFactor;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-external-slow/esm/Slower.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-external-slow/esm/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-external-slow/esm/index.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Slow: () => (/* reexport safe */ _Options_Classes_Slow__WEBPACK_IMPORTED_MODULE_1__.Slow),\n/* harmony export */   loadExternalSlowInteraction: () => (/* binding */ loadExternalSlowInteraction)\n/* harmony export */ });\n/* harmony import */ var _Slower__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Slower */ \"(ssr)/./node_modules/tsparticles-interaction-external-slow/esm/Slower.js\");\n/* harmony import */ var _Options_Classes_Slow__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Options/Classes/Slow */ \"(ssr)/./node_modules/tsparticles-interaction-external-slow/esm/Options/Classes/Slow.js\");\n\nasync function loadExternalSlowInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalSlow\", (container) => new _Slower__WEBPACK_IMPORTED_MODULE_0__.Slower(container), refresh);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tZXh0ZXJuYWwtc2xvdy9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFrQztBQUMzQjtBQUNQLGtFQUFrRSwyQ0FBTTtBQUN4RTtBQUN1QztBQUNJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnV0dXJpc3RpYy1wb3J0Zm9saW8vLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tZXh0ZXJuYWwtc2xvdy9lc20vaW5kZXguanM/ODNmMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTbG93ZXIgfSBmcm9tIFwiLi9TbG93ZXJcIjtcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBsb2FkRXh0ZXJuYWxTbG93SW50ZXJhY3Rpb24oZW5naW5lLCByZWZyZXNoID0gdHJ1ZSkge1xuICAgIGF3YWl0IGVuZ2luZS5hZGRJbnRlcmFjdG9yKFwiZXh0ZXJuYWxTbG93XCIsIChjb250YWluZXIpID0+IG5ldyBTbG93ZXIoY29udGFpbmVyKSwgcmVmcmVzaCk7XG59XG5leHBvcnQgKiBmcm9tIFwiLi9PcHRpb25zL0NsYXNzZXMvU2xvd1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vT3B0aW9ucy9JbnRlcmZhY2VzL0lTbG93XCI7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-external-slow/esm/index.js\n");

/***/ })

};
;