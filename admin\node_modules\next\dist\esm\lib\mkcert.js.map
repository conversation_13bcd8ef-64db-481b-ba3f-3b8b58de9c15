{"version": 3, "sources": ["../../src/lib/mkcert.ts"], "names": ["fs", "path", "getCacheDirectory", "Log", "execSync", "WritableStream", "require", "MKCERT_VERSION", "getBinaryName", "platform", "process", "arch", "Error", "downloadBinary", "binaryName", "cacheDirectory", "binaryPath", "join", "existsSync", "downloadUrl", "promises", "mkdir", "recursive", "info", "response", "fetch", "ok", "body", "status", "binaryWriteStream", "createWriteStream", "pipeTo", "write", "chunk", "Promise", "resolve", "reject", "error", "close", "chmod", "err", "createSelfSignedCertificate", "host", "certDir", "resolvedCertDir", "cwd", "keyP<PERSON>", "certPath", "defaultHosts", "hosts", "includes", "stdio", "caLocation", "toString", "trim", "gitignore<PERSON>ath", "gitignore", "readFile", "appendFile", "key", "cert", "rootCA"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,SAASC,iBAAiB,QAAQ,gCAA+B;AACjE,YAAYC,SAAS,sBAAqB;AAC1C,SAASC,QAAQ,QAAQ,gBAAe;AACxC,MAAM,EAAEC,cAAc,EAAE,GAAGC,QAAQ;AAInC,MAAMC,iBAAiB;AAQvB,SAASC;IACP,MAAMC,WAAWC,QAAQD,QAAQ;IACjC,MAAME,OAAOD,QAAQC,IAAI,KAAK,QAAQ,UAAUD,QAAQC,IAAI;IAE5D,IAAIF,aAAa,SAAS;QACxB,OAAO,CAAC,OAAO,EAAEF,eAAe,SAAS,EAAEI,KAAK,IAAI,CAAC;IACvD;IACA,IAAIF,aAAa,UAAU;QACzB,OAAO,CAAC,OAAO,EAAEF,eAAe,QAAQ,EAAEI,KAAK,CAAC;IAClD;IACA,IAAIF,aAAa,SAAS;QACxB,OAAO,CAAC,OAAO,EAAEF,eAAe,OAAO,EAAEI,KAAK,CAAC;IACjD;IAEA,MAAM,IAAIC,MAAM,CAAC,sBAAsB,EAAEH,SAAS,CAAC;AACrD;AAEA,eAAeI;IACb,IAAI;QACF,MAAMC,aAAaN;QACnB,MAAMO,iBAAiBb,kBAAkB;QACzC,MAAMc,aAAaf,KAAKgB,IAAI,CAACF,gBAAgBD;QAE7C,IAAId,GAAGkB,UAAU,CAACF,aAAa;YAC7B,OAAOA;QACT;QAEA,MAAMG,cAAc,CAAC,wDAAwD,EAAEZ,eAAe,CAAC,EAAEO,WAAW,CAAC;QAE7G,MAAMd,GAAGoB,QAAQ,CAACC,KAAK,CAACN,gBAAgB;YAAEO,WAAW;QAAK;QAE1DnB,IAAIoB,IAAI,CAAC,CAAC,6BAA6B,CAAC;QAExC,MAAMC,WAAW,MAAMC,MAAMN;QAE7B,IAAI,CAACK,SAASE,EAAE,IAAI,CAACF,SAASG,IAAI,EAAE;YAClC,MAAM,IAAIf,MAAM,CAAC,2BAA2B,EAAEY,SAASI,MAAM,CAAC,CAAC;QACjE;QAEAzB,IAAIoB,IAAI,CAAC,CAAC,iDAAiD,CAAC;QAE5D,MAAMM,oBAAoB7B,GAAG8B,iBAAiB,CAACd;QAE/C,MAAMQ,SAASG,IAAI,CAACI,MAAM,CACxB,IAAI1B,eAAe;YACjB2B,OAAMC,KAAK;gBACT,OAAO,IAAIC,QAAQ,CAACC,SAASC;oBAC3BP,kBAAkBG,KAAK,CAACC,OAAO,CAACI;wBAC9B,IAAIA,OAAO;4BACTD,OAAOC;4BACP;wBACF;wBAEAF;oBACF;gBACF;YACF;YACAG;gBACE,OAAO,IAAIJ,QAAQ,CAACC,SAASC;oBAC3BP,kBAAkBS,KAAK,CAAC,CAACD;wBACvB,IAAIA,OAAO;4BACTD,OAAOC;4BACP;wBACF;wBAEAF;oBACF;gBACF;YACF;QACF;QAGF,MAAMnC,GAAGoB,QAAQ,CAACmB,KAAK,CAACvB,YAAY;QAEpC,OAAOA;IACT,EAAE,OAAOwB,KAAK;QACZrC,IAAIkC,KAAK,CAAC,6BAA6BG;IACzC;AACF;AAEA,OAAO,eAAeC,4BACpBC,IAAa,EACbC,UAAkB,cAAc;IAEhC,IAAI;QACF,MAAM3B,aAAa,MAAMH;QACzB,IAAI,CAACG,YAAY,MAAM,IAAIJ,MAAM;QAEjC,MAAMgC,kBAAkB3C,KAAKkC,OAAO,CAACzB,QAAQmC,GAAG,IAAI,CAAC,EAAE,EAAEF,QAAQ,CAAC;QAElE,MAAM3C,GAAGoB,QAAQ,CAACC,KAAK,CAACuB,iBAAiB;YACvCtB,WAAW;QACb;QAEA,MAAMwB,UAAU7C,KAAKkC,OAAO,CAACS,iBAAiB;QAC9C,MAAMG,WAAW9C,KAAKkC,OAAO,CAACS,iBAAiB;QAE/CzC,IAAIoB,IAAI,CACN;QAGF,MAAMyB,eAAe;YAAC;YAAa;YAAa;SAAM;QAEtD,MAAMC,QACJP,QAAQ,CAACM,aAAaE,QAAQ,CAACR,QAC3B;eAAIM;YAAcN;SAAK,GACvBM;QAEN5C,SACE,CAAC,CAAC,EAAEY,WAAW,sBAAsB,EAAE8B,QAAQ,cAAc,EAAEC,SAAS,EAAE,EAAEE,MAAMhC,IAAI,CACpF,KACA,CAAC,EACH;YAAEkC,OAAO;QAAS;QAGpB,MAAMC,aAAahD,SAAS,CAAC,CAAC,EAAEY,WAAW,SAAS,CAAC,EAAEqC,QAAQ,GAAGC,IAAI;QAEtE,IAAI,CAACtD,GAAGkB,UAAU,CAAC4B,YAAY,CAAC9C,GAAGkB,UAAU,CAAC6B,WAAW;YACvD,MAAM,IAAInC,MAAM;QAClB;QAEAT,IAAIoB,IAAI,CAAC,CAAC,+BAA+B,EAAE6B,WAAW,CAAC;QACvDjD,IAAIoB,IAAI,CAAC,CAAC,wBAAwB,EAAEqB,gBAAgB,CAAC;QAErD,MAAMW,gBAAgBtD,KAAKkC,OAAO,CAACzB,QAAQmC,GAAG,IAAI;QAElD,IAAI7C,GAAGkB,UAAU,CAACqC,gBAAgB;YAChC,MAAMC,YAAY,MAAMxD,GAAGoB,QAAQ,CAACqC,QAAQ,CAACF,eAAe;YAC5D,IAAI,CAACC,UAAUN,QAAQ,CAACP,UAAU;gBAChCxC,IAAIoB,IAAI,CAAC;gBAET,MAAMvB,GAAGoB,QAAQ,CAACsC,UAAU,CAACH,eAAe,CAAC,EAAE,EAAEZ,QAAQ,CAAC;YAC5D;QACF;QAEA,OAAO;YACLgB,KAAKb;YACLc,MAAMb;YACNc,QAAQ,CAAC,EAAET,WAAW,WAAW,CAAC;QACpC;IACF,EAAE,OAAOZ,KAAK;QACZrC,IAAIkC,KAAK,CACP,qEACAG;IAEJ;AACF"}