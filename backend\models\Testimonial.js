const mongoose = require('mongoose');

const testimonialSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  role: {
    type: String,
    required: true,
    trim: true
  },
  company: {
    type: String,
    required: true,
    trim: true
  },
  image: {
    type: String,
    default: ''
  },
  rating: {
    type: Number,
    required: true,
    min: 1,
    max: 5,
    default: 5
  },
  text: {
    type: String,
    required: true,
    maxlength: 1000
  },
  email: {
    type: String,
    trim: true,
    lowercase: true
  },
  linkedin: {
    type: String,
    default: ''
  },
  projectRelated: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Project'
  },
  isPublished: {
    type: Boolean,
    default: true
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  order: {
    type: Number,
    default: 0
  },
  dateGiven: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Index for better query performance
testimonialSchema.index({ isPublished: 1, isFeatured: -1, order: 1 });
testimonialSchema.index({ rating: -1, createdAt: -1 });

// Virtual for testimonial excerpt
testimonialSchema.virtual('excerpt').get(function() {
  if (this.text.length <= 100) return this.text;
  return this.text.substring(0, 100) + '...';
});

// Method to toggle featured status
testimonialSchema.methods.toggleFeatured = function() {
  this.isFeatured = !this.isFeatured;
  return this.save();
};

// Static method to get featured testimonials
testimonialSchema.statics.getFeatured = function() {
  return this.find({ 
    isPublished: true, 
    isFeatured: true 
  }).sort({ order: 1, createdAt: -1 });
};

// Static method to get testimonials by rating
testimonialSchema.statics.getByRating = function(minRating = 4) {
  return this.find({ 
    isPublished: true, 
    rating: { $gte: minRating } 
  }).sort({ rating: -1, createdAt: -1 });
};

module.exports = mongoose.model('Testimonial', testimonialSchema);
