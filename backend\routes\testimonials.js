const express = require('express');
const { body, validationResult } = require('express-validator');
const Testimonial = require('../models/Testimonial');
const { auth, adminAuth, logAdminAction } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/testimonials
// @desc    Get all published testimonials
// @access  Public
router.get('/', async (req, res) => {
  try {
    const { featured, limit } = req.query;
    
    let query = { isPublished: true };
    if (featured === 'true') query.isFeatured = true;
    
    let testimonialsQuery = Testimonial.find(query)
      .sort({ isFeatured: -1, order: 1, createdAt: -1 });
    
    if (limit) testimonialsQuery = testimonialsQuery.limit(parseInt(limit));
    
    const testimonials = await testimonialsQuery.populate('projectRelated', 'title');
    
    res.json({
      success: true,
      count: testimonials.length,
      data: testimonials
    });
  } catch (error) {
    console.error('Get testimonials error:', error);
    res.status(500).json({ message: 'Server error fetching testimonials' });
  }
});

// Admin routes below require authentication

// @route   GET /api/testimonials/admin/all
// @desc    Get all testimonials for admin
// @access  Private/Admin
router.get('/admin/all', auth, adminAuth, async (req, res) => {
  try {
    const testimonials = await Testimonial.find()
      .sort({ createdAt: -1 })
      .populate('projectRelated', 'title');
    
    res.json({
      success: true,
      count: testimonials.length,
      data: testimonials
    });
  } catch (error) {
    console.error('Get all testimonials error:', error);
    res.status(500).json({ message: 'Server error fetching testimonials' });
  }
});

// @route   POST /api/testimonials
// @desc    Create new testimonial
// @access  Private/Admin
router.post('/',
  auth,
  adminAuth,
  logAdminAction('CREATE_TESTIMONIAL'),
  [
    body('name').notEmpty().trim(),
    body('role').notEmpty().trim(),
    body('company').notEmpty().trim(),
    body('text').notEmpty().isLength({ max: 1000 }),
    body('rating').isInt({ min: 1, max: 5 })
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ 
          message: 'Invalid input',
          errors: errors.array() 
        });
      }

      const testimonial = new Testimonial(req.body);
      await testimonial.save();

      res.status(201).json({
        success: true,
        message: 'Testimonial created successfully',
        data: testimonial
      });
    } catch (error) {
      console.error('Create testimonial error:', error);
      res.status(500).json({ message: 'Server error creating testimonial' });
    }
  }
);

// @route   PUT /api/testimonials/:id
// @desc    Update testimonial
// @access  Private/Admin
router.put('/:id',
  auth,
  adminAuth,
  logAdminAction('UPDATE_TESTIMONIAL'),
  async (req, res) => {
    try {
      const testimonial = await Testimonial.findByIdAndUpdate(
        req.params.id,
        req.body,
        { new: true, runValidators: true }
      );

      if (!testimonial) {
        return res.status(404).json({ message: 'Testimonial not found' });
      }

      res.json({
        success: true,
        message: 'Testimonial updated successfully',
        data: testimonial
      });
    } catch (error) {
      console.error('Update testimonial error:', error);
      res.status(500).json({ message: 'Server error updating testimonial' });
    }
  }
);

// @route   DELETE /api/testimonials/:id
// @desc    Delete testimonial
// @access  Private/Admin
router.delete('/:id',
  auth,
  adminAuth,
  logAdminAction('DELETE_TESTIMONIAL'),
  async (req, res) => {
    try {
      const testimonial = await Testimonial.findByIdAndDelete(req.params.id);

      if (!testimonial) {
        return res.status(404).json({ message: 'Testimonial not found' });
      }

      res.json({
        success: true,
        message: 'Testimonial deleted successfully'
      });
    } catch (error) {
      console.error('Delete testimonial error:', error);
      res.status(500).json({ message: 'Server error deleting testimonial' });
    }
  }
);

// @route   PATCH /api/testimonials/:id/toggle-featured
// @desc    Toggle testimonial featured status
// @access  Private/Admin
router.patch('/:id/toggle-featured',
  auth,
  adminAuth,
  logAdminAction('TOGGLE_TESTIMONIAL_FEATURED'),
  async (req, res) => {
    try {
      const testimonial = await Testimonial.findById(req.params.id);

      if (!testimonial) {
        return res.status(404).json({ message: 'Testimonial not found' });
      }

      await testimonial.toggleFeatured();

      res.json({
        success: true,
        message: `Testimonial ${testimonial.isFeatured ? 'featured' : 'unfeatured'} successfully`,
        data: testimonial
      });
    } catch (error) {
      console.error('Toggle featured error:', error);
      res.status(500).json({ message: 'Server error toggling featured status' });
    }
  }
);

// @route   PATCH /api/testimonials/:id/toggle-published
// @desc    Toggle testimonial published status
// @access  Private/Admin
router.patch('/:id/toggle-published',
  auth,
  adminAuth,
  logAdminAction('TOGGLE_TESTIMONIAL_PUBLISHED'),
  async (req, res) => {
    try {
      const testimonial = await Testimonial.findById(req.params.id);

      if (!testimonial) {
        return res.status(404).json({ message: 'Testimonial not found' });
      }

      testimonial.isPublished = !testimonial.isPublished;
      await testimonial.save();

      res.json({
        success: true,
        message: `Testimonial ${testimonial.isPublished ? 'published' : 'unpublished'} successfully`,
        data: testimonial
      });
    } catch (error) {
      console.error('Toggle published error:', error);
      res.status(500).json({ message: 'Server error toggling published status' });
    }
  }
);

module.exports = router;
