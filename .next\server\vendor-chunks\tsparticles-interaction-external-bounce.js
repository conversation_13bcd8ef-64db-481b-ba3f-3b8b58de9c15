"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tsparticles-interaction-external-bounce";
exports.ids = ["vendor-chunks/tsparticles-interaction-external-bounce"];
exports.modules = {

/***/ "(ssr)/./node_modules/tsparticles-interaction-external-bounce/esm/Bouncer.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-external-bounce/esm/Bouncer.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bouncer: () => (/* binding */ Bouncer)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/ExternalInteractorBase.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/Circle.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/Utils.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/Vector.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/Rectangle.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/Constants.js\");\n/* harmony import */ var _Options_Classes_Bounce__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Options/Classes/Bounce */ \"(ssr)/./node_modules/tsparticles-interaction-external-bounce/esm/Options/Classes/Bounce.js\");\n\n\nclass Bouncer extends tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n        this._processBounce = (position, radius, area) => {\n            const query = this.container.particles.quadTree.query(area, (p) => this.isEnabled(p));\n            for (const particle of query) {\n                if (area instanceof tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.Circle) {\n                    (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.circleBounce)((0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.circleBounceDataFromParticle)(particle), {\n                        position,\n                        radius,\n                        mass: (radius ** 2 * Math.PI) / 2,\n                        velocity: tsparticles_engine__WEBPACK_IMPORTED_MODULE_3__.Vector.origin,\n                        factor: tsparticles_engine__WEBPACK_IMPORTED_MODULE_3__.Vector.origin,\n                    });\n                }\n                else if (area instanceof tsparticles_engine__WEBPACK_IMPORTED_MODULE_4__.Rectangle) {\n                    (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.rectBounce)(particle, (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.calculateBounds)(position, radius));\n                }\n            }\n        };\n        this._processMouseBounce = () => {\n            const container = this.container, pxRatio = container.retina.pixelRatio, tolerance = 10 * pxRatio, mousePos = container.interactivity.mouse.position, radius = container.retina.bounceModeDistance;\n            if (!radius || radius < 0 || !mousePos) {\n                return;\n            }\n            this._processBounce(mousePos, radius, new tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.Circle(mousePos.x, mousePos.y, radius + tolerance));\n        };\n        this._singleSelectorBounce = (selector, div) => {\n            const container = this.container, query = document.querySelectorAll(selector);\n            if (!query.length) {\n                return;\n            }\n            query.forEach((item) => {\n                const elem = item, pxRatio = container.retina.pixelRatio, pos = {\n                    x: (elem.offsetLeft + elem.offsetWidth / 2) * pxRatio,\n                    y: (elem.offsetTop + elem.offsetHeight / 2) * pxRatio,\n                }, radius = (elem.offsetWidth / 2) * pxRatio, tolerance = 10 * pxRatio, area = div.type === \"circle\"\n                    ? new tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.Circle(pos.x, pos.y, radius + tolerance)\n                    : new tsparticles_engine__WEBPACK_IMPORTED_MODULE_4__.Rectangle(elem.offsetLeft * pxRatio - tolerance, elem.offsetTop * pxRatio - tolerance, elem.offsetWidth * pxRatio + tolerance * 2, elem.offsetHeight * pxRatio + tolerance * 2);\n                this._processBounce(pos, radius, area);\n            });\n        };\n    }\n    clear() {\n    }\n    init() {\n        const container = this.container, bounce = container.actualOptions.interactivity.modes.bounce;\n        if (!bounce) {\n            return;\n        }\n        container.retina.bounceModeDistance = bounce.distance * container.retina.pixelRatio;\n    }\n    async interact() {\n        const container = this.container, options = container.actualOptions, events = options.interactivity.events, mouseMoveStatus = container.interactivity.status === tsparticles_engine__WEBPACK_IMPORTED_MODULE_5__.mouseMoveEvent, hoverEnabled = events.onHover.enable, hoverMode = events.onHover.mode, divs = events.onDiv;\n        if (mouseMoveStatus && hoverEnabled && (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.isInArray)(\"bounce\", hoverMode)) {\n            this._processMouseBounce();\n        }\n        else {\n            (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.divModeExecute)(\"bounce\", divs, (selector, div) => this._singleSelectorBounce(selector, div));\n        }\n    }\n    isEnabled(particle) {\n        const container = this.container, options = container.actualOptions, mouse = container.interactivity.mouse, events = (particle?.interactivity ?? options.interactivity).events, divs = events.onDiv;\n        return ((mouse.position && events.onHover.enable && (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.isInArray)(\"bounce\", events.onHover.mode)) ||\n            (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_2__.isDivModeEnabled)(\"bounce\", divs));\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.bounce) {\n            options.bounce = new _Options_Classes_Bounce__WEBPACK_IMPORTED_MODULE_6__.Bounce();\n        }\n        for (const source of sources) {\n            options.bounce.load(source?.bounce);\n        }\n    }\n    reset() {\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-external-bounce/esm/Bouncer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-external-bounce/esm/Options/Classes/Bounce.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-external-bounce/esm/Options/Classes/Bounce.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bounce: () => (/* binding */ Bounce)\n/* harmony export */ });\nclass Bounce {\n    constructor() {\n        this.distance = 200;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tZXh0ZXJuYWwtYm91bmNlL2VzbS9PcHRpb25zL0NsYXNzZXMvQm91bmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1dHVyaXN0aWMtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL3RzcGFydGljbGVzLWludGVyYWN0aW9uLWV4dGVybmFsLWJvdW5jZS9lc20vT3B0aW9ucy9DbGFzc2VzL0JvdW5jZS5qcz84YTFiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBCb3VuY2Uge1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICB0aGlzLmRpc3RhbmNlID0gMjAwO1xuICAgIH1cbiAgICBsb2FkKGRhdGEpIHtcbiAgICAgICAgaWYgKCFkYXRhKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGRhdGEuZGlzdGFuY2UgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5kaXN0YW5jZSA9IGRhdGEuZGlzdGFuY2U7XG4gICAgICAgIH1cbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-external-bounce/esm/Options/Classes/Bounce.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-external-bounce/esm/index.js":
/*!***************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-external-bounce/esm/index.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bounce: () => (/* reexport safe */ _Options_Classes_Bounce__WEBPACK_IMPORTED_MODULE_1__.Bounce),\n/* harmony export */   loadExternalBounceInteraction: () => (/* binding */ loadExternalBounceInteraction)\n/* harmony export */ });\n/* harmony import */ var _Bouncer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Bouncer */ \"(ssr)/./node_modules/tsparticles-interaction-external-bounce/esm/Bouncer.js\");\n/* harmony import */ var _Options_Classes_Bounce__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Options/Classes/Bounce */ \"(ssr)/./node_modules/tsparticles-interaction-external-bounce/esm/Options/Classes/Bounce.js\");\n\nasync function loadExternalBounceInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalBounce\", (container) => new _Bouncer__WEBPACK_IMPORTED_MODULE_0__.Bouncer(container), refresh);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tZXh0ZXJuYWwtYm91bmNlL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQW9DO0FBQzdCO0FBQ1Asb0VBQW9FLDZDQUFPO0FBQzNFO0FBQ3lDO0FBQ0kiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mdXR1cmlzdGljLXBvcnRmb2xpby8uL25vZGVfbW9kdWxlcy90c3BhcnRpY2xlcy1pbnRlcmFjdGlvbi1leHRlcm5hbC1ib3VuY2UvZXNtL2luZGV4LmpzPzc1N2MiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQm91bmNlciB9IGZyb20gXCIuL0JvdW5jZXJcIjtcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBsb2FkRXh0ZXJuYWxCb3VuY2VJbnRlcmFjdGlvbihlbmdpbmUsIHJlZnJlc2ggPSB0cnVlKSB7XG4gICAgYXdhaXQgZW5naW5lLmFkZEludGVyYWN0b3IoXCJleHRlcm5hbEJvdW5jZVwiLCAoY29udGFpbmVyKSA9PiBuZXcgQm91bmNlcihjb250YWluZXIpLCByZWZyZXNoKTtcbn1cbmV4cG9ydCAqIGZyb20gXCIuL09wdGlvbnMvQ2xhc3Nlcy9Cb3VuY2VcIjtcbmV4cG9ydCAqIGZyb20gXCIuL09wdGlvbnMvSW50ZXJmYWNlcy9JQm91bmNlXCI7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-external-bounce/esm/index.js\n");

/***/ })

};
;