'use client'

import { motion } from 'framer-motion'
import { Github, Linkedin, Twitter, Mail, Phone, MapPin, Heart, Code, Coffee } from 'lucide-react'

const socialLinks = [
  {
    name: 'GitHub',
    icon: Github,
    url: 'https://github.com/yourusername',
    color: 'hover:text-white'
  },
  {
    name: 'LinkedIn',
    icon: Linkedin,
    url: 'https://linkedin.com/in/yourusername',
    color: 'hover:text-blue-400'
  },
  {
    name: 'Twitter',
    icon: Twitter,
    url: 'https://twitter.com/yourusername',
    color: 'hover:text-blue-400'
  },
  {
    name: 'Email',
    icon: Mail,
    url: 'mailto:<EMAIL>',
    color: 'hover:text-neon-blue'
  },
]

const quickLinks = [
  { name: 'Home', href: '#home' },
  { name: 'About', href: '#about' },
  { name: 'Projects', href: '#projects' },
  { name: 'Skills', href: '#skills' },
  { name: 'Resume', href: '#resume' },
  { name: 'Blog', href: '#blog' },
  { name: 'Contact', href: '#contact' },
]

const services = [
  'Web Development',
  'AI Integration',
  'Full-Stack Solutions',
  'Performance Optimization',
  'Technical Consulting',
  'Code Review',
]

export default function Footer() {
  const scrollToSection = (href: string) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  const currentYear = new Date().getFullYear()

  return (
    <footer className="relative bg-black border-t border-gray-800 mt-20">
      {/* Background Effects */}
      <div className="absolute inset-0 cyber-grid opacity-5"></div>
      <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent"></div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-12">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h3 className="text-2xl font-cyber font-bold text-neon-blue neon-glow-subtle mb-4">
                {'<YourName />'}
              </h3>
              <p className="text-gray-400 mb-6 leading-relaxed">
                Passionate self-taught developer building the future with AI, 
                web technologies, and creative coding solutions.
              </p>
              
              {/* Contact Info */}
              <div className="space-y-3 text-sm">
                <div className="flex items-center text-gray-400">
                  <MapPin size={16} className="mr-3 text-neon-blue" />
                  San Francisco, CA
                </div>
                <div className="flex items-center text-gray-400">
                  <Phone size={16} className="mr-3 text-neon-blue" />
                  +****************
                </div>
                <div className="flex items-center text-gray-400">
                  <Mail size={16} className="mr-3 text-neon-blue" />
                  <EMAIL>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Quick Links */}
          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <h4 className="text-lg font-tech font-semibold text-white mb-6">
                Quick Links
              </h4>
              <ul className="space-y-3">
                {quickLinks.map((link) => (
                  <li key={link.name}>
                    <button
                      onClick={() => scrollToSection(link.href)}
                      className="text-gray-400 hover:text-neon-blue transition-colors duration-300 font-tech"
                    >
                      {link.name}
                    </button>
                  </li>
                ))}
              </ul>
            </motion.div>
          </div>

          {/* Services */}
          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <h4 className="text-lg font-tech font-semibold text-white mb-6">
                Services
              </h4>
              <ul className="space-y-3">
                {services.map((service) => (
                  <li key={service} className="text-gray-400 font-tech">
                    <span className="text-neon-green mr-2">▸</span>
                    {service}
                  </li>
                ))}
              </ul>
            </motion.div>
          </div>

          {/* Social & Newsletter */}
          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <h4 className="text-lg font-tech font-semibold text-white mb-6">
                Connect With Me
              </h4>
              
              {/* Social Links */}
              <div className="flex space-x-4 mb-8">
                {socialLinks.map((social) => (
                  <motion.a
                    key={social.name}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`p-3 bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg text-gray-400 ${social.color} transition-all duration-300`}
                    whileHover={{ scale: 1.1, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <social.icon size={20} />
                  </motion.a>
                ))}
              </div>

              {/* Newsletter Signup */}
              <div className="bg-gray-900/30 backdrop-blur-sm border border-gray-700 rounded-lg p-4">
                <h5 className="text-white font-tech font-medium mb-3">
                  Stay Updated
                </h5>
                <p className="text-gray-400 text-sm mb-4">
                  Get notified about new projects and blog posts.
                </p>
                <div className="flex">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className="flex-1 px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-l-md text-white text-sm placeholder-gray-400 focus:outline-none focus:border-neon-blue"
                  />
                  <button className="px-4 py-2 bg-neon-blue text-black rounded-r-md hover:bg-white transition-colors font-tech font-medium text-sm">
                    Subscribe
                  </button>
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Bottom Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="border-t border-gray-800 mt-12 pt-8"
        >
          <div className="flex flex-col md:flex-row justify-between items-center">
            {/* Copyright */}
            <div className="flex items-center text-gray-400 text-sm mb-4 md:mb-0">
              <span>© {currentYear} Your Name. Made with</span>
              <Heart size={16} className="mx-2 text-red-500 fill-current" />
              <span>and</span>
              <Coffee size={16} className="mx-2 text-yellow-600" />
              <span>using</span>
              <Code size={16} className="ml-2 text-neon-blue" />
            </div>

            {/* Tech Stack */}
            <div className="flex items-center space-x-4 text-xs text-gray-500">
              <span className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                Next.js
              </span>
              <span className="flex items-center">
                <span className="w-2 h-2 bg-cyan-500 rounded-full mr-2"></span>
                Tailwind CSS
              </span>
              <span className="flex items-center">
                <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                Framer Motion
              </span>
              <span className="flex items-center">
                <span className="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
                Three.js
              </span>
            </div>
          </div>

          {/* Fun Stats */}
          <div className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div className="bg-gray-900/30 backdrop-blur-sm border border-gray-700 rounded-lg p-3">
              <div className="text-neon-blue font-cyber font-bold text-lg">☕</div>
              <div className="text-gray-400 text-xs font-tech">Cups of Coffee</div>
              <div className="text-white text-sm font-tech">∞</div>
            </div>
            <div className="bg-gray-900/30 backdrop-blur-sm border border-gray-700 rounded-lg p-3">
              <div className="text-neon-green font-cyber font-bold text-lg">🚀</div>
              <div className="text-gray-400 text-xs font-tech">Projects Launched</div>
              <div className="text-white text-sm font-tech">50+</div>
            </div>
            <div className="bg-gray-900/30 backdrop-blur-sm border border-gray-700 rounded-lg p-3">
              <div className="text-neon-pink font-cyber font-bold text-lg">🧠</div>
              <div className="text-gray-400 text-xs font-tech">Lines of Code</div>
              <div className="text-white text-sm font-tech">100K+</div>
            </div>
            <div className="bg-gray-900/30 backdrop-blur-sm border border-gray-700 rounded-lg p-3">
              <div className="text-neon-purple font-cyber font-bold text-lg">⭐</div>
              <div className="text-gray-400 text-xs font-tech">GitHub Stars</div>
              <div className="text-white text-sm font-tech">500+</div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-10 left-10 w-4 h-4 border border-neon-blue/30 rotate-45 animate-pulse"></div>
      <div className="absolute top-20 right-20 w-6 h-6 border border-neon-green/30 rounded-full animate-bounce"></div>
      <div className="absolute bottom-10 left-1/4 w-3 h-3 bg-neon-pink/30 rounded-full animate-ping"></div>
    </footer>
  )
}
