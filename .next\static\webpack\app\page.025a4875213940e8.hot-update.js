"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/Blog.tsx":
/*!*****************************!*\
  !*** ./components/Blog.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Blog; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-intersection-observer */ \"(app-pages-browser)/./node_modules/react-intersection-observer/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Clock_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Clock,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Clock_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Clock,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Clock_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Clock,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Clock_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Clock,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst blogPosts = [\n    {\n        id: 1,\n        title: \"Building AI-Powered Web Applications: A Complete Guide\",\n        excerpt: \"Learn how to integrate AI capabilities into your web applications using modern frameworks and APIs. This comprehensive guide covers everything from setup to deployment.\",\n        content: \"Full blog post content would go here...\",\n        author: \"Your Name\",\n        date: \"2024-01-15\",\n        readTime: \"8 min read\",\n        tags: [\n            \"AI\",\n            \"Web Development\",\n            \"React\",\n            \"OpenAI\"\n        ],\n        image: \"/api/placeholder/600/300\",\n        featured: true\n    },\n    {\n        id: 2,\n        title: \"The Future of Web Development: Trends to Watch in 2024\",\n        excerpt: \"Explore the latest trends and technologies shaping the future of web development. From AI integration to new frameworks, here's what you need to know.\",\n        content: \"Full blog post content would go here...\",\n        author: \"Your Name\",\n        date: \"2024-01-10\",\n        readTime: \"6 min read\",\n        tags: [\n            \"Web Development\",\n            \"Trends\",\n            \"Technology\"\n        ],\n        image: \"/api/placeholder/600/300\",\n        featured: false\n    },\n    {\n        id: 3,\n        title: \"My Journey as a Self-Taught Developer: Lessons Learned\",\n        excerpt: \"Sharing my experience and insights from learning to code independently. Tips, challenges, and advice for aspiring self-taught developers.\",\n        content: \"Full blog post content would go here...\",\n        author: \"Your Name\",\n        date: \"2024-01-05\",\n        readTime: \"10 min read\",\n        tags: [\n            \"Career\",\n            \"Learning\",\n            \"Personal\"\n        ],\n        image: \"/api/placeholder/600/300\",\n        featured: false\n    },\n    {\n        id: 4,\n        title: \"Optimizing React Applications for Better Performance\",\n        excerpt: \"Practical tips and techniques to improve your React application's performance. Learn about code splitting, memoization, and other optimization strategies.\",\n        content: \"Full blog post content would go here...\",\n        author: \"Your Name\",\n        date: \"2023-12-28\",\n        readTime: \"7 min read\",\n        tags: [\n            \"React\",\n            \"Performance\",\n            \"Optimization\"\n        ],\n        image: \"/api/placeholder/600/300\",\n        featured: false\n    }\n];\nconst tagColors = {\n    \"AI\": \"bg-purple-500/20 text-purple-400 border-purple-500/30\",\n    \"Web Development\": \"bg-blue-500/20 text-blue-400 border-blue-500/30\",\n    \"React\": \"bg-cyan-500/20 text-cyan-400 border-cyan-500/30\",\n    \"OpenAI\": \"bg-green-500/20 text-green-400 border-green-500/30\",\n    \"Trends\": \"bg-orange-500/20 text-orange-400 border-orange-500/30\",\n    \"Technology\": \"bg-red-500/20 text-red-400 border-red-500/30\",\n    \"Career\": \"bg-yellow-500/20 text-yellow-400 border-yellow-500/30\",\n    \"Learning\": \"bg-pink-500/20 text-pink-400 border-pink-500/30\",\n    \"Personal\": \"bg-indigo-500/20 text-indigo-400 border-indigo-500/30\",\n    \"Performance\": \"bg-teal-500/20 text-teal-400 border-teal-500/30\",\n    \"Optimization\": \"bg-emerald-500/20 text-emerald-400 border-emerald-500/30\"\n};\nfunction Blog() {\n    _s();\n    const [ref, inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_1__.useInView)({\n        triggerOnce: true,\n        threshold: 0.1\n    });\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6\n            }\n        }\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        ref: ref,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                variants: containerVariants,\n                initial: \"hidden\",\n                animate: inView ? \"visible\" : \"hidden\",\n                className: \"text-center mb-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h2, {\n                        variants: itemVariants,\n                        className: \"text-4xl md:text-6xl font-cyber font-bold text-neon-blue neon-glow-subtle mb-6\",\n                        children: \"Blog & Insights\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        variants: itemVariants,\n                        className: \"w-24 h-1 bg-gradient-to-r from-neon-blue to-neon-green mx-auto mb-8\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                        variants: itemVariants,\n                        className: \"text-xl text-gray-400 max-w-3xl mx-auto\",\n                        children: \"Sharing my learning journey, technical insights, and thoughts on the future of technology.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            blogPosts.find((post)=>post.featured) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                variants: itemVariants,\n                initial: \"hidden\",\n                animate: inView ? \"visible\" : \"hidden\",\n                className: \"mb-16\",\n                children: (()=>{\n                    const featuredPost = blogPosts.find((post)=>post.featured);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900/50 backdrop-blur-sm border border-neon-blue/30 rounded-lg overflow-hidden hover:border-neon-blue/50 transition-all duration-300 group\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-64 md:h-full bg-gradient-to-br from-neon-blue/20 to-neon-green/20 flex items-center justify-center relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-4 left-4 bg-neon-blue text-black px-3 py-1 rounded-full text-sm font-tech font-semibold\",\n                                                children: \"Featured\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-6xl\",\n                                                children: \"\\uD83D\\uDCDD\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:w-1/2 p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-sm text-gray-400 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 23\n                                                }, this),\n                                                formatDate(featuredPost.date),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"ml-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 23\n                                                }, this),\n                                                featuredPost.readTime\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-tech font-semibold text-white mb-4 group-hover:text-neon-blue transition-colors\",\n                                            children: featuredPost.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 mb-6 leading-relaxed\",\n                                            children: featuredPost.excerpt\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 mb-6\",\n                                            children: featuredPost.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-3 py-1 text-xs font-tech font-medium rounded-full border \".concat(tagColors[tag] || \"bg-gray-500/20 text-gray-400 border-gray-500/30\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            size: 12,\n                                                            className: \"inline mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        tag\n                                                    ]\n                                                }, tag, true, {\n                                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                            className: \"flex items-center text-neon-blue hover:text-neon-green transition-colors font-tech font-medium\",\n                                            whileHover: {\n                                                x: 5\n                                            },\n                                            children: [\n                                                \"Read More\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 15\n                    }, this);\n                })()\n            }, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                variants: containerVariants,\n                initial: \"hidden\",\n                animate: inView ? \"visible\" : \"hidden\",\n                className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                children: blogPosts.filter((post)=>!post.featured).map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.article, {\n                        variants: itemVariants,\n                        className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg overflow-hidden hover:border-neon-blue/50 transition-all duration-300 group\",\n                        whileHover: {\n                            y: -5\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-48 bg-gradient-to-br from-neon-blue/20 to-neon-green/20 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl\",\n                                    children: \"\\uD83D\\uDCC4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-sm text-gray-400 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                size: 14,\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this),\n                                            formatDate(post.date),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                size: 14,\n                                                className: \"ml-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this),\n                                            post.readTime\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-tech font-semibold text-white mb-3 group-hover:text-neon-blue transition-colors line-clamp-2\",\n                                        children: post.title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4 text-sm leading-relaxed line-clamp-3\",\n                                        children: post.excerpt\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mb-4\",\n                                        children: [\n                                            post.tags.slice(0, 2).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 text-xs font-tech font-medium rounded-full border \".concat(tagColors[tag] || \"bg-gray-500/20 text-gray-400 border-gray-500/30\"),\n                                                    children: tag\n                                                }, tag, false, {\n                                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            post.tags.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 text-xs font-tech font-medium rounded-full border bg-gray-500/20 text-gray-400 border-gray-500/30\",\n                                                children: [\n                                                    \"+\",\n                                                    post.tags.length - 2\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                        className: \"flex items-center text-neon-blue hover:text-neon-green transition-colors font-tech font-medium text-sm\",\n                                        whileHover: {\n                                            x: 3\n                                        },\n                                        children: [\n                                            \"Read Article\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                size: 14,\n                                                className: \"ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, post.id, true, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                variants: itemVariants,\n                initial: \"hidden\",\n                animate: inView ? \"visible\" : \"hidden\",\n                className: \"text-center mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                    className: \"btn-cyber group flex items-center gap-3 mx-auto\",\n                    whileHover: {\n                        scale: 1.05\n                    },\n                    whileTap: {\n                        scale: 0.95\n                    },\n                    children: [\n                        \"View All Posts\",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Blog.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, this);\n}\n_s(Blog, \"GpcLnEGLCRT/LcXgsVwPMCbjDPg=\", false, function() {\n    return [\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_1__.useInView\n    ];\n});\n_c = Blog;\nvar _c;\n$RefreshReg$(_c, \"Blog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Blog.tsx\n"));

/***/ })

});