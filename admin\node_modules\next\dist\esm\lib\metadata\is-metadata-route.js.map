{"version": 3, "sources": ["../../../src/lib/metadata/is-metadata-route.ts"], "names": ["normalizePathSep", "STATIC_METADATA_IMAGES", "icon", "filename", "extensions", "apple", "favicon", "openGraph", "twitter", "defaultExtensions", "getExtensionRegexString", "join", "isMetadataRouteFile", "appDirRelativePath", "pageExtensions", "withExtension", "metadataRouteFilesRegex", "RegExp", "concat", "normalizedAppDirRelativePath", "some", "r", "test", "isStaticMetadataRouteFile", "isStaticMetadataRoute", "page", "isMetadataRoute", "route", "replace", "endsWith"], "mappings": "AACA,SAASA,gBAAgB,QAAQ,gDAA+C;AAEhF,OAAO,MAAMC,yBAAyB;IACpCC,MAAM;QACJC,UAAU;QACVC,YAAY;YAAC;YAAO;YAAO;YAAQ;YAAO;SAAM;IAClD;IACAC,OAAO;QACLF,UAAU;QACVC,YAAY;YAAC;YAAO;YAAQ;SAAM;IACpC;IACAE,SAAS;QACPH,UAAU;QACVC,YAAY;YAAC;SAAM;IACrB;IACAG,WAAW;QACTJ,UAAU;QACVC,YAAY;YAAC;YAAO;YAAQ;YAAO;SAAM;IAC3C;IACAI,SAAS;QACPL,UAAU;QACVC,YAAY;YAAC;YAAO;YAAQ;YAAO;SAAM;IAC3C;AACF,EAAU;AAEV,gGAAgG;AAChG,mEAAmE;AACnE,MAAMK,oBAAoB;IAAC;IAAM;IAAO;IAAM;CAAM;AAEpD,MAAMC,0BAA0B,CAACN,aAC/B,CAAC,GAAG,EAAEA,WAAWO,IAAI,CAAC,KAAK,CAAC,CAAC;AAE/B,gGAAgG;AAChG,+DAA+D;AAC/D,oHAAoH;AACpH,4DAA4D;AAC5D,uHAAuH;AACvH,kGAAkG;AAClG,OAAO,SAASC,oBACdC,kBAA0B,EAC1BC,cAA8B,EAC9BC,aAAsB;IAEtB,MAAMC,0BAA0B;QAC9B,IAAIC,OACF,CAAC,cAAc,EACbF,gBACI,CAAC,GAAG,EAAEL,wBAAwBI,eAAeI,MAAM,CAAC,QAAQ,CAAC,CAAC,GAC9D,GACL,CAAC;QAEJ,IAAID,OACF,CAAC,gBAAgB,EACfF,gBACI,CAAC,GAAG,EAAEL,wBACJI,eAAeI,MAAM,CAAC,eAAe,SACrC,CAAC,CAAC,GACJ,GACL,CAAC;QAEJ,IAAID,OAAO,CAAC,sBAAsB,CAAC;QACnC,IAAIA,OACF,CAAC,cAAc,EACbF,gBACI,CAAC,GAAG,EAAEL,wBAAwBI,eAAeI,MAAM,CAAC,QAAQ,CAAC,CAAC,GAC9D,GACL,CAAC;QAEJ,IAAID,OACF,CAAC,OAAO,EAAEhB,uBAAuBC,IAAI,CAACC,QAAQ,CAAC,IAAI,EACjDY,gBACI,CAAC,GAAG,EAAEL,wBACJI,eAAeI,MAAM,CAACjB,uBAAuBC,IAAI,CAACE,UAAU,GAC5D,CAAC,CAAC,GACJ,GACL,CAAC;QAEJ,IAAIa,OACF,CAAC,OAAO,EAAEhB,uBAAuBI,KAAK,CAACF,QAAQ,CAAC,IAAI,EAClDY,gBACI,CAAC,GAAG,EAAEL,wBACJI,eAAeI,MAAM,CAACjB,uBAAuBI,KAAK,CAACD,UAAU,GAC7D,CAAC,CAAC,GACJ,GACL,CAAC;QAEJ,IAAIa,OACF,CAAC,OAAO,EAAEhB,uBAAuBM,SAAS,CAACJ,QAAQ,CAAC,IAAI,EACtDY,gBACI,CAAC,GAAG,EAAEL,wBACJI,eAAeI,MAAM,CAACjB,uBAAuBM,SAAS,CAACH,UAAU,GACjE,CAAC,CAAC,GACJ,GACL,CAAC;QAEJ,IAAIa,OACF,CAAC,OAAO,EAAEhB,uBAAuBO,OAAO,CAACL,QAAQ,CAAC,IAAI,EACpDY,gBACI,CAAC,GAAG,EAAEL,wBACJI,eAAeI,MAAM,CAACjB,uBAAuBO,OAAO,CAACJ,UAAU,GAC/D,CAAC,CAAC,GACJ,GACL,CAAC;KAEL;IAED,MAAMe,+BAA+BnB,iBAAiBa;IACtD,OAAOG,wBAAwBI,IAAI,CAAC,CAACC,IACnCA,EAAEC,IAAI,CAACH;AAEX;AAEA,OAAO,SAASI,0BAA0BV,kBAA0B;IAClE,OAAOD,oBAAoBC,oBAAoB,EAAE,EAAE;AACrD;AAEA,OAAO,SAASW,sBAAsBC,IAAY;IAChD,OACEA,SAAS,aACTA,SAAS,eACTF,0BAA0BE;AAE9B;AAEA;;;;;;CAMC,GACD,OAAO,SAASC,gBAAgBC,KAAa;IAC3C,IAAIF,OAAOE,MAAMC,OAAO,CAAC,aAAa,IAAIA,OAAO,CAAC,YAAY;IAC9D,IAAIH,IAAI,CAAC,EAAE,KAAK,KAAKA,OAAO,MAAMA;IAElC,OACE,CAACA,KAAKI,QAAQ,CAAC,YACfjB,oBAAoBa,MAAMhB,mBAAmB;AAEjD"}