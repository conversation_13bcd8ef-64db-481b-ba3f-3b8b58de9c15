'use client'

import { useEffect, useState } from 'react'
import Navbar from '@/components/Navbar'
import Hero from '@/components/Hero'
import About from '@/components/About'
import Projects from '@/components/Projects'
import Skills from '@/components/Skills'
import Resume from '@/components/Resume'
import Contact from '@/components/Contact'
import Blog from '@/components/Blog'
import Testimonials from '@/components/Testimonials'
import Footer from '@/components/Footer'
import BackToTop from '@/components/BackToTop'
import ParticleBackground from '@/components/ParticleBackground'

export default function Home() {
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulate loading time
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 2000)

    return () => clearTimeout(timer)
  }, [])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="loading-dots mb-8">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
          </div>
          <h2 className="text-2xl font-cyber text-neon-blue neon-glow-subtle">
            Initializing Portfolio...
          </h2>
        </div>
      </div>
    )
  }

  return (
    <main className="relative min-h-screen bg-black text-white overflow-x-hidden">
      <ParticleBackground />
      <Navbar />
      
      <section id="home">
        <Hero />
      </section>
      
      <section id="about" className="py-20">
        <About />
      </section>
      
      <section id="projects" className="py-20">
        <Projects />
      </section>
      
      <section id="skills" className="py-20">
        <Skills />
      </section>
      
      <section id="resume" className="py-20">
        <Resume />
      </section>
      
      <section id="blog" className="py-20">
        <Blog />
      </section>
      
      <section id="testimonials" className="py-20">
        <Testimonials />
      </section>
      
      <section id="contact" className="py-20">
        <Contact />
      </section>
      
      <Footer />
      <BackToTop />
    </main>
  )
}
