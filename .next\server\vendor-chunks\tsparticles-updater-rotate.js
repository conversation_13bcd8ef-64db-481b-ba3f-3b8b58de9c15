"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tsparticles-updater-rotate";
exports.ids = ["vendor-chunks/tsparticles-updater-rotate"];
exports.modules = {

/***/ "(ssr)/./node_modules/tsparticles-updater-rotate/esm/Options/Classes/Rotate.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/tsparticles-updater-rotate/esm/Options/Classes/Rotate.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Rotate: () => (/* binding */ Rotate)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Options/Classes/ValueWithRandom.js\");\n/* harmony import */ var _RotateAnimation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./RotateAnimation */ \"(ssr)/./node_modules/tsparticles-updater-rotate/esm/Options/Classes/RotateAnimation.js\");\n\n\nclass Rotate extends tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ValueWithRandom {\n    constructor() {\n        super();\n        this.animation = new _RotateAnimation__WEBPACK_IMPORTED_MODULE_1__.RotateAnimation();\n        this.direction = \"clockwise\";\n        this.path = false;\n        this.value = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        super.load(data);\n        if (data.direction !== undefined) {\n            this.direction = data.direction;\n        }\n        this.animation.load(data.animation);\n        if (data.path !== undefined) {\n            this.path = data.path;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtdXBkYXRlci1yb3RhdGUvZXNtL09wdGlvbnMvQ2xhc3Nlcy9Sb3RhdGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXNEO0FBQ0Y7QUFDN0MscUJBQXFCLCtEQUFlO0FBQzNDO0FBQ0E7QUFDQSw2QkFBNkIsNkRBQWU7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnV0dXJpc3RpYy1wb3J0Zm9saW8vLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtdXBkYXRlci1yb3RhdGUvZXNtL09wdGlvbnMvQ2xhc3Nlcy9Sb3RhdGUuanM/ZDUxNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBWYWx1ZVdpdGhSYW5kb20sIH0gZnJvbSBcInRzcGFydGljbGVzLWVuZ2luZVwiO1xuaW1wb3J0IHsgUm90YXRlQW5pbWF0aW9uIH0gZnJvbSBcIi4vUm90YXRlQW5pbWF0aW9uXCI7XG5leHBvcnQgY2xhc3MgUm90YXRlIGV4dGVuZHMgVmFsdWVXaXRoUmFuZG9tIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgc3VwZXIoKTtcbiAgICAgICAgdGhpcy5hbmltYXRpb24gPSBuZXcgUm90YXRlQW5pbWF0aW9uKCk7XG4gICAgICAgIHRoaXMuZGlyZWN0aW9uID0gXCJjbG9ja3dpc2VcIjtcbiAgICAgICAgdGhpcy5wYXRoID0gZmFsc2U7XG4gICAgICAgIHRoaXMudmFsdWUgPSAwO1xuICAgIH1cbiAgICBsb2FkKGRhdGEpIHtcbiAgICAgICAgaWYgKCFkYXRhKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgc3VwZXIubG9hZChkYXRhKTtcbiAgICAgICAgaWYgKGRhdGEuZGlyZWN0aW9uICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHRoaXMuZGlyZWN0aW9uID0gZGF0YS5kaXJlY3Rpb247XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5hbmltYXRpb24ubG9hZChkYXRhLmFuaW1hdGlvbik7XG4gICAgICAgIGlmIChkYXRhLnBhdGggIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5wYXRoID0gZGF0YS5wYXRoO1xuICAgICAgICB9XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-rotate/esm/Options/Classes/Rotate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-updater-rotate/esm/Options/Classes/RotateAnimation.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/tsparticles-updater-rotate/esm/Options/Classes/RotateAnimation.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RotateAnimation: () => (/* binding */ RotateAnimation)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n\nclass RotateAnimation {\n    constructor() {\n        this.enable = false;\n        this.speed = 0;\n        this.decay = 0;\n        this.sync = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.speed !== undefined) {\n            this.speed = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(data.speed);\n        }\n        if (data.decay !== undefined) {\n            this.decay = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(data.decay);\n        }\n        if (data.sync !== undefined) {\n            this.sync = data.sync;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtdXBkYXRlci1yb3RhdGUvZXNtL09wdGlvbnMvQ2xhc3Nlcy9Sb3RhdGVBbmltYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBbUQ7QUFDNUM7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLGlFQUFhO0FBQ3RDO0FBQ0E7QUFDQSx5QkFBeUIsaUVBQWE7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnV0dXJpc3RpYy1wb3J0Zm9saW8vLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtdXBkYXRlci1yb3RhdGUvZXNtL09wdGlvbnMvQ2xhc3Nlcy9Sb3RhdGVBbmltYXRpb24uanM/NTc1MyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzZXRSYW5nZVZhbHVlIH0gZnJvbSBcInRzcGFydGljbGVzLWVuZ2luZVwiO1xuZXhwb3J0IGNsYXNzIFJvdGF0ZUFuaW1hdGlvbiB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHRoaXMuZW5hYmxlID0gZmFsc2U7XG4gICAgICAgIHRoaXMuc3BlZWQgPSAwO1xuICAgICAgICB0aGlzLmRlY2F5ID0gMDtcbiAgICAgICAgdGhpcy5zeW5jID0gZmFsc2U7XG4gICAgfVxuICAgIGxvYWQoZGF0YSkge1xuICAgICAgICBpZiAoIWRhdGEpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5lbmFibGUgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5lbmFibGUgPSBkYXRhLmVuYWJsZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5zcGVlZCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aGlzLnNwZWVkID0gc2V0UmFuZ2VWYWx1ZShkYXRhLnNwZWVkKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5kZWNheSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aGlzLmRlY2F5ID0gc2V0UmFuZ2VWYWx1ZShkYXRhLmRlY2F5KTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5zeW5jICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHRoaXMuc3luYyA9IGRhdGEuc3luYztcbiAgICAgICAgfVxuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-rotate/esm/Options/Classes/RotateAnimation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-updater-rotate/esm/RotateUpdater.js":
/*!**********************************************************************!*\
  !*** ./node_modules/tsparticles-updater-rotate/esm/RotateUpdater.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RotateUpdater: () => (/* binding */ RotateUpdater)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n/* harmony import */ var _Options_Classes_Rotate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Options/Classes/Rotate */ \"(ssr)/./node_modules/tsparticles-updater-rotate/esm/Options/Classes/Rotate.js\");\n\n\nfunction updateRotate(particle, delta) {\n    const rotate = particle.rotate, rotateOptions = particle.options.rotate;\n    if (!rotate || !rotateOptions) {\n        return;\n    }\n    const rotateAnimation = rotateOptions.animation, speed = (rotate.velocity ?? 0) * delta.factor, max = 2 * Math.PI, decay = rotate.decay ?? 1;\n    if (!rotateAnimation.enable) {\n        return;\n    }\n    switch (rotate.status) {\n        case \"increasing\":\n            rotate.value += speed;\n            if (rotate.value > max) {\n                rotate.value -= max;\n            }\n            break;\n        case \"decreasing\":\n        default:\n            rotate.value -= speed;\n            if (rotate.value < 0) {\n                rotate.value += max;\n            }\n            break;\n    }\n    if (rotate.velocity && decay !== 1) {\n        rotate.velocity *= decay;\n    }\n}\nclass RotateUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const rotateOptions = particle.options.rotate;\n        if (!rotateOptions) {\n            return;\n        }\n        particle.rotate = {\n            enable: rotateOptions.animation.enable,\n            value: ((0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(rotateOptions.value) * Math.PI) / 180,\n        };\n        particle.pathRotation = rotateOptions.path;\n        let rotateDirection = rotateOptions.direction;\n        if (rotateDirection === \"random\") {\n            const index = Math.floor((0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)() * 2);\n            rotateDirection = index > 0 ? \"counter-clockwise\" : \"clockwise\";\n        }\n        switch (rotateDirection) {\n            case \"counter-clockwise\":\n            case \"counterClockwise\":\n                particle.rotate.status = \"decreasing\";\n                break;\n            case \"clockwise\":\n                particle.rotate.status = \"increasing\";\n                break;\n        }\n        const rotateAnimation = rotateOptions.animation;\n        if (rotateAnimation.enable) {\n            particle.rotate.decay = 1 - (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(rotateAnimation.decay);\n            particle.rotate.velocity =\n                ((0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(rotateAnimation.speed) / 360) * this.container.retina.reduceFactor;\n            if (!rotateAnimation.sync) {\n                particle.rotate.velocity *= (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)();\n            }\n        }\n        particle.rotation = particle.rotate.value;\n    }\n    isEnabled(particle) {\n        const rotate = particle.options.rotate;\n        if (!rotate) {\n            return false;\n        }\n        return !particle.destroyed && !particle.spawning && rotate.animation.enable && !rotate.path;\n    }\n    loadOptions(options, ...sources) {\n        if (!options.rotate) {\n            options.rotate = new _Options_Classes_Rotate__WEBPACK_IMPORTED_MODULE_1__.Rotate();\n        }\n        for (const source of sources) {\n            options.rotate.load(source?.rotate);\n        }\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        updateRotate(particle, delta);\n        particle.rotation = particle.rotate?.value ?? 0;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-rotate/esm/RotateUpdater.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-updater-rotate/esm/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/tsparticles-updater-rotate/esm/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadRotateUpdater: () => (/* binding */ loadRotateUpdater)\n/* harmony export */ });\n/* harmony import */ var _RotateUpdater__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./RotateUpdater */ \"(ssr)/./node_modules/tsparticles-updater-rotate/esm/RotateUpdater.js\");\n\nasync function loadRotateUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"rotate\", (container) => new _RotateUpdater__WEBPACK_IMPORTED_MODULE_0__.RotateUpdater(container), refresh);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtdXBkYXRlci1yb3RhdGUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWdEO0FBQ3pDO0FBQ1AsaUVBQWlFLHlEQUFhO0FBQzlFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnV0dXJpc3RpYy1wb3J0Zm9saW8vLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtdXBkYXRlci1yb3RhdGUvZXNtL2luZGV4LmpzP2RmODAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUm90YXRlVXBkYXRlciB9IGZyb20gXCIuL1JvdGF0ZVVwZGF0ZXJcIjtcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBsb2FkUm90YXRlVXBkYXRlcihlbmdpbmUsIHJlZnJlc2ggPSB0cnVlKSB7XG4gICAgYXdhaXQgZW5naW5lLmFkZFBhcnRpY2xlVXBkYXRlcihcInJvdGF0ZVwiLCAoY29udGFpbmVyKSA9PiBuZXcgUm90YXRlVXBkYXRlcihjb250YWluZXIpLCByZWZyZXNoKTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-rotate/esm/index.js\n");

/***/ })

};
;