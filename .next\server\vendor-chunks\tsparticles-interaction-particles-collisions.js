"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tsparticles-interaction-particles-collisions";
exports.ids = ["vendor-chunks/tsparticles-interaction-particles-collisions"];
exports.modules = {

/***/ "(ssr)/./node_modules/tsparticles-interaction-particles-collisions/esm/Absorb.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-particles-collisions/esm/Absorb.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   absorb: () => (/* binding */ absorb)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n\nfunction updateAbsorb(p1, r1, p2, r2, delta, pixelRatio) {\n    const factor = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.clamp)((p1.options.collisions.absorb.speed * delta.factor) / 10, 0, r2);\n    p1.size.value += factor / 2;\n    p2.size.value -= factor;\n    if (r2 <= pixelRatio) {\n        p2.size.value = 0;\n        p2.destroy();\n    }\n}\nfunction absorb(p1, p2, delta, pixelRatio) {\n    const r1 = p1.getRadius(), r2 = p2.getRadius();\n    if (r1 === undefined && r2 !== undefined) {\n        p1.destroy();\n    }\n    else if (r1 !== undefined && r2 === undefined) {\n        p2.destroy();\n    }\n    else if (r1 !== undefined && r2 !== undefined) {\n        if (r1 >= r2) {\n            updateAbsorb(p1, r1, p2, r2, delta, pixelRatio);\n        }\n        else {\n            updateAbsorb(p2, r2, p1, r1, delta, pixelRatio);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tcGFydGljbGVzLWNvbGxpc2lvbnMvZXNtL0Fic29yYi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEyQztBQUMzQztBQUNBLG1CQUFtQix5REFBSztBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mdXR1cmlzdGljLXBvcnRmb2xpby8uL25vZGVfbW9kdWxlcy90c3BhcnRpY2xlcy1pbnRlcmFjdGlvbi1wYXJ0aWNsZXMtY29sbGlzaW9ucy9lc20vQWJzb3JiLmpzPzJkOTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2xhbXAgfSBmcm9tIFwidHNwYXJ0aWNsZXMtZW5naW5lXCI7XG5mdW5jdGlvbiB1cGRhdGVBYnNvcmIocDEsIHIxLCBwMiwgcjIsIGRlbHRhLCBwaXhlbFJhdGlvKSB7XG4gICAgY29uc3QgZmFjdG9yID0gY2xhbXAoKHAxLm9wdGlvbnMuY29sbGlzaW9ucy5hYnNvcmIuc3BlZWQgKiBkZWx0YS5mYWN0b3IpIC8gMTAsIDAsIHIyKTtcbiAgICBwMS5zaXplLnZhbHVlICs9IGZhY3RvciAvIDI7XG4gICAgcDIuc2l6ZS52YWx1ZSAtPSBmYWN0b3I7XG4gICAgaWYgKHIyIDw9IHBpeGVsUmF0aW8pIHtcbiAgICAgICAgcDIuc2l6ZS52YWx1ZSA9IDA7XG4gICAgICAgIHAyLmRlc3Ryb3koKTtcbiAgICB9XG59XG5leHBvcnQgZnVuY3Rpb24gYWJzb3JiKHAxLCBwMiwgZGVsdGEsIHBpeGVsUmF0aW8pIHtcbiAgICBjb25zdCByMSA9IHAxLmdldFJhZGl1cygpLCByMiA9IHAyLmdldFJhZGl1cygpO1xuICAgIGlmIChyMSA9PT0gdW5kZWZpbmVkICYmIHIyICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgcDEuZGVzdHJveSgpO1xuICAgIH1cbiAgICBlbHNlIGlmIChyMSAhPT0gdW5kZWZpbmVkICYmIHIyID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgcDIuZGVzdHJveSgpO1xuICAgIH1cbiAgICBlbHNlIGlmIChyMSAhPT0gdW5kZWZpbmVkICYmIHIyICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgaWYgKHIxID49IHIyKSB7XG4gICAgICAgICAgICB1cGRhdGVBYnNvcmIocDEsIHIxLCBwMiwgcjIsIGRlbHRhLCBwaXhlbFJhdGlvKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHVwZGF0ZUFic29yYihwMiwgcjIsIHAxLCByMSwgZGVsdGEsIHBpeGVsUmF0aW8pO1xuICAgICAgICB9XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-particles-collisions/esm/Absorb.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-particles-collisions/esm/Bounce.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-particles-collisions/esm/Bounce.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bounce: () => (/* binding */ bounce)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/Utils.js\");\n\nconst fixBounceSpeed = (p) => {\n    if (p.collisionMaxSpeed === undefined) {\n        p.collisionMaxSpeed = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(p.options.collisions.maxSpeed);\n    }\n    if (p.velocity.length > p.collisionMaxSpeed) {\n        p.velocity.length = p.collisionMaxSpeed;\n    }\n};\nfunction bounce(p1, p2) {\n    (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.circleBounce)((0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.circleBounceDataFromParticle)(p1), (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.circleBounceDataFromParticle)(p2));\n    fixBounceSpeed(p1);\n    fixBounceSpeed(p2);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tcGFydGljbGVzLWNvbGxpc2lvbnMvZXNtL0JvdW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0Y7QUFDL0Y7QUFDQTtBQUNBLDhCQUE4QixpRUFBYTtBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCxJQUFJLGdFQUFZLENBQUMsZ0ZBQTRCLE1BQU0sZ0ZBQTRCO0FBQy9FO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1dHVyaXN0aWMtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL3RzcGFydGljbGVzLWludGVyYWN0aW9uLXBhcnRpY2xlcy1jb2xsaXNpb25zL2VzbS9Cb3VuY2UuanM/MjY4MyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjaXJjbGVCb3VuY2UsIGNpcmNsZUJvdW5jZURhdGFGcm9tUGFydGljbGUsIGdldFJhbmdlVmFsdWUgfSBmcm9tIFwidHNwYXJ0aWNsZXMtZW5naW5lXCI7XG5jb25zdCBmaXhCb3VuY2VTcGVlZCA9IChwKSA9PiB7XG4gICAgaWYgKHAuY29sbGlzaW9uTWF4U3BlZWQgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICBwLmNvbGxpc2lvbk1heFNwZWVkID0gZ2V0UmFuZ2VWYWx1ZShwLm9wdGlvbnMuY29sbGlzaW9ucy5tYXhTcGVlZCk7XG4gICAgfVxuICAgIGlmIChwLnZlbG9jaXR5Lmxlbmd0aCA+IHAuY29sbGlzaW9uTWF4U3BlZWQpIHtcbiAgICAgICAgcC52ZWxvY2l0eS5sZW5ndGggPSBwLmNvbGxpc2lvbk1heFNwZWVkO1xuICAgIH1cbn07XG5leHBvcnQgZnVuY3Rpb24gYm91bmNlKHAxLCBwMikge1xuICAgIGNpcmNsZUJvdW5jZShjaXJjbGVCb3VuY2VEYXRhRnJvbVBhcnRpY2xlKHAxKSwgY2lyY2xlQm91bmNlRGF0YUZyb21QYXJ0aWNsZShwMikpO1xuICAgIGZpeEJvdW5jZVNwZWVkKHAxKTtcbiAgICBmaXhCb3VuY2VTcGVlZChwMik7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-particles-collisions/esm/Bounce.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-particles-collisions/esm/Collider.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-particles-collisions/esm/Collider.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Collider: () => (/* binding */ Collider)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/ParticlesInteractorBase.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n/* harmony import */ var _ResolveCollision__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ResolveCollision */ \"(ssr)/./node_modules/tsparticles-interaction-particles-collisions/esm/ResolveCollision.js\");\n\n\nclass Collider extends tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ParticlesInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    clear() {\n    }\n    init() {\n    }\n    async interact(p1, delta) {\n        if (p1.destroyed || p1.spawning) {\n            return;\n        }\n        const container = this.container, pos1 = p1.getPosition(), radius1 = p1.getRadius(), query = container.particles.quadTree.queryCircle(pos1, radius1 * 2);\n        for (const p2 of query) {\n            if (p1 === p2 ||\n                !p2.options.collisions.enable ||\n                p1.options.collisions.mode !== p2.options.collisions.mode ||\n                p2.destroyed ||\n                p2.spawning) {\n                continue;\n            }\n            const pos2 = p2.getPosition(), radius2 = p2.getRadius();\n            if (Math.abs(Math.round(pos1.z) - Math.round(pos2.z)) > radius1 + radius2) {\n                continue;\n            }\n            const dist = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getDistance)(pos1, pos2), distP = radius1 + radius2;\n            if (dist > distP) {\n                continue;\n            }\n            (0,_ResolveCollision__WEBPACK_IMPORTED_MODULE_2__.resolveCollision)(p1, p2, delta, container.retina.pixelRatio);\n        }\n    }\n    isEnabled(particle) {\n        return particle.options.collisions.enable;\n    }\n    reset() {\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-particles-collisions/esm/Collider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-particles-collisions/esm/Destroy.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-particles-collisions/esm/Destroy.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   destroy: () => (/* binding */ destroy)\n/* harmony export */ });\n/* harmony import */ var _Bounce__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Bounce */ \"(ssr)/./node_modules/tsparticles-interaction-particles-collisions/esm/Bounce.js\");\n\nfunction destroy(p1, p2) {\n    if (!p1.unbreakable && !p2.unbreakable) {\n        (0,_Bounce__WEBPACK_IMPORTED_MODULE_0__.bounce)(p1, p2);\n    }\n    if (p1.getRadius() === undefined && p2.getRadius() !== undefined) {\n        p1.destroy();\n    }\n    else if (p1.getRadius() !== undefined && p2.getRadius() === undefined) {\n        p2.destroy();\n    }\n    else if (p1.getRadius() !== undefined && p2.getRadius() !== undefined) {\n        const deleteP = p1.getRadius() >= p2.getRadius() ? p2 : p1;\n        deleteP.destroy();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tcGFydGljbGVzLWNvbGxpc2lvbnMvZXNtL0Rlc3Ryb3kuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0M7QUFDM0I7QUFDUDtBQUNBLFFBQVEsK0NBQU07QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mdXR1cmlzdGljLXBvcnRmb2xpby8uL25vZGVfbW9kdWxlcy90c3BhcnRpY2xlcy1pbnRlcmFjdGlvbi1wYXJ0aWNsZXMtY29sbGlzaW9ucy9lc20vRGVzdHJveS5qcz84MGU1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGJvdW5jZSB9IGZyb20gXCIuL0JvdW5jZVwiO1xuZXhwb3J0IGZ1bmN0aW9uIGRlc3Ryb3kocDEsIHAyKSB7XG4gICAgaWYgKCFwMS51bmJyZWFrYWJsZSAmJiAhcDIudW5icmVha2FibGUpIHtcbiAgICAgICAgYm91bmNlKHAxLCBwMik7XG4gICAgfVxuICAgIGlmIChwMS5nZXRSYWRpdXMoKSA9PT0gdW5kZWZpbmVkICYmIHAyLmdldFJhZGl1cygpICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgcDEuZGVzdHJveSgpO1xuICAgIH1cbiAgICBlbHNlIGlmIChwMS5nZXRSYWRpdXMoKSAhPT0gdW5kZWZpbmVkICYmIHAyLmdldFJhZGl1cygpID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgcDIuZGVzdHJveSgpO1xuICAgIH1cbiAgICBlbHNlIGlmIChwMS5nZXRSYWRpdXMoKSAhPT0gdW5kZWZpbmVkICYmIHAyLmdldFJhZGl1cygpICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgY29uc3QgZGVsZXRlUCA9IHAxLmdldFJhZGl1cygpID49IHAyLmdldFJhZGl1cygpID8gcDIgOiBwMTtcbiAgICAgICAgZGVsZXRlUC5kZXN0cm95KCk7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-particles-collisions/esm/Destroy.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-particles-collisions/esm/ResolveCollision.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-particles-collisions/esm/ResolveCollision.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveCollision: () => (/* binding */ resolveCollision)\n/* harmony export */ });\n/* harmony import */ var _Absorb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Absorb */ \"(ssr)/./node_modules/tsparticles-interaction-particles-collisions/esm/Absorb.js\");\n/* harmony import */ var _Bounce__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Bounce */ \"(ssr)/./node_modules/tsparticles-interaction-particles-collisions/esm/Bounce.js\");\n/* harmony import */ var _Destroy__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Destroy */ \"(ssr)/./node_modules/tsparticles-interaction-particles-collisions/esm/Destroy.js\");\n\n\n\nfunction resolveCollision(p1, p2, delta, pixelRatio) {\n    switch (p1.options.collisions.mode) {\n        case \"absorb\": {\n            (0,_Absorb__WEBPACK_IMPORTED_MODULE_0__.absorb)(p1, p2, delta, pixelRatio);\n            break;\n        }\n        case \"bounce\": {\n            (0,_Bounce__WEBPACK_IMPORTED_MODULE_1__.bounce)(p1, p2);\n            break;\n        }\n        case \"destroy\": {\n            (0,_Destroy__WEBPACK_IMPORTED_MODULE_2__.destroy)(p1, p2);\n            break;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tcGFydGljbGVzLWNvbGxpc2lvbnMvZXNtL1Jlc29sdmVDb2xsaXNpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFrQztBQUNBO0FBQ0U7QUFDN0I7QUFDUDtBQUNBO0FBQ0EsWUFBWSwrQ0FBTTtBQUNsQjtBQUNBO0FBQ0E7QUFDQSxZQUFZLCtDQUFNO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBLFlBQVksaURBQU87QUFDbkI7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mdXR1cmlzdGljLXBvcnRmb2xpby8uL25vZGVfbW9kdWxlcy90c3BhcnRpY2xlcy1pbnRlcmFjdGlvbi1wYXJ0aWNsZXMtY29sbGlzaW9ucy9lc20vUmVzb2x2ZUNvbGxpc2lvbi5qcz9kZGI1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGFic29yYiB9IGZyb20gXCIuL0Fic29yYlwiO1xuaW1wb3J0IHsgYm91bmNlIH0gZnJvbSBcIi4vQm91bmNlXCI7XG5pbXBvcnQgeyBkZXN0cm95IH0gZnJvbSBcIi4vRGVzdHJveVwiO1xuZXhwb3J0IGZ1bmN0aW9uIHJlc29sdmVDb2xsaXNpb24ocDEsIHAyLCBkZWx0YSwgcGl4ZWxSYXRpbykge1xuICAgIHN3aXRjaCAocDEub3B0aW9ucy5jb2xsaXNpb25zLm1vZGUpIHtcbiAgICAgICAgY2FzZSBcImFic29yYlwiOiB7XG4gICAgICAgICAgICBhYnNvcmIocDEsIHAyLCBkZWx0YSwgcGl4ZWxSYXRpbyk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgICBjYXNlIFwiYm91bmNlXCI6IHtcbiAgICAgICAgICAgIGJvdW5jZShwMSwgcDIpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgICAgY2FzZSBcImRlc3Ryb3lcIjoge1xuICAgICAgICAgICAgZGVzdHJveShwMSwgcDIpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-particles-collisions/esm/ResolveCollision.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-particles-collisions/esm/index.js":
/*!********************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-particles-collisions/esm/index.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadParticlesCollisionsInteraction: () => (/* binding */ loadParticlesCollisionsInteraction)\n/* harmony export */ });\n/* harmony import */ var _Collider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Collider */ \"(ssr)/./node_modules/tsparticles-interaction-particles-collisions/esm/Collider.js\");\n\nasync function loadParticlesCollisionsInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"particlesCollisions\", (container) => new _Collider__WEBPACK_IMPORTED_MODULE_0__.Collider(container), refresh);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tcGFydGljbGVzLWNvbGxpc2lvbnMvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNDO0FBQy9CO0FBQ1AseUVBQXlFLCtDQUFRO0FBQ2pGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnV0dXJpc3RpYy1wb3J0Zm9saW8vLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tcGFydGljbGVzLWNvbGxpc2lvbnMvZXNtL2luZGV4LmpzPzNmZTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ29sbGlkZXIgfSBmcm9tIFwiLi9Db2xsaWRlclwiO1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGxvYWRQYXJ0aWNsZXNDb2xsaXNpb25zSW50ZXJhY3Rpb24oZW5naW5lLCByZWZyZXNoID0gdHJ1ZSkge1xuICAgIGF3YWl0IGVuZ2luZS5hZGRJbnRlcmFjdG9yKFwicGFydGljbGVzQ29sbGlzaW9uc1wiLCAoY29udGFpbmVyKSA9PiBuZXcgQ29sbGlkZXIoY29udGFpbmVyKSwgcmVmcmVzaCk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-particles-collisions/esm/index.js\n");

/***/ })

};
;