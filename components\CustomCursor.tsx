'use client'

import { useEffect, useState, useRef } from 'react'

export default function CustomCursor() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [isHovering, setIsHovering] = useState(false)
  const cursorRef = useRef<HTMLDivElement>(null)
  const trailRef = useRef<HTMLDivElement>(null)
  const requestRef = useRef<number>()

  useEffect(() => {
    let mouseX = 0
    let mouseY = 0
    let cursorX = 0
    let cursorY = 0
    let trailX = 0
    let trailY = 0

    const updateMousePosition = (e: MouseEvent) => {
      mouseX = e.clientX
      mouseY = e.clientY
    }

    const animateCursor = () => {
      // Smooth cursor movement with different speeds
      cursorX += (mouseX - cursorX) * 0.3
      cursorY += (mouseY - cursorY) * 0.3

      trailX += (mouseX - trailX) * 0.1
      trailY += (mouseY - trailY) * 0.1

      if (cursorRef.current) {
        cursorRef.current.style.transform = `translate3d(${cursorX - 8}px, ${cursorY - 8}px, 0) scale(${isHovering ? 1.5 : 1})`
      }

      if (trailRef.current) {
        trailRef.current.style.transform = `translate3d(${trailX - 16}px, ${trailY - 16}px, 0) scale(${isHovering ? 2 : 1})`
      }

      requestRef.current = requestAnimationFrame(animateCursor)
    }

    const handleMouseEnter = () => setIsHovering(true)
    const handleMouseLeave = () => setIsHovering(false)

    // Add event listeners for interactive elements
    const interactiveElements = document.querySelectorAll('a, button, [role="button"], input, textarea')

    interactiveElements.forEach(el => {
      el.addEventListener('mouseenter', handleMouseEnter)
      el.addEventListener('mouseleave', handleMouseLeave)
    })

    window.addEventListener('mousemove', updateMousePosition)
    requestRef.current = requestAnimationFrame(animateCursor)

    return () => {
      window.removeEventListener('mousemove', updateMousePosition)
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current)
      }
      interactiveElements.forEach(el => {
        el.removeEventListener('mouseenter', handleMouseEnter)
        el.removeEventListener('mouseleave', handleMouseLeave)
      })
    }
  }, [isHovering])

  return (
    <>
      {/* Main cursor */}
      <div
        ref={cursorRef}
        className="fixed top-0 left-0 w-4 h-4 bg-neon-blue rounded-full pointer-events-none z-50 mix-blend-difference transition-transform duration-200 ease-out"
        style={{ willChange: 'transform' }}
      />

      {/* Cursor trail */}
      <div
        ref={trailRef}
        className="fixed top-0 left-0 w-8 h-8 border-2 border-neon-blue rounded-full pointer-events-none z-40 transition-transform duration-300 ease-out"
        style={{ willChange: 'transform' }}
      />
    </>
  )
}
