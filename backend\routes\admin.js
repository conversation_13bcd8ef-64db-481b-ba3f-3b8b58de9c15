const express = require('express');
const Project = require('../models/Project');
const BlogPost = require('../models/BlogPost');
const Testimonial = require('../models/Testimonial');
const Contact = require('../models/Contact');
const Skill = require('../models/Skill');
const { auth, adminAuth } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/admin/dashboard
// @desc    Get admin dashboard statistics
// @access  Private/Admin
router.get('/dashboard', auth, adminAuth, async (req, res) => {
  try {
    // Get counts
    const [
      totalProjects,
      publishedProjects,
      totalBlogPosts,
      publishedBlogPosts,
      totalTestimonials,
      publishedTestimonials,
      totalContacts,
      unreadContacts,
      totalSkills
    ] = await Promise.all([
      Project.countDocuments(),
      Project.countDocuments({ isPublished: true }),
      BlogPost.countDocuments(),
      BlogPost.countDocuments({ isPublished: true }),
      Testimonial.countDocuments(),
      Testimonial.countDocuments({ isPublished: true }),
      Contact.countDocuments({ isSpam: false }),
      Contact.countDocuments({ status: 'new', isSpam: false }),
      Skill.countDocuments()
    ]);

    // Get recent activities
    const [recentProjects, recentBlogPosts, recentContacts] = await Promise.all([
      Project.find().sort({ createdAt: -1 }).limit(5).select('title createdAt isPublished'),
      BlogPost.find().sort({ createdAt: -1 }).limit(5).select('title createdAt isPublished'),
      Contact.find({ isSpam: false }).sort({ createdAt: -1 }).limit(5).select('name email subject status createdAt')
    ]);

    // Get monthly stats for the last 6 months
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyContacts = await Contact.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo },
          isSpam: false
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ]);

    // Get project views and blog post views
    const [projectStats, blogStats] = await Promise.all([
      Project.aggregate([
        { $match: { isPublished: true } },
        {
          $group: {
            _id: null,
            totalViews: { $sum: '$views' },
            totalLikes: { $sum: '$likes' }
          }
        }
      ]),
      BlogPost.aggregate([
        { $match: { isPublished: true } },
        {
          $group: {
            _id: null,
            totalViews: { $sum: '$views' },
            totalLikes: { $sum: '$likes' },
            totalShares: { $sum: '$shares' }
          }
        }
      ])
    ]);

    // Get contact statistics
    const contactStats = await Contact.getStats();

    res.json({
      success: true,
      data: {
        overview: {
          projects: { total: totalProjects, published: publishedProjects },
          blogPosts: { total: totalBlogPosts, published: publishedBlogPosts },
          testimonials: { total: totalTestimonials, published: publishedTestimonials },
          contacts: { total: totalContacts, unread: unreadContacts },
          skills: { total: totalSkills }
        },
        analytics: {
          projects: projectStats[0] || { totalViews: 0, totalLikes: 0 },
          blog: blogStats[0] || { totalViews: 0, totalLikes: 0, totalShares: 0 },
          contacts: contactStats,
          monthlyContacts
        },
        recentActivity: {
          projects: recentProjects,
          blogPosts: recentBlogPosts,
          contacts: recentContacts
        }
      }
    });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({ message: 'Server error fetching dashboard statistics' });
  }
});

// @route   GET /api/admin/analytics
// @desc    Get detailed analytics
// @access  Private/Admin
router.get('/analytics', auth, adminAuth, async (req, res) => {
  try {
    const { period = '30' } = req.query; // days
    const daysAgo = new Date();
    daysAgo.setDate(daysAgo.getDate() - parseInt(period));

    // Contact analytics
    const contactAnalytics = await Contact.aggregate([
      {
        $match: {
          createdAt: { $gte: daysAgo },
          isSpam: false
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
          },
          count: { $sum: 1 },
          projectTypes: { $push: '$projectType' },
          budgets: { $push: '$budget' }
        }
      },
      {
        $sort: { '_id': 1 }
      }
    ]);

    // Project analytics
    const projectAnalytics = await Project.aggregate([
      {
        $match: { isPublished: true }
      },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          totalViews: { $sum: '$views' },
          totalLikes: { $sum: '$likes' }
        }
      }
    ]);

    // Blog analytics
    const blogAnalytics = await BlogPost.aggregate([
      {
        $match: { isPublished: true }
      },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          totalViews: { $sum: '$views' },
          totalLikes: { $sum: '$likes' },
          totalShares: { $sum: '$shares' }
        }
      }
    ]);

    // Top performing content
    const [topProjects, topBlogPosts] = await Promise.all([
      Project.find({ isPublished: true })
        .sort({ views: -1 })
        .limit(10)
        .select('title views likes'),
      BlogPost.find({ isPublished: true })
        .sort({ views: -1 })
        .limit(10)
        .select('title views likes shares')
    ]);

    res.json({
      success: true,
      data: {
        period: parseInt(period),
        contacts: contactAnalytics,
        projects: projectAnalytics,
        blog: blogAnalytics,
        topContent: {
          projects: topProjects,
          blogPosts: topBlogPosts
        }
      }
    });
  } catch (error) {
    console.error('Analytics error:', error);
    res.status(500).json({ message: 'Server error fetching analytics' });
  }
});

// @route   GET /api/admin/system-info
// @desc    Get system information
// @access  Private/Admin
router.get('/system-info', auth, adminAuth, async (req, res) => {
  try {
    const systemInfo = {
      server: {
        nodeVersion: process.version,
        platform: process.platform,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        environment: process.env.NODE_ENV
      },
      database: {
        connected: require('mongoose').connection.readyState === 1,
        collections: {
          projects: await Project.countDocuments(),
          blogPosts: await BlogPost.countDocuments(),
          testimonials: await Testimonial.countDocuments(),
          contacts: await Contact.countDocuments(),
          skills: await Skill.countDocuments()
        }
      },
      lastUpdated: new Date().toISOString()
    };

    res.json({
      success: true,
      data: systemInfo
    });
  } catch (error) {
    console.error('System info error:', error);
    res.status(500).json({ message: 'Server error fetching system information' });
  }
});

// @route   POST /api/admin/backup
// @desc    Create database backup
// @access  Private/Admin
router.post('/backup', auth, adminAuth, async (req, res) => {
  try {
    // This is a simplified backup - in production, you'd want to use proper backup tools
    const [projects, blogPosts, testimonials, contacts, skills] = await Promise.all([
      Project.find(),
      BlogPost.find(),
      Testimonial.find(),
      Contact.find(),
      Skill.find()
    ]);

    const backup = {
      timestamp: new Date().toISOString(),
      data: {
        projects,
        blogPosts,
        testimonials,
        contacts,
        skills
      }
    };

    res.json({
      success: true,
      message: 'Backup created successfully',
      data: backup
    });
  } catch (error) {
    console.error('Backup error:', error);
    res.status(500).json({ message: 'Server error creating backup' });
  }
});

module.exports = router;
