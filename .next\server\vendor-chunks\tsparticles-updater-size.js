"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tsparticles-updater-size";
exports.ids = ["vendor-chunks/tsparticles-updater-size"];
exports.modules = {

/***/ "(ssr)/./node_modules/tsparticles-updater-size/esm/SizeUpdater.js":
/*!******************************************************************!*\
  !*** ./node_modules/tsparticles-updater-size/esm/SizeUpdater.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SizeUpdater: () => (/* binding */ SizeUpdater)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/tsparticles-updater-size/esm/Utils.js\");\n\n\nclass SizeUpdater {\n    init(particle) {\n        const container = particle.container, sizeOptions = particle.options.size, sizeAnimation = sizeOptions.animation;\n        if (sizeAnimation.enable) {\n            particle.size.velocity =\n                ((particle.retina.sizeAnimationSpeed ?? container.retina.sizeAnimationSpeed) / 100) *\n                    container.retina.reduceFactor;\n            if (!sizeAnimation.sync) {\n                particle.size.velocity *= (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)();\n            }\n        }\n    }\n    isEnabled(particle) {\n        return (!particle.destroyed &&\n            !particle.spawning &&\n            particle.size.enable &&\n            ((particle.size.maxLoops ?? 0) <= 0 ||\n                ((particle.size.maxLoops ?? 0) > 0 && (particle.size.loops ?? 0) < (particle.size.maxLoops ?? 0))));\n    }\n    reset(particle) {\n        particle.size.loops = 0;\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        (0,_Utils__WEBPACK_IMPORTED_MODULE_1__.updateSize)(particle, delta);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-size/esm/SizeUpdater.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-updater-size/esm/Utils.js":
/*!************************************************************!*\
  !*** ./node_modules/tsparticles-updater-size/esm/Utils.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   updateSize: () => (/* binding */ updateSize)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n\nfunction checkDestroy(particle, value, minValue, maxValue) {\n    switch (particle.options.size.animation.destroy) {\n        case \"max\":\n            if (value >= maxValue) {\n                particle.destroy();\n            }\n            break;\n        case \"min\":\n            if (value <= minValue) {\n                particle.destroy();\n            }\n            break;\n    }\n}\nfunction updateSize(particle, delta) {\n    const data = particle.size;\n    if (particle.destroyed ||\n        !data ||\n        !data.enable ||\n        ((data.maxLoops ?? 0) > 0 && (data.loops ?? 0) > (data.maxLoops ?? 0))) {\n        return;\n    }\n    const sizeVelocity = (data.velocity ?? 0) * delta.factor, minValue = data.min, maxValue = data.max, decay = data.decay ?? 1;\n    if (!data.time) {\n        data.time = 0;\n    }\n    if ((data.delayTime ?? 0) > 0 && data.time < (data.delayTime ?? 0)) {\n        data.time += delta.value;\n    }\n    if ((data.delayTime ?? 0) > 0 && data.time < (data.delayTime ?? 0)) {\n        return;\n    }\n    switch (data.status) {\n        case \"increasing\":\n            if (data.value >= maxValue) {\n                data.status = \"decreasing\";\n                if (!data.loops) {\n                    data.loops = 0;\n                }\n                data.loops++;\n            }\n            else {\n                data.value += sizeVelocity;\n            }\n            break;\n        case \"decreasing\":\n            if (data.value <= minValue) {\n                data.status = \"increasing\";\n                if (!data.loops) {\n                    data.loops = 0;\n                }\n                data.loops++;\n            }\n            else {\n                data.value -= sizeVelocity;\n            }\n    }\n    if (data.velocity && decay !== 1) {\n        data.velocity *= decay;\n    }\n    checkDestroy(particle, data.value, minValue, maxValue);\n    if (!particle.destroyed) {\n        data.value = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.clamp)(data.value, minValue, maxValue);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-size/esm/Utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-updater-size/esm/index.js":
/*!************************************************************!*\
  !*** ./node_modules/tsparticles-updater-size/esm/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadSizeUpdater: () => (/* binding */ loadSizeUpdater)\n/* harmony export */ });\n/* harmony import */ var _SizeUpdater__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SizeUpdater */ \"(ssr)/./node_modules/tsparticles-updater-size/esm/SizeUpdater.js\");\n\nasync function loadSizeUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"size\", () => new _SizeUpdater__WEBPACK_IMPORTED_MODULE_0__.SizeUpdater(), refresh);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtdXBkYXRlci1zaXplL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0QztBQUNyQztBQUNQLHNEQUFzRCxxREFBVztBQUNqRSIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1dHVyaXN0aWMtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL3RzcGFydGljbGVzLXVwZGF0ZXItc2l6ZS9lc20vaW5kZXguanM/NWZhNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTaXplVXBkYXRlciB9IGZyb20gXCIuL1NpemVVcGRhdGVyXCI7XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gbG9hZFNpemVVcGRhdGVyKGVuZ2luZSwgcmVmcmVzaCA9IHRydWUpIHtcbiAgICBhd2FpdCBlbmdpbmUuYWRkUGFydGljbGVVcGRhdGVyKFwic2l6ZVwiLCAoKSA9PiBuZXcgU2l6ZVVwZGF0ZXIoKSwgcmVmcmVzaCk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-size/esm/index.js\n");

/***/ })

};
;