@echo off
echo.
echo 🚀 Starting Complete Portfolio Development Environment
echo ===================================================
echo.

REM Check if all dependencies are installed
if not exist "node_modules" (
    echo ❌ Frontend dependencies not found. Running setup...
    call setup.bat
    if errorlevel 1 exit /b 1
)

if not exist "backend\node_modules" (
    echo ❌ Backend dependencies not found. Running setup...
    call setup.bat
    if errorlevel 1 exit /b 1
)

if not exist "admin\node_modules" (
    echo ❌ Admin dependencies not found. Running setup...
    call setup.bat
    if errorlevel 1 exit /b 1
)

echo ✅ All dependencies found!
echo.
echo 🌐 Starting all services...
echo.
echo 📝 This will open 3 terminal windows:
echo    1. 🔧 Backend API Server (Port 5000)
echo    2. 🎨 Frontend Portfolio (Port 3000) 
echo    3. 🛠️  Admin Panel (Port 3001)
echo.
echo 🔐 Admin Panel Login:
echo    URL: http://localhost:3001
echo    Email: <EMAIL>
echo    Password: legendhero
echo.

pause

REM Start Backend API Server
echo 🔧 Starting Backend API Server...
start "🔧 Backend API Server" cmd /k "cd backend && echo. && echo 🔧 BACKEND API SERVER && echo Port: 5000 && echo ==================== && echo. && npm run dev"

REM Wait for backend to start
echo ⏳ Waiting for backend to start...
timeout /t 5 /nobreak >nul

REM Start Frontend Portfolio
echo 🎨 Starting Frontend Portfolio...
start "🎨 Frontend Portfolio" cmd /k "echo. && echo 🎨 FRONTEND PORTFOLIO && echo Port: 3000 && echo =================== && echo. && npm run dev"

REM Wait for frontend to start
echo ⏳ Waiting for frontend to start...
timeout /t 3 /nobreak >nul

REM Start Admin Panel
echo 🛠️  Starting Admin Panel...
start "🛠️ Admin Panel" cmd /k "cd admin && echo. && echo 🛠️  ADMIN PANEL && echo Port: 3001 && echo ============ && echo. && npm run dev"

echo.
echo ✅ All services are starting!
echo.
echo 🌐 Your applications will be available at:
echo    📱 Frontend Portfolio: http://localhost:3000
echo    🛠️  Admin Panel:       http://localhost:3001  
echo    🔧 Backend API:        http://localhost:5000
echo.
echo 🔐 Admin Panel Access:
echo    Email:    <EMAIL>
echo    Password: legendhero
echo.
echo 💡 Tips:
echo    - Wait 1-2 minutes for all services to fully start
echo    - Check each terminal window for startup status
echo    - Use Ctrl+C in each terminal to stop services
echo    - Admin panel requires backend to be running
echo.
echo 🎯 Quick Links:
echo    - Portfolio:  http://localhost:3000
echo    - Admin:      http://localhost:3001
echo    - API Health: http://localhost:5000/api/health
echo.
echo ✨ Happy coding!
echo.
echo Press any key to close this launcher...
pause >nul
