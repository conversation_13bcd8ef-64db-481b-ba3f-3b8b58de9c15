{"version": 3, "sources": ["../../../src/client/components/use-reducer-with-devtools.ts"], "names": ["useReducerWithReduxDevtools", "useUnwrapState", "normalizeRouterState", "val", "Map", "obj", "key", "value", "entries", "$$typeof", "toString", "_bundlerConfig", "hasOwnProperty", "Array", "isArray", "map", "state", "isThenable", "result", "use", "useReducerWithReduxDevtoolsNoop", "initialState", "useReducerWithReduxDevtoolsImpl", "setState", "React", "useState", "actionQueue", "useContext", "ActionQueueContext", "Error", "devtoolsConnectionRef", "useRef", "enabledRef", "useEffect", "current", "undefined", "window", "__REDUX_DEVTOOLS_EXTENSION__", "connect", "instanceId", "name", "init", "devToolsInstance", "dispatch", "useCallback", "action", "sync", "resolvedState", "send", "type"], "mappings": ";;;;;;;;;;;;;;;IA2KaA,2BAA2B;eAA3BA;;IA7FGC,cAAc;eAAdA;;;;iEA7EuB;oCAOhC;6BAC4B;AAInC,SAASC,qBAAqBC,GAAQ;IACpC,IAAIA,eAAeC,KAAK;QACtB,MAAMC,MAA8B,CAAC;QACrC,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIJ,IAAIK,OAAO,GAAI;YACxC,IAAI,OAAOD,UAAU,YAAY;gBAC/BF,GAAG,CAACC,IAAI,GAAG;gBACX;YACF;YACA,IAAI,OAAOC,UAAU,YAAYA,UAAU,MAAM;gBAC/C,IAAIA,MAAME,QAAQ,EAAE;oBAClBJ,GAAG,CAACC,IAAI,GAAGC,MAAME,QAAQ,CAACC,QAAQ;oBAClC;gBACF;gBACA,IAAIH,MAAMI,cAAc,EAAE;oBACxBN,GAAG,CAACC,IAAI,GAAG;oBACX;gBACF;YACF;YACAD,GAAG,CAACC,IAAI,GAAGJ,qBAAqBK;QAClC;QACA,OAAOF;IACT;IAEA,IAAI,OAAOF,QAAQ,YAAYA,QAAQ,MAAM;QAC3C,MAAME,MAA8B,CAAC;QACrC,IAAK,MAAMC,OAAOH,IAAK;YACrB,MAAMI,QAAQJ,GAAG,CAACG,IAAI;YACtB,IAAI,OAAOC,UAAU,YAAY;gBAC/BF,GAAG,CAACC,IAAI,GAAG;gBACX;YACF;YACA,IAAI,OAAOC,UAAU,YAAYA,UAAU,MAAM;gBAC/C,IAAIA,MAAME,QAAQ,EAAE;oBAClBJ,GAAG,CAACC,IAAI,GAAGC,MAAME,QAAQ,CAACC,QAAQ;oBAClC;gBACF;gBACA,IAAIH,MAAMK,cAAc,CAAC,mBAAmB;oBAC1CP,GAAG,CAACC,IAAI,GAAG;oBACX;gBACF;YACF;YAEAD,GAAG,CAACC,IAAI,GAAGJ,qBAAqBK;QAClC;QACA,OAAOF;IACT;IAEA,IAAIQ,MAAMC,OAAO,CAACX,MAAM;QACtB,OAAOA,IAAIY,GAAG,CAACb;IACjB;IAEA,OAAOC;AACT;AAaO,SAASF,eAAee,KAAmB;IAChD,4FAA4F;IAC5F,IAAIC,IAAAA,8BAAU,EAACD,QAAQ;QACrB,MAAME,SAASC,IAAAA,UAAG,EAACH;QACnB,OAAOE;IACT;IAEA,OAAOF;AACT;AAEA,SAASI,gCACPC,YAA4B;IAE5B,OAAO;QAACA;QAAc,KAAO;QAAG,KAAO;KAAE;AAC3C;AAEA,SAASC,gCACPD,YAA4B;IAE5B,MAAM,CAACL,OAAOO,SAAS,GAAGC,cAAK,CAACC,QAAQ,CAAeJ;IAEvD,MAAMK,cAAcC,IAAAA,iBAAU,EAACC,+BAAkB;IAEjD,IAAI,CAACF,aAAa;QAChB,MAAM,IAAIG,MAAM;IAClB;IAEA,MAAMC,wBAAwBC,IAAAA,aAAM;IACpC,MAAMC,aAAaD,IAAAA,aAAM;IAEzBE,IAAAA,gBAAS,EAAC;QACR,IAAIH,sBAAsBI,OAAO,IAAIF,WAAWE,OAAO,KAAK,OAAO;YACjE;QACF;QAEA,IACEF,WAAWE,OAAO,KAAKC,aACvB,OAAOC,OAAOC,4BAA4B,KAAK,aAC/C;YACAL,WAAWE,OAAO,GAAG;YACrB;QACF;QAEAJ,sBAAsBI,OAAO,GAAGE,OAAOC,4BAA4B,CAACC,OAAO,CACzE;YACEC,YAAY;YACZC,MAAM;QACR;QAEF,IAAIV,sBAAsBI,OAAO,EAAE;YACjCJ,sBAAsBI,OAAO,CAACO,IAAI,CAACvC,qBAAqBmB;YAExD,IAAIK,aAAa;gBACfA,YAAYgB,gBAAgB,GAAGZ,sBAAsBI,OAAO;YAC9D;QACF;QAEA,OAAO;YACLJ,sBAAsBI,OAAO,GAAGC;QAClC;IACF,GAAG;QAACd;QAAcK;KAAY;IAE9B,MAAMiB,WAAWC,IAAAA,kBAAW,EAC1B,CAACC;QACC,IAAI,CAACnB,YAAYV,KAAK,EAAE;YACtB,0EAA0E;YAC1E,iFAAiF;YACjFU,YAAYV,KAAK,GAAGK;QACtB;QAEAK,YAAYiB,QAAQ,CAACE,QAAQtB;IAC/B,GACA;QAACG;QAAaL;KAAa;IAG7B,6DAA6D;IAC7D,oEAAoE;IACpE,mEAAmE;IACnE,qBAAqB;IACrB,mEAAmE;IACnE,8CAA8C;IAC9C,MAAMyB,OAAOF,IAAAA,kBAAW,EAAsB,CAACG;QAC7C,IAAIjB,sBAAsBI,OAAO,EAAE;YACjCJ,sBAAsBI,OAAO,CAACc,IAAI,CAChC;gBAAEC,MAAM;YAAc,GACtB/C,qBAAqB6C;QAEzB;IACF,GAAG,EAAE;IAEL,OAAO;QAAC/B;QAAO2B;QAAUG;KAAK;AAChC;AAEO,MAAM9C,8BACX,OAAOoC,WAAW,cACdd,kCACAF"}