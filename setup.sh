#!/bin/bash

echo "🚀 Setting up Futuristic Portfolio with Backend & Admin Panel"
echo "============================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check if MongoDB is installed
if ! command -v mongod &> /dev/null; then
    print_warning "MongoDB is not installed locally. You can use MongoDB Atlas instead."
fi

print_info "Installing dependencies for all components..."

# Install frontend dependencies
print_info "Installing frontend dependencies..."
npm install
if [ $? -eq 0 ]; then
    print_status "Frontend dependencies installed"
else
    print_error "Failed to install frontend dependencies"
    exit 1
fi

# Install backend dependencies
print_info "Installing backend dependencies..."
cd backend
npm install
if [ $? -eq 0 ]; then
    print_status "Backend dependencies installed"
else
    print_error "Failed to install backend dependencies"
    exit 1
fi

# Create backend .env file if it doesn't exist
if [ ! -f .env ]; then
    print_info "Creating backend .env file..."
    cat > .env << EOL
# Database
MONGODB_URI=mongodb://localhost:27017/portfolio

# JWT Secret (CHANGE THIS IN PRODUCTION!)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Admin Credentials (CHANGE THESE!)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123456

# Server
PORT=5000
NODE_ENV=development

# Email Configuration (Configure for contact form)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Cloudinary (Optional - for image uploads)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Frontend URL
FRONTEND_URL=http://localhost:3000
EOL
    print_status "Backend .env file created"
    print_warning "Please update the .env file with your actual credentials!"
else
    print_info "Backend .env file already exists"
fi

cd ..

# Install admin panel dependencies
print_info "Installing admin panel dependencies..."
cd admin
npm install
if [ $? -eq 0 ]; then
    print_status "Admin panel dependencies installed"
else
    print_error "Failed to install admin panel dependencies"
    exit 1
fi

# Create admin .env.local file if it doesn't exist
if [ ! -f .env.local ]; then
    print_info "Creating admin panel .env.local file..."
    cat > .env.local << EOL
NEXT_PUBLIC_API_URL=http://localhost:5000/api
EOL
    print_status "Admin panel .env.local file created"
else
    print_info "Admin panel .env.local file already exists"
fi

cd ..

print_status "Setup completed successfully!"
echo ""
echo "🎉 Your futuristic portfolio is ready!"
echo ""
echo "📋 Next Steps:"
echo "=============="
echo ""
echo "1. 🔧 Configure your environment:"
echo "   - Edit backend/.env with your MongoDB URI and credentials"
echo "   - Update admin email and password"
echo "   - Configure email settings for contact form"
echo ""
echo "2. 🗄️  Start MongoDB (if using local):"
echo "   mongod"
echo ""
echo "3. 🌱 Seed the database:"
echo "   cd backend && npm run seed"
echo ""
echo "4. 🚀 Start the services:"
echo ""
echo "   Terminal 1 - Backend API:"
echo "   cd backend && npm run dev"
echo "   (Runs on http://localhost:5000)"
echo ""
echo "   Terminal 2 - Frontend:"
echo "   npm run dev"
echo "   (Runs on http://localhost:3000)"
echo ""
echo "   Terminal 3 - Admin Panel:"
echo "   cd admin && npm run dev"
echo "   (Runs on http://localhost:3001)"
echo ""
echo "5. 🔐 Access Admin Panel:"
echo "   URL: http://localhost:3001"
echo "   Email: <EMAIL> (or your configured email)"
echo "   Password: admin123456 (or your configured password)"
echo ""
echo "⚠️  Security Reminders:"
echo "- Change default admin credentials immediately"
echo "- Use strong JWT secret in production"
echo "- Configure proper email settings"
echo "- Set up MongoDB Atlas for production"
echo ""
echo "📚 Documentation:"
echo "- Full setup guide in README.md"
echo "- API documentation in backend/routes/"
echo "- Admin panel features listed in README.md"
echo ""
print_status "Happy coding! 🎨✨"
