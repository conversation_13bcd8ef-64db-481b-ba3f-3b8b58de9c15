"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/Projects.tsx":
/*!*********************************!*\
  !*** ./components/Projects.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Projects; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-intersection-observer */ \"(app-pages-browser)/./node_modules/react-intersection-observer/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Play_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Play_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Play_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst projects = [\n    {\n        id: 1,\n        title: \"AI-Powered Chat Application\",\n        description: \"A real-time chat application with AI integration, built with React, Node.js, and OpenAI API. Features include smart responses, sentiment analysis, and modern UI.\",\n        image: \"/api/placeholder/600/400\",\n        tech: [\n            \"React\",\n            \"Node.js\",\n            \"OpenAI\",\n            \"Socket.io\",\n            \"MongoDB\"\n        ],\n        liveUrl: \"https://example.com\",\n        githubUrl: \"https://github.com/yourusername/ai-chat\",\n        featured: true\n    },\n    {\n        id: 2,\n        title: \"E-Commerce Platform\",\n        description: \"Full-stack e-commerce solution with payment integration, inventory management, and admin dashboard. Built with modern technologies.\",\n        image: \"/api/placeholder/600/400\",\n        tech: [\n            \"Next.js\",\n            \"PostgreSQL\",\n            \"Stripe\",\n            \"Tailwind CSS\"\n        ],\n        liveUrl: \"https://example.com\",\n        githubUrl: \"https://github.com/yourusername/ecommerce\",\n        featured: true\n    },\n    {\n        id: 3,\n        title: \"Machine Learning Dashboard\",\n        description: \"Interactive dashboard for visualizing ML model performance with real-time data processing and beautiful charts.\",\n        image: \"/api/placeholder/600/400\",\n        tech: [\n            \"Python\",\n            \"FastAPI\",\n            \"React\",\n            \"D3.js\",\n            \"TensorFlow\"\n        ],\n        liveUrl: \"https://example.com\",\n        githubUrl: \"https://github.com/yourusername/ml-dashboard\",\n        featured: false\n    },\n    {\n        id: 4,\n        title: \"Blockchain Voting System\",\n        description: \"Secure and transparent voting system built on blockchain technology with smart contracts and modern web interface.\",\n        image: \"/api/placeholder/600/400\",\n        tech: [\n            \"Solidity\",\n            \"Web3.js\",\n            \"React\",\n            \"Ethereum\",\n            \"IPFS\"\n        ],\n        liveUrl: \"https://example.com\",\n        githubUrl: \"https://github.com/yourusername/blockchain-voting\",\n        featured: false\n    },\n    {\n        id: 5,\n        title: \"IoT Home Automation\",\n        description: \"Smart home automation system with mobile app control, sensor integration, and AI-powered energy optimization.\",\n        image: \"/api/placeholder/600/400\",\n        tech: [\n            \"React Native\",\n            \"Python\",\n            \"Raspberry Pi\",\n            \"MQTT\",\n            \"TensorFlow\"\n        ],\n        liveUrl: \"https://example.com\",\n        githubUrl: \"https://github.com/yourusername/iot-home\",\n        featured: false\n    },\n    {\n        id: 6,\n        title: \"Social Media Analytics\",\n        description: \"Comprehensive social media analytics platform with sentiment analysis, trend detection, and automated reporting.\",\n        image: \"/api/placeholder/600/400\",\n        tech: [\n            \"Django\",\n            \"React\",\n            \"PostgreSQL\",\n            \"Celery\",\n            \"NLP\"\n        ],\n        liveUrl: \"https://example.com\",\n        githubUrl: \"https://github.com/yourusername/social-analytics\",\n        featured: false\n    }\n];\nconst techColors = {\n    \"React\": \"bg-blue-500\",\n    \"Node.js\": \"bg-green-500\",\n    \"Python\": \"bg-yellow-500\",\n    \"Next.js\": \"bg-gray-800\",\n    \"FastAPI\": \"bg-teal-500\",\n    \"Django\": \"bg-green-700\",\n    \"PostgreSQL\": \"bg-blue-700\",\n    \"MongoDB\": \"bg-green-600\",\n    \"TensorFlow\": \"bg-orange-500\",\n    \"OpenAI\": \"bg-purple-500\",\n    \"Solidity\": \"bg-gray-600\",\n    \"Web3.js\": \"bg-orange-600\"\n};\nfunction Projects() {\n    _s();\n    const [ref, inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_2__.useInView)({\n        triggerOnce: true,\n        threshold: 0.1\n    });\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [hoveredProject, setHoveredProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const filteredProjects = filter === \"all\" ? projects : filter === \"featured\" ? projects.filter((p)=>p.featured) : projects.filter((p)=>!p.featured);\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        ref: ref,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                variants: containerVariants,\n                initial: \"hidden\",\n                animate: inView ? \"visible\" : \"hidden\",\n                className: \"text-center mb-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h2, {\n                        variants: itemVariants,\n                        className: \"text-4xl md:text-6xl font-cyber font-bold text-neon-blue neon-glow-subtle mb-6\",\n                        children: \"My Projects\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        variants: itemVariants,\n                        className: \"w-24 h-1 bg-gradient-to-r from-neon-blue to-neon-green mx-auto mb-8\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                        variants: itemVariants,\n                        className: \"text-xl text-gray-400 max-w-3xl mx-auto\",\n                        children: \"Here are some of my recent projects that showcase my skills and passion for creating innovative solutions.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                variants: itemVariants,\n                initial: \"hidden\",\n                animate: inView ? \"visible\" : \"hidden\",\n                className: \"flex justify-center mb-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-4 bg-gray-900/50 backdrop-blur-sm rounded-lg p-2 border border-gray-700\",\n                    children: [\n                        \"all\",\n                        \"featured\",\n                        \"other\"\n                    ].map((filterType)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setFilter(filterType),\n                            className: \"px-6 py-2 rounded-md font-tech font-medium transition-all duration-300 \".concat(filter === filterType ? \"bg-neon-blue text-black\" : \"text-gray-400 hover:text-neon-blue\"),\n                            children: filterType.charAt(0).toUpperCase() + filterType.slice(1)\n                        }, filterType, false, {\n                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                variants: containerVariants,\n                initial: \"hidden\",\n                animate: inView ? \"visible\" : \"hidden\",\n                className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                children: filteredProjects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        variants: itemVariants,\n                        className: \"group relative bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg overflow-hidden hover:border-neon-blue/50 transition-all duration-300\",\n                        onMouseEnter: ()=>setHoveredProject(project.id),\n                        onMouseLeave: ()=>setHoveredProject(null),\n                        whileHover: {\n                            y: -10\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-48 bg-gradient-to-br from-neon-blue/20 to-neon-green/20 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-black/50 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Play_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"text-neon-blue\",\n                                            size: 48\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this),\n                                    project.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-4 left-4 bg-neon-blue text-black px-3 py-1 rounded-full text-sm font-tech font-semibold\",\n                                        children: \"Featured\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        className: \"absolute inset-0 bg-neon-blue/20 flex items-center justify-center space-x-4\",\n                                        initial: {\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            opacity: hoveredProject === project.id ? 1 : 0\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.a, {\n                                                href: project.liveUrl,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"p-3 bg-neon-blue text-black rounded-full hover:bg-white transition-colors\",\n                                                whileHover: {\n                                                    scale: 1.1\n                                                },\n                                                whileTap: {\n                                                    scale: 0.9\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Play_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.a, {\n                                                href: project.githubUrl,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"p-3 bg-gray-800 text-white rounded-full hover:bg-gray-700 transition-colors\",\n                                                whileHover: {\n                                                    scale: 1.1\n                                                },\n                                                whileTap: {\n                                                    scale: 0.9\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Play_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-tech font-semibold text-white mb-3 group-hover:text-neon-blue transition-colors\",\n                                        children: project.title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4 line-clamp-3\",\n                                        children: project.description\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mb-4\",\n                                        children: project.tech.map((tech)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-3 py-1 text-xs font-tech font-medium rounded-full text-white \".concat(techColors[tech] || \"bg-gray-600\"),\n                                                children: tech\n                                            }, tech, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: project.liveUrl,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"flex-1 bg-neon-blue text-black py-2 px-4 rounded-md font-tech font-medium text-center hover:bg-white transition-colors\",\n                                                children: \"Live Demo\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: project.githubUrl,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"flex-1 border border-gray-600 text-gray-300 py-2 px-4 rounded-md font-tech font-medium text-center hover:border-neon-blue hover:text-neon-blue transition-colors\",\n                                                children: \"Source Code\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, project.id, true, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\components\\\\Projects.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\n_s(Projects, \"N9osBawMAoUhsVip5qg1igWicU4=\", false, function() {\n    return [\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_2__.useInView\n    ];\n});\n_c = Projects;\nvar _c;\n$RefreshReg$(_c, \"Projects\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Projects.tsx\n"));

/***/ })

});