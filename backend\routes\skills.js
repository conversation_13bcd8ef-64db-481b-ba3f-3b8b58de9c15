const express = require('express');
const { body, validationResult } = require('express-validator');
const Skill = require('../models/Skill');
const { auth, adminAuth, logAdminAction } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/skills
// @desc    Get all visible skills
// @access  Public
router.get('/', async (req, res) => {
  try {
    const { category, grouped } = req.query;
    
    if (grouped === 'true') {
      const skillsGrouped = await Skill.getGroupedByCategory();
      return res.json({
        success: true,
        data: skillsGrouped
      });
    }
    
    let query = { isVisible: true };
    if (category) query.category = category;
    
    const skills = await Skill.find(query)
      .sort({ category: 1, order: 1, level: -1 })
      .populate('projects', 'title slug');
    
    res.json({
      success: true,
      count: skills.length,
      data: skills
    });
  } catch (error) {
    console.error('Get skills error:', error);
    res.status(500).json({ message: 'Server error fetching skills' });
  }
});

// @route   GET /api/skills/top
// @desc    Get top skills
// @access  Public
router.get('/top', async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const skills = await Skill.getTopSkills(parseInt(limit));
    
    res.json({
      success: true,
      count: skills.length,
      data: skills
    });
  } catch (error) {
    console.error('Get top skills error:', error);
    res.status(500).json({ message: 'Server error fetching top skills' });
  }
});

// Admin routes below require authentication

// @route   GET /api/skills/admin/all
// @desc    Get all skills for admin
// @access  Private/Admin
router.get('/admin/all', auth, adminAuth, async (req, res) => {
  try {
    const skills = await Skill.find()
      .sort({ category: 1, order: 1 })
      .populate('projects', 'title');
    
    res.json({
      success: true,
      count: skills.length,
      data: skills
    });
  } catch (error) {
    console.error('Get all skills error:', error);
    res.status(500).json({ message: 'Server error fetching skills' });
  }
});

// @route   POST /api/skills
// @desc    Create new skill
// @access  Private/Admin
router.post('/',
  auth,
  adminAuth,
  logAdminAction('CREATE_SKILL'),
  [
    body('name').notEmpty().trim(),
    body('level').isInt({ min: 0, max: 100 }),
    body('category').isIn(['frontend', 'backend', 'devops', 'ai-data', 'mobile', 'other'])
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ 
          message: 'Invalid input',
          errors: errors.array() 
        });
      }

      const skill = new Skill(req.body);
      await skill.save();

      res.status(201).json({
        success: true,
        message: 'Skill created successfully',
        data: skill
      });
    } catch (error) {
      console.error('Create skill error:', error);
      res.status(500).json({ message: 'Server error creating skill' });
    }
  }
);

// @route   PUT /api/skills/:id
// @desc    Update skill
// @access  Private/Admin
router.put('/:id',
  auth,
  adminAuth,
  logAdminAction('UPDATE_SKILL'),
  async (req, res) => {
    try {
      const skill = await Skill.findByIdAndUpdate(
        req.params.id,
        req.body,
        { new: true, runValidators: true }
      );

      if (!skill) {
        return res.status(404).json({ message: 'Skill not found' });
      }

      res.json({
        success: true,
        message: 'Skill updated successfully',
        data: skill
      });
    } catch (error) {
      console.error('Update skill error:', error);
      res.status(500).json({ message: 'Server error updating skill' });
    }
  }
);

// @route   DELETE /api/skills/:id
// @desc    Delete skill
// @access  Private/Admin
router.delete('/:id',
  auth,
  adminAuth,
  logAdminAction('DELETE_SKILL'),
  async (req, res) => {
    try {
      const skill = await Skill.findByIdAndDelete(req.params.id);

      if (!skill) {
        return res.status(404).json({ message: 'Skill not found' });
      }

      res.json({
        success: true,
        message: 'Skill deleted successfully'
      });
    } catch (error) {
      console.error('Delete skill error:', error);
      res.status(500).json({ message: 'Server error deleting skill' });
    }
  }
);

// @route   PATCH /api/skills/:id/toggle-visible
// @desc    Toggle skill visibility
// @access  Private/Admin
router.patch('/:id/toggle-visible',
  auth,
  adminAuth,
  logAdminAction('TOGGLE_SKILL_VISIBLE'),
  async (req, res) => {
    try {
      const skill = await Skill.findById(req.params.id);

      if (!skill) {
        return res.status(404).json({ message: 'Skill not found' });
      }

      skill.isVisible = !skill.isVisible;
      await skill.save();

      res.json({
        success: true,
        message: `Skill ${skill.isVisible ? 'shown' : 'hidden'} successfully`,
        data: skill
      });
    } catch (error) {
      console.error('Toggle visible error:', error);
      res.status(500).json({ message: 'Server error toggling visibility' });
    }
  }
);

// @route   PATCH /api/skills/reorder
// @desc    Reorder skills
// @access  Private/Admin
router.patch('/reorder',
  auth,
  adminAuth,
  logAdminAction('REORDER_SKILLS'),
  async (req, res) => {
    try {
      const { skills } = req.body; // Array of { id, order }
      
      const updatePromises = skills.map(({ id, order }) =>
        Skill.findByIdAndUpdate(id, { order })
      );
      
      await Promise.all(updatePromises);

      res.json({
        success: true,
        message: 'Skills reordered successfully'
      });
    } catch (error) {
      console.error('Reorder skills error:', error);
      res.status(500).json({ message: 'Server error reordering skills' });
    }
  }
);

module.exports = router;
