"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tsparticles-updater-opacity";
exports.ids = ["vendor-chunks/tsparticles-updater-opacity"];
exports.modules = {

/***/ "(ssr)/./node_modules/tsparticles-updater-opacity/esm/OpacityUpdater.js":
/*!************************************************************************!*\
  !*** ./node_modules/tsparticles-updater-opacity/esm/OpacityUpdater.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OpacityUpdater: () => (/* binding */ OpacityUpdater)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/Utils.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/tsparticles-updater-opacity/esm/Utils.js\");\n\n\nclass OpacityUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const opacityOptions = particle.options.opacity;\n        particle.opacity = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.initParticleNumericAnimationValue)(opacityOptions, 1);\n        const opacityAnimation = opacityOptions.animation;\n        if (opacityAnimation.enable) {\n            particle.opacity.velocity =\n                ((0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getRangeValue)(opacityAnimation.speed) / 100) * this.container.retina.reduceFactor;\n            if (!opacityAnimation.sync) {\n                particle.opacity.velocity *= (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getRandom)();\n            }\n        }\n    }\n    isEnabled(particle) {\n        return (!particle.destroyed &&\n            !particle.spawning &&\n            !!particle.opacity &&\n            particle.opacity.enable &&\n            ((particle.opacity.maxLoops ?? 0) <= 0 ||\n                ((particle.opacity.maxLoops ?? 0) > 0 &&\n                    (particle.opacity.loops ?? 0) < (particle.opacity.maxLoops ?? 0))));\n    }\n    reset(particle) {\n        if (particle.opacity) {\n            particle.opacity.time = 0;\n            particle.opacity.loops = 0;\n        }\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        (0,_Utils__WEBPACK_IMPORTED_MODULE_2__.updateOpacity)(particle, delta);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-opacity/esm/OpacityUpdater.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-updater-opacity/esm/Utils.js":
/*!***************************************************************!*\
  !*** ./node_modules/tsparticles-updater-opacity/esm/Utils.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   updateOpacity: () => (/* binding */ updateOpacity)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n\nfunction checkDestroy(particle, value, minValue, maxValue) {\n    switch (particle.options.opacity.animation.destroy) {\n        case \"max\":\n            if (value >= maxValue) {\n                particle.destroy();\n            }\n            break;\n        case \"min\":\n            if (value <= minValue) {\n                particle.destroy();\n            }\n            break;\n    }\n}\nfunction updateOpacity(particle, delta) {\n    const data = particle.opacity;\n    if (particle.destroyed || !data?.enable || ((data.maxLoops ?? 0) > 0 && (data.loops ?? 0) > (data.maxLoops ?? 0))) {\n        return;\n    }\n    const minValue = data.min, maxValue = data.max, decay = data.decay ?? 1;\n    if (!data.time) {\n        data.time = 0;\n    }\n    if ((data.delayTime ?? 0) > 0 && data.time < (data.delayTime ?? 0)) {\n        data.time += delta.value;\n    }\n    if ((data.delayTime ?? 0) > 0 && data.time < (data.delayTime ?? 0)) {\n        return;\n    }\n    switch (data.status) {\n        case \"increasing\":\n            if (data.value >= maxValue) {\n                data.status = \"decreasing\";\n                if (!data.loops) {\n                    data.loops = 0;\n                }\n                data.loops++;\n            }\n            else {\n                data.value += (data.velocity ?? 0) * delta.factor;\n            }\n            break;\n        case \"decreasing\":\n            if (data.value <= minValue) {\n                data.status = \"increasing\";\n                if (!data.loops) {\n                    data.loops = 0;\n                }\n                data.loops++;\n            }\n            else {\n                data.value -= (data.velocity ?? 0) * delta.factor;\n            }\n            break;\n    }\n    if (data.velocity && data.decay !== 1) {\n        data.velocity *= decay;\n    }\n    checkDestroy(particle, data.value, minValue, maxValue);\n    if (!particle.destroyed) {\n        data.value = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.clamp)(data.value, minValue, maxValue);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-opacity/esm/Utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-updater-opacity/esm/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/tsparticles-updater-opacity/esm/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadOpacityUpdater: () => (/* binding */ loadOpacityUpdater)\n/* harmony export */ });\n/* harmony import */ var _OpacityUpdater__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./OpacityUpdater */ \"(ssr)/./node_modules/tsparticles-updater-opacity/esm/OpacityUpdater.js\");\n\nasync function loadOpacityUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"opacity\", (container) => new _OpacityUpdater__WEBPACK_IMPORTED_MODULE_0__.OpacityUpdater(container), refresh);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtdXBkYXRlci1vcGFjaXR5L2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrRDtBQUMzQztBQUNQLGtFQUFrRSwyREFBYztBQUNoRiIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1dHVyaXN0aWMtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL3RzcGFydGljbGVzLXVwZGF0ZXItb3BhY2l0eS9lc20vaW5kZXguanM/Njg5MSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBPcGFjaXR5VXBkYXRlciB9IGZyb20gXCIuL09wYWNpdHlVcGRhdGVyXCI7XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gbG9hZE9wYWNpdHlVcGRhdGVyKGVuZ2luZSwgcmVmcmVzaCA9IHRydWUpIHtcbiAgICBhd2FpdCBlbmdpbmUuYWRkUGFydGljbGVVcGRhdGVyKFwib3BhY2l0eVwiLCAoY29udGFpbmVyKSA9PiBuZXcgT3BhY2l0eVVwZGF0ZXIoY29udGFpbmVyKSwgcmVmcmVzaCk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-opacity/esm/index.js\n");

/***/ })

};
;