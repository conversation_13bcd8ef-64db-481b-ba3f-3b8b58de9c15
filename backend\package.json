{"name": "portfolio-backend", "version": "1.0.0", "description": "Backend API for futuristic portfolio with admin panel", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node scripts/seedData.js"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "cloudinary": "^1.41.0", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "express-validator": "^7.0.1", "nodemailer": "^6.9.7", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["portfolio", "backend", "admin", "api", "mongodb"], "author": "Your Name", "license": "MIT"}