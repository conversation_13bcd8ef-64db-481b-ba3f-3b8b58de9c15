"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tsparticles-updater-color";
exports.ids = ["vendor-chunks/tsparticles-updater-color"];
exports.modules = {

/***/ "(ssr)/./node_modules/tsparticles-updater-color/esm/ColorUpdater.js":
/*!********************************************************************!*\
  !*** ./node_modules/tsparticles-updater-color/esm/ColorUpdater.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColorUpdater: () => (/* binding */ ColorUpdater)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/ColorUtils.js\");\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/tsparticles-updater-color/esm/Utils.js\");\n\n\nclass ColorUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const hslColor = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.rangeColorToHsl)(particle.options.color, particle.id, particle.options.reduceDuplicates);\n        if (hslColor) {\n            particle.color = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getHslAnimationFromHsl)(hslColor, particle.options.color.animation, this.container.retina.reduceFactor);\n        }\n    }\n    isEnabled(particle) {\n        const { h: hAnimation, s: sAnimation, l: lAnimation } = particle.options.color.animation, { color } = particle;\n        return (!particle.destroyed &&\n            !particle.spawning &&\n            ((color?.h.value !== undefined && hAnimation.enable) ||\n                (color?.s.value !== undefined && sAnimation.enable) ||\n                (color?.l.value !== undefined && lAnimation.enable)));\n    }\n    update(particle, delta) {\n        (0,_Utils__WEBPACK_IMPORTED_MODULE_1__.updateColor)(particle, delta);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-color/esm/ColorUpdater.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-updater-color/esm/Utils.js":
/*!*************************************************************!*\
  !*** ./node_modules/tsparticles-updater-color/esm/Utils.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   updateColor: () => (/* binding */ updateColor)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n\nfunction updateColorValue(delta, colorValue, valueAnimation, max, decrease) {\n    if (!colorValue ||\n        !valueAnimation.enable ||\n        ((colorValue.maxLoops ?? 0) > 0 && (colorValue.loops ?? 0) > (colorValue.maxLoops ?? 0))) {\n        return;\n    }\n    if (!colorValue.time) {\n        colorValue.time = 0;\n    }\n    if ((colorValue.delayTime ?? 0) > 0 && colorValue.time < (colorValue.delayTime ?? 0)) {\n        colorValue.time += delta.value;\n    }\n    if ((colorValue.delayTime ?? 0) > 0 && colorValue.time < (colorValue.delayTime ?? 0)) {\n        return;\n    }\n    const offset = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.randomInRange)(valueAnimation.offset), velocity = (colorValue.velocity ?? 0) * delta.factor + offset * 3.6, decay = colorValue.decay ?? 1;\n    if (!decrease || colorValue.status === \"increasing\") {\n        colorValue.value += velocity;\n        if (colorValue.value > max) {\n            if (!colorValue.loops) {\n                colorValue.loops = 0;\n            }\n            colorValue.loops++;\n            if (decrease) {\n                colorValue.status = \"decreasing\";\n                colorValue.value -= colorValue.value % max;\n            }\n        }\n    }\n    else {\n        colorValue.value -= velocity;\n        if (colorValue.value < 0) {\n            if (!colorValue.loops) {\n                colorValue.loops = 0;\n            }\n            colorValue.loops++;\n            colorValue.status = \"increasing\";\n            colorValue.value += colorValue.value;\n        }\n    }\n    if (colorValue.velocity && decay !== 1) {\n        colorValue.velocity *= decay;\n    }\n    if (colorValue.value > max) {\n        colorValue.value %= max;\n    }\n}\nfunction updateColor(particle, delta) {\n    const { h: hAnimation, s: sAnimation, l: lAnimation } = particle.options.color.animation, { color } = particle;\n    if (!color) {\n        return;\n    }\n    const { h, s, l } = color;\n    if (h) {\n        updateColorValue(delta, h, hAnimation, 360, false);\n    }\n    if (s) {\n        updateColorValue(delta, s, sAnimation, 100, true);\n    }\n    if (l) {\n        updateColorValue(delta, l, lAnimation, 100, true);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-color/esm/Utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-updater-color/esm/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/tsparticles-updater-color/esm/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadColorUpdater: () => (/* binding */ loadColorUpdater)\n/* harmony export */ });\n/* harmony import */ var _ColorUpdater__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ColorUpdater */ \"(ssr)/./node_modules/tsparticles-updater-color/esm/ColorUpdater.js\");\n\nasync function loadColorUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"color\", (container) => new _ColorUpdater__WEBPACK_IMPORTED_MODULE_0__.ColorUpdater(container), refresh);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtdXBkYXRlci1jb2xvci9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEM7QUFDdkM7QUFDUCxnRUFBZ0UsdURBQVk7QUFDNUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mdXR1cmlzdGljLXBvcnRmb2xpby8uL25vZGVfbW9kdWxlcy90c3BhcnRpY2xlcy11cGRhdGVyLWNvbG9yL2VzbS9pbmRleC5qcz85MjdkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IENvbG9yVXBkYXRlciB9IGZyb20gXCIuL0NvbG9yVXBkYXRlclwiO1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGxvYWRDb2xvclVwZGF0ZXIoZW5naW5lLCByZWZyZXNoID0gdHJ1ZSkge1xuICAgIGF3YWl0IGVuZ2luZS5hZGRQYXJ0aWNsZVVwZGF0ZXIoXCJjb2xvclwiLCAoY29udGFpbmVyKSA9PiBuZXcgQ29sb3JVcGRhdGVyKGNvbnRhaW5lciksIHJlZnJlc2gpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-updater-color/esm/index.js\n");

/***/ })

};
;