"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navbar */ \"(app-pages-browser)/./components/Navbar.tsx\");\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Hero */ \"(app-pages-browser)/./components/Hero.tsx\");\n/* harmony import */ var _components_About__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/About */ \"(app-pages-browser)/./components/About.tsx\");\n/* harmony import */ var _components_Projects__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Projects */ \"(app-pages-browser)/./components/Projects.tsx\");\n/* harmony import */ var _components_Skills__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Skills */ \"(app-pages-browser)/./components/Skills.tsx\");\n/* harmony import */ var _components_Resume__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Resume */ \"(app-pages-browser)/./components/Resume.tsx\");\n/* harmony import */ var _components_Contact__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Contact */ \"(app-pages-browser)/./components/Contact.tsx\");\n/* harmony import */ var _components_Blog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/Blog */ \"(app-pages-browser)/./components/Blog.tsx\");\n/* harmony import */ var _components_Testimonials__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/Testimonials */ \"(app-pages-browser)/./components/Testimonials.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./components/Footer.tsx\");\n/* harmony import */ var _components_BackToTop__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/BackToTop */ \"(app-pages-browser)/./components/BackToTop.tsx\");\n/* harmony import */ var _components_ParticleBackground__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ParticleBackground */ \"(app-pages-browser)/./components/ParticleBackground.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate loading time\n        const timer = setTimeout(()=>{\n            setIsLoading(false);\n        }, 2000);\n        return ()=>clearTimeout(timer);\n    }, []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"loading-dots mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-cyber text-neon-blue neon-glow-subtle\",\n                        children: \"Initializing Portfolio...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"relative min-h-screen bg-black text-white overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ParticleBackground__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"home\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"about\",\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_About__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"projects\",\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Projects__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"skills\",\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Skills__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"resume\",\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Resume__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"blog\",\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Blog__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"testimonials\",\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Testimonials__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"contact\",\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Contact__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BackToTop__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\DEV-College\\\\portfolio_website\\\\app\\\\page.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"Yt82d/dvZsn5nYh5sqDQjv+rJ38=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});