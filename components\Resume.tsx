'use client'

import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { Download, Eye, Calendar, MapPin, Award, Briefcase, GraduationCap } from 'lucide-react'

const experiences = [
  {
    title: 'Full Stack Developer',
    company: 'Tech Startup Inc.',
    location: 'Remote',
    period: '2023 - Present',
    description: 'Leading development of AI-powered web applications using React, Python, and cloud technologies.',
    achievements: [
      'Built scalable web applications serving 10k+ users',
      'Implemented AI features that improved user engagement by 40%',
      'Mentored junior developers and established coding standards'
    ]
  },
  {
    title: 'Frontend Developer',
    company: 'Digital Agency',
    location: 'New York, NY',
    period: '2022 - 2023',
    description: 'Developed responsive web applications and collaborated with design teams to create exceptional user experiences.',
    achievements: [
      'Delivered 15+ client projects on time and within budget',
      'Improved website performance by 60% through optimization',
      'Introduced modern development practices and tools'
    ]
  },
  {
    title: 'Junior Developer',
    company: 'Software Solutions Ltd.',
    location: 'San Francisco, CA',
    period: '2021 - 2022',
    description: 'Started my professional journey building web applications and learning industry best practices.',
    achievements: [
      'Contributed to 5+ major projects',
      'Learned full-stack development fundamentals',
      'Received "Rising Star" award for exceptional performance'
    ]
  }
]

const education = [
  {
    degree: 'Self-Taught Developer',
    institution: 'Online Platforms & Open Source',
    period: '2020 - Present',
    description: 'Continuous learning through online courses, documentation, and hands-on projects.',
    courses: ['Full Stack Web Development', 'Machine Learning', 'Cloud Computing', 'DevOps']
  },
  {
    degree: 'Computer Science Fundamentals',
    institution: 'Various Online Platforms',
    period: '2020 - 2021',
    description: 'Completed comprehensive courses in computer science fundamentals and programming.',
    courses: ['Data Structures & Algorithms', 'Database Design', 'Software Engineering', 'System Design']
  }
]

const certifications = [
  { name: 'AWS Cloud Practitioner', issuer: 'Amazon Web Services', year: '2023' },
  { name: 'React Developer Certification', issuer: 'Meta', year: '2022' },
  { name: 'Python for Data Science', issuer: 'IBM', year: '2022' },
  { name: 'Full Stack Web Development', issuer: 'freeCodeCamp', year: '2021' },
]

export default function Resume() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  }

  const handleDownloadResume = () => {
    // In a real application, this would download the actual PDF
    const link = document.createElement('a')
    link.href = '/resume.pdf'
    link.download = 'YourName_Resume.pdf'
    link.click()
  }

  const handleViewResume = () => {
    // In a real application, this would open the PDF in a new tab
    window.open('/resume.pdf', '_blank')
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" ref={ref}>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        className="text-center mb-16"
      >
        <motion.h2
          variants={itemVariants}
          className="text-4xl md:text-6xl font-cyber font-bold text-neon-blue neon-glow-subtle mb-6"
        >
          Resume
        </motion.h2>
        <motion.div
          variants={itemVariants}
          className="w-24 h-1 bg-gradient-to-r from-neon-blue to-neon-green mx-auto mb-8"
        />
        <motion.p
          variants={itemVariants}
          className="text-xl text-gray-400 max-w-3xl mx-auto mb-8"
        >
          My professional journey, skills, and achievements in the world of technology.
        </motion.p>
        
        {/* Action Buttons */}
        <motion.div
          variants={itemVariants}
          className="flex flex-col sm:flex-row gap-4 justify-center"
        >
          <motion.button
            onClick={handleViewResume}
            className="btn-cyber group flex items-center gap-3"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Eye size={20} />
            View Resume
          </motion.button>
          <motion.button
            onClick={handleDownloadResume}
            className="btn-cyber group flex items-center gap-3 border-neon-green text-neon-green hover:text-black"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Download size={20} />
            Download PDF
          </motion.button>
        </motion.div>
      </motion.div>

      <div className="grid lg:grid-cols-3 gap-12">
        {/* Left Column - Experience */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="lg:col-span-2 space-y-8"
        >
          {/* Experience Section */}
          <motion.div variants={itemVariants}>
            <div className="flex items-center mb-8">
              <Briefcase className="text-neon-blue mr-3" size={24} />
              <h3 className="text-2xl font-tech font-semibold text-white">
                Professional Experience
              </h3>
            </div>
            
            <div className="space-y-8">
              {experiences.map((exp, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="relative bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg p-6 hover:border-neon-blue/50 transition-all duration-300"
                >
                  <div className="flex flex-col md:flex-row md:items-start md:justify-between mb-4">
                    <div>
                      <h4 className="text-xl font-tech font-semibold text-white mb-1">
                        {exp.title}
                      </h4>
                      <p className="text-neon-blue font-medium mb-2">{exp.company}</p>
                    </div>
                    <div className="flex flex-col md:items-end text-sm text-gray-400">
                      <div className="flex items-center mb-1">
                        <Calendar size={16} className="mr-1" />
                        {exp.period}
                      </div>
                      <div className="flex items-center">
                        <MapPin size={16} className="mr-1" />
                        {exp.location}
                      </div>
                    </div>
                  </div>
                  
                  <p className="text-gray-300 mb-4">{exp.description}</p>
                  
                  <ul className="space-y-2">
                    {exp.achievements.map((achievement, i) => (
                      <li key={i} className="flex items-start text-gray-400">
                        <span className="text-neon-green mr-2 mt-1">▸</span>
                        {achievement}
                      </li>
                    ))}
                  </ul>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Education Section */}
          <motion.div variants={itemVariants}>
            <div className="flex items-center mb-8">
              <GraduationCap className="text-neon-green mr-3" size={24} />
              <h3 className="text-2xl font-tech font-semibold text-white">
                Education & Learning
              </h3>
            </div>
            
            <div className="space-y-6">
              {education.map((edu, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg p-6 hover:border-neon-green/50 transition-all duration-300"
                >
                  <div className="flex flex-col md:flex-row md:items-start md:justify-between mb-4">
                    <div>
                      <h4 className="text-lg font-tech font-semibold text-white mb-1">
                        {edu.degree}
                      </h4>
                      <p className="text-neon-green font-medium mb-2">{edu.institution}</p>
                    </div>
                    <div className="flex items-center text-sm text-gray-400">
                      <Calendar size={16} className="mr-1" />
                      {edu.period}
                    </div>
                  </div>
                  
                  <p className="text-gray-300 mb-4">{edu.description}</p>
                  
                  <div className="flex flex-wrap gap-2">
                    {edu.courses.map((course, i) => (
                      <span
                        key={i}
                        className="px-3 py-1 bg-neon-green/20 text-neon-green text-sm rounded-full font-tech"
                      >
                        {course}
                      </span>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>

        {/* Right Column - Certifications & Skills Summary */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-8"
        >
          {/* Certifications */}
          <motion.div variants={itemVariants}>
            <div className="flex items-center mb-6">
              <Award className="text-neon-pink mr-3" size={24} />
              <h3 className="text-xl font-tech font-semibold text-white">
                Certifications
              </h3>
            </div>
            
            <div className="space-y-4">
              {certifications.map((cert, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg p-4 hover:border-neon-pink/50 transition-all duration-300"
                  whileHover={{ scale: 1.02 }}
                >
                  <h4 className="font-tech font-semibold text-white mb-1">
                    {cert.name}
                  </h4>
                  <p className="text-neon-pink text-sm mb-1">{cert.issuer}</p>
                  <p className="text-gray-400 text-sm">{cert.year}</p>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Quick Stats */}
          <motion.div variants={itemVariants}>
            <h3 className="text-xl font-tech font-semibold text-white mb-6">
              Quick Stats
            </h3>
            
            <div className="space-y-4">
              <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg p-4">
                <div className="text-2xl font-cyber font-bold text-neon-blue mb-1">3+</div>
                <div className="text-gray-400 text-sm">Years of Experience</div>
              </div>
              
              <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg p-4">
                <div className="text-2xl font-cyber font-bold text-neon-green mb-1">50+</div>
                <div className="text-gray-400 text-sm">Projects Completed</div>
              </div>
              
              <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg p-4">
                <div className="text-2xl font-cyber font-bold text-neon-pink mb-1">15+</div>
                <div className="text-gray-400 text-sm">Technologies Mastered</div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}
