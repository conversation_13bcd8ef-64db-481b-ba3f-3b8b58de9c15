'use client'

import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { useState, useEffect } from 'react'
import { ChevronLeft, ChevronRight, Star, Quote } from 'lucide-react'

const testimonials = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'Product Manager',
    company: 'TechCorp Inc.',
    image: '/api/placeholder/80/80',
    rating: 5,
    text: 'Working with this developer was an absolute pleasure. Their technical expertise and attention to detail resulted in a product that exceeded our expectations. The AI integration was seamless and the user experience is fantastic.',
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'CTO',
    company: 'StartupXYZ',
    image: '/api/placeholder/80/80',
    rating: 5,
    text: 'Exceptional work on our e-commerce platform. The performance optimizations and modern architecture have significantly improved our conversion rates. Highly recommend for any complex web development project.',
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'Founder',
    company: 'Digital Solutions',
    image: '/api/placeholder/80/80',
    rating: 5,
    text: 'The machine learning dashboard they built for us has transformed how we analyze our data. The visualizations are beautiful and the insights are actionable. A true professional who delivers quality work.',
  },
  {
    id: 4,
    name: '<PERSON>',
    role: 'Lead Developer',
    company: 'Innovation Labs',
    image: '/api/placeholder/80/80',
    rating: 5,
    text: 'Great collaboration on our blockchain project. Their understanding of both frontend and backend technologies, combined with their problem-solving skills, made them an invaluable team member.',
  },
  {
    id: 5,
    name: 'Lisa Wang',
    role: 'Marketing Director',
    company: 'Growth Agency',
    image: '/api/placeholder/80/80',
    rating: 5,
    text: 'The social media analytics platform they developed has been a game-changer for our agency. The automated reporting and sentiment analysis features have saved us countless hours while providing deeper insights.',
  },
]

export default function Testimonials() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
      )
    }, 5000)

    return () => clearInterval(interval)
  }, [isAutoPlaying])

  const goToPrevious = () => {
    setIsAutoPlaying(false)
    setCurrentIndex(currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1)
  }

  const goToNext = () => {
    setIsAutoPlaying(false)
    setCurrentIndex(currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1)
  }

  const goToSlide = (index: number) => {
    setIsAutoPlaying(false)
    setCurrentIndex(index)
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        size={16}
        className={`${
          index < rating ? 'text-yellow-400 fill-current' : 'text-gray-600'
        }`}
      />
    ))
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" ref={ref}>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        className="text-center mb-16"
      >
        <motion.h2
          variants={itemVariants}
          className="text-4xl md:text-6xl font-cyber font-bold text-neon-blue neon-glow-subtle mb-6"
        >
          Testimonials
        </motion.h2>
        <motion.div
          variants={itemVariants}
          className="w-24 h-1 bg-gradient-to-r from-neon-blue to-neon-green mx-auto mb-8"
        />
        <motion.p
          variants={itemVariants}
          className="text-xl text-gray-400 max-w-3xl mx-auto"
        >
          What clients and collaborators say about working with me.
        </motion.p>
      </motion.div>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        className="relative"
      >
        {/* Main Testimonial Display */}
        <motion.div
          variants={itemVariants}
          className="relative bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg p-8 md:p-12 mb-8 overflow-hidden"
        >
          {/* Background Quote Icon */}
          <div className="absolute top-4 right-4 opacity-10">
            <Quote size={80} className="text-neon-blue" />
          </div>

          <div className="relative z-10">
            {/* Stars */}
            <div className="flex justify-center mb-6">
              {renderStars(testimonials[currentIndex].rating)}
            </div>

            {/* Testimonial Text */}
            <motion.blockquote
              key={currentIndex}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
              className="text-lg md:text-xl text-gray-300 text-center leading-relaxed mb-8 max-w-4xl mx-auto"
            >
              "{testimonials[currentIndex].text}"
            </motion.blockquote>

            {/* Author Info */}
            <motion.div
              key={`author-${currentIndex}`}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="flex flex-col items-center"
            >
              <div className="w-16 h-16 bg-gradient-to-br from-neon-blue/30 to-neon-green/30 rounded-full flex items-center justify-center mb-4 border-2 border-neon-blue/50">
                <span className="text-2xl">👤</span>
              </div>
              <h4 className="text-xl font-tech font-semibold text-white mb-1">
                {testimonials[currentIndex].name}
              </h4>
              <p className="text-neon-blue font-medium mb-1">
                {testimonials[currentIndex].role}
              </p>
              <p className="text-gray-400 text-sm">
                {testimonials[currentIndex].company}
              </p>
            </motion.div>
          </div>
        </motion.div>

        {/* Navigation Controls */}
        <motion.div
          variants={itemVariants}
          className="flex items-center justify-center space-x-4 mb-8"
        >
          <motion.button
            onClick={goToPrevious}
            className="p-3 bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-full text-gray-400 hover:text-neon-blue hover:border-neon-blue/50 transition-all duration-300"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <ChevronLeft size={20} />
          </motion.button>

          {/* Dots Indicator */}
          <div className="flex space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? 'bg-neon-blue scale-125'
                    : 'bg-gray-600 hover:bg-gray-500'
                }`}
              />
            ))}
          </div>

          <motion.button
            onClick={goToNext}
            className="p-3 bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-full text-gray-400 hover:text-neon-blue hover:border-neon-blue/50 transition-all duration-300"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <ChevronRight size={20} />
          </motion.button>
        </motion.div>

        {/* Thumbnail Navigation */}
        <motion.div
          variants={itemVariants}
          className="flex justify-center space-x-4 overflow-x-auto pb-4"
        >
          {testimonials.map((testimonial, index) => (
            <motion.button
              key={testimonial.id}
              onClick={() => goToSlide(index)}
              className={`flex-shrink-0 p-3 rounded-lg border transition-all duration-300 ${
                index === currentIndex
                  ? 'border-neon-blue bg-neon-blue/10'
                  : 'border-gray-700 bg-gray-900/50 hover:border-gray-600'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <div className="text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-neon-blue/30 to-neon-green/30 rounded-full flex items-center justify-center mb-2 mx-auto border border-neon-blue/50">
                  <span className="text-sm">👤</span>
                </div>
                <p className="text-xs font-tech font-medium text-white">
                  {testimonial.name.split(' ')[0]}
                </p>
                <p className="text-xs text-gray-400">
                  {testimonial.company}
                </p>
              </div>
            </motion.button>
          ))}
        </motion.div>

        {/* Auto-play Toggle */}
        <motion.div
          variants={itemVariants}
          className="text-center mt-8"
        >
          <button
            onClick={() => setIsAutoPlaying(!isAutoPlaying)}
            className={`px-4 py-2 rounded-lg font-tech text-sm transition-all duration-300 ${
              isAutoPlaying
                ? 'bg-neon-blue/20 text-neon-blue border border-neon-blue/50'
                : 'bg-gray-900/50 text-gray-400 border border-gray-700 hover:border-gray-600'
            }`}
          >
            {isAutoPlaying ? 'Pause Auto-play' : 'Resume Auto-play'}
          </button>
        </motion.div>
      </motion.div>
    </div>
  )
}
