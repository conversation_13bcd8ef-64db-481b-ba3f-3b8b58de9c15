const express = require('express');
const { body, validationResult } = require('express-validator');
const nodemailer = require('nodemailer');
const Contact = require('../models/Contact');
const { auth, adminAuth, logAdminAction } = require('../middleware/auth');

const router = express.Router();

// Email transporter setup
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: false,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    }
  });
};

// @route   POST /api/contact
// @desc    Submit contact form
// @access  Public
router.post('/',
  [
    body('name').notEmpty().trim().isLength({ min: 2, max: 100 }),
    body('email').isEmail().normalizeEmail(),
    body('subject').notEmpty().trim().isLength({ min: 5, max: 200 }),
    body('message').notEmpty().trim().isLength({ min: 10, max: 2000 })
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ 
          message: 'Invalid input',
          errors: errors.array() 
        });
      }

      const { name, email, subject, message, phone, company, projectType, budget, timeline } = req.body;

      // Basic spam detection
      const spamKeywords = ['viagra', 'casino', 'lottery', 'winner', 'congratulations'];
      const isSpam = spamKeywords.some(keyword => 
        message.toLowerCase().includes(keyword) || 
        subject.toLowerCase().includes(keyword)
      );

      // Create contact entry
      const contact = new Contact({
        name,
        email,
        subject,
        message,
        phone,
        company,
        projectType,
        budget,
        timeline,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        isSpam
      });

      await contact.save();

      // Send email notification to admin (if not spam)
      if (!isSpam) {
        try {
          const transporter = createTransporter();
          
          const mailOptions = {
            from: process.env.EMAIL_USER,
            to: process.env.ADMIN_EMAIL,
            subject: `New Contact Form Submission: ${subject}`,
            html: `
              <h2>New Contact Form Submission</h2>
              <p><strong>Name:</strong> ${name}</p>
              <p><strong>Email:</strong> ${email}</p>
              <p><strong>Phone:</strong> ${phone || 'Not provided'}</p>
              <p><strong>Company:</strong> ${company || 'Not provided'}</p>
              <p><strong>Project Type:</strong> ${projectType}</p>
              <p><strong>Budget:</strong> ${budget}</p>
              <p><strong>Timeline:</strong> ${timeline}</p>
              <p><strong>Subject:</strong> ${subject}</p>
              <p><strong>Message:</strong></p>
              <p>${message.replace(/\n/g, '<br>')}</p>
              <hr>
              <p><small>Submitted at: ${new Date().toLocaleString()}</small></p>
              <p><small>IP Address: ${req.ip}</small></p>
            `
          };

          await transporter.sendMail(mailOptions);
        } catch (emailError) {
          console.error('Email sending error:', emailError);
          // Don't fail the request if email fails
        }
      }

      res.status(201).json({
        success: true,
        message: 'Message sent successfully! I\'ll get back to you soon.',
        id: contact._id
      });

    } catch (error) {
      console.error('Contact form error:', error);
      res.status(500).json({ message: 'Server error processing contact form' });
    }
  }
);

// Admin routes below require authentication

// @route   GET /api/contact/admin/all
// @desc    Get all contact messages for admin
// @access  Private/Admin
router.get('/admin/all', auth, adminAuth, async (req, res) => {
  try {
    const { status, priority, page = 1, limit = 20, includeSpam = false } = req.query;
    
    let query = {};
    if (!includeSpam) query.isSpam = false;
    if (status) query.status = status;
    if (priority) query.priority = priority;
    
    const skip = (page - 1) * limit;
    
    const contacts = await Contact.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));
    
    const total = await Contact.countDocuments(query);
    
    res.json({
      success: true,
      data: contacts,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('Get contacts error:', error);
    res.status(500).json({ message: 'Server error fetching contacts' });
  }
});

// @route   GET /api/contact/admin/stats
// @desc    Get contact statistics
// @access  Private/Admin
router.get('/admin/stats', auth, adminAuth, async (req, res) => {
  try {
    const stats = await Contact.getStats();
    const unreadCount = await Contact.countDocuments({ status: 'new', isSpam: false });
    const highPriorityCount = await Contact.countDocuments({ 
      priority: { $in: ['high', 'urgent'] },
      status: { $nin: ['completed', 'archived'] },
      isSpam: false 
    });
    
    res.json({
      success: true,
      data: {
        ...stats,
        unread: unreadCount,
        highPriority: highPriorityCount
      }
    });
  } catch (error) {
    console.error('Get contact stats error:', error);
    res.status(500).json({ message: 'Server error fetching contact statistics' });
  }
});

// @route   GET /api/contact/admin/:id
// @desc    Get single contact message
// @access  Private/Admin
router.get('/admin/:id', auth, adminAuth, async (req, res) => {
  try {
    const contact = await Contact.findById(req.params.id);
    
    if (!contact) {
      return res.status(404).json({ message: 'Contact message not found' });
    }
    
    // Mark as read if it's new
    if (contact.status === 'new') {
      await contact.markAsRead();
    }
    
    res.json({
      success: true,
      data: contact
    });
  } catch (error) {
    console.error('Get contact error:', error);
    res.status(500).json({ message: 'Server error fetching contact message' });
  }
});

// @route   PATCH /api/contact/admin/:id/status
// @desc    Update contact message status
// @access  Private/Admin
router.patch('/admin/:id/status',
  auth,
  adminAuth,
  logAdminAction('UPDATE_CONTACT_STATUS'),
  async (req, res) => {
    try {
      const { status, notes } = req.body;
      
      const contact = await Contact.findById(req.params.id);
      
      if (!contact) {
        return res.status(404).json({ message: 'Contact message not found' });
      }
      
      contact.status = status;
      if (notes) contact.notes = notes;
      
      if (status === 'replied') {
        contact.repliedAt = new Date();
      }
      
      await contact.save();
      
      res.json({
        success: true,
        message: 'Contact status updated successfully',
        data: contact
      });
    } catch (error) {
      console.error('Update contact status error:', error);
      res.status(500).json({ message: 'Server error updating contact status' });
    }
  }
);

// @route   PATCH /api/contact/admin/:id/priority
// @desc    Update contact message priority
// @access  Private/Admin
router.patch('/admin/:id/priority',
  auth,
  adminAuth,
  logAdminAction('UPDATE_CONTACT_PRIORITY'),
  async (req, res) => {
    try {
      const { priority } = req.body;
      
      const contact = await Contact.findByIdAndUpdate(
        req.params.id,
        { priority },
        { new: true }
      );
      
      if (!contact) {
        return res.status(404).json({ message: 'Contact message not found' });
      }
      
      res.json({
        success: true,
        message: 'Contact priority updated successfully',
        data: contact
      });
    } catch (error) {
      console.error('Update contact priority error:', error);
      res.status(500).json({ message: 'Server error updating contact priority' });
    }
  }
);

// @route   DELETE /api/contact/admin/:id
// @desc    Delete contact message
// @access  Private/Admin
router.delete('/admin/:id',
  auth,
  adminAuth,
  logAdminAction('DELETE_CONTACT'),
  async (req, res) => {
    try {
      const contact = await Contact.findByIdAndDelete(req.params.id);
      
      if (!contact) {
        return res.status(404).json({ message: 'Contact message not found' });
      }
      
      res.json({
        success: true,
        message: 'Contact message deleted successfully'
      });
    } catch (error) {
      console.error('Delete contact error:', error);
      res.status(500).json({ message: 'Server error deleting contact message' });
    }
  }
);

// @route   PATCH /api/contact/admin/:id/spam
// @desc    Mark contact as spam
// @access  Private/Admin
router.patch('/admin/:id/spam',
  auth,
  adminAuth,
  logAdminAction('MARK_CONTACT_SPAM'),
  async (req, res) => {
    try {
      const contact = await Contact.findById(req.params.id);
      
      if (!contact) {
        return res.status(404).json({ message: 'Contact message not found' });
      }
      
      await contact.markAsSpam();
      
      res.json({
        success: true,
        message: 'Contact marked as spam',
        data: contact
      });
    } catch (error) {
      console.error('Mark spam error:', error);
      res.status(500).json({ message: 'Server error marking contact as spam' });
    }
  }
);

module.exports = router;
