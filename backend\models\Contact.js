const mongoose = require('mongoose');

const contactSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  subject: {
    type: String,
    required: true,
    trim: true
  },
  message: {
    type: String,
    required: true
  },
  phone: {
    type: String,
    trim: true
  },
  company: {
    type: String,
    trim: true
  },
  projectType: {
    type: String,
    enum: ['web-development', 'ai-integration', 'consultation', 'collaboration', 'other'],
    default: 'other'
  },
  budget: {
    type: String,
    enum: ['under-5k', '5k-10k', '10k-25k', '25k-50k', 'over-50k', 'not-specified'],
    default: 'not-specified'
  },
  timeline: {
    type: String,
    enum: ['asap', '1-month', '2-3-months', '3-6-months', 'flexible'],
    default: 'flexible'
  },
  status: {
    type: String,
    enum: ['new', 'read', 'replied', 'in-progress', 'completed', 'archived'],
    default: 'new'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  source: {
    type: String,
    enum: ['website', 'linkedin', 'github', 'referral', 'other'],
    default: 'website'
  },
  ipAddress: {
    type: String
  },
  userAgent: {
    type: String
  },
  isSpam: {
    type: Boolean,
    default: false
  },
  notes: {
    type: String,
    default: ''
  },
  repliedAt: {
    type: Date
  },
  readAt: {
    type: Date
  }
}, {
  timestamps: true
});

// Index for better query performance
contactSchema.index({ status: 1, createdAt: -1 });
contactSchema.index({ priority: 1, status: 1 });
contactSchema.index({ email: 1 });
contactSchema.index({ isSpam: 1, createdAt: -1 });

// Virtual for time since submission
contactSchema.virtual('timeAgo').get(function() {
  const now = new Date();
  const diff = now - this.createdAt;
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
  if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  return 'Just now';
});

// Method to mark as read
contactSchema.methods.markAsRead = function() {
  if (this.status === 'new') {
    this.status = 'read';
    this.readAt = new Date();
  }
  return this.save();
};

// Method to mark as replied
contactSchema.methods.markAsReplied = function() {
  this.status = 'replied';
  this.repliedAt = new Date();
  return this.save();
};

// Method to mark as spam
contactSchema.methods.markAsSpam = function() {
  this.isSpam = true;
  this.status = 'archived';
  return this.save();
};

// Static method to get unread messages
contactSchema.statics.getUnread = function() {
  return this.find({ 
    status: 'new',
    isSpam: false 
  }).sort({ createdAt: -1 });
};

// Static method to get messages by status
contactSchema.statics.getByStatus = function(status) {
  return this.find({ 
    status: status,
    isSpam: false 
  }).sort({ createdAt: -1 });
};

// Static method to get high priority messages
contactSchema.statics.getHighPriority = function() {
  return this.find({ 
    priority: { $in: ['high', 'urgent'] },
    status: { $nin: ['completed', 'archived'] },
    isSpam: false 
  }).sort({ priority: 1, createdAt: -1 });
};

// Static method to get statistics
contactSchema.statics.getStats = async function() {
  const stats = await this.aggregate([
    {
      $match: { isSpam: false }
    },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]);
  
  const total = await this.countDocuments({ isSpam: false });
  const today = await this.countDocuments({
    isSpam: false,
    createdAt: {
      $gte: new Date(new Date().setHours(0, 0, 0, 0))
    }
  });
  
  return {
    total,
    today,
    byStatus: stats.reduce((acc, stat) => {
      acc[stat._id] = stat.count;
      return acc;
    }, {})
  };
};

module.exports = mongoose.model('Contact', contactSchema);
