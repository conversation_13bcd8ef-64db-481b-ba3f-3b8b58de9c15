{"version": 3, "sources": ["../../../src/lib/memory/startup.ts"], "names": ["enableMemoryDebuggingMode", "v8", "setHeapSnapshotNearHeapLimit", "setFlagsFromString", "process", "on", "warn", "italic", "writeHeapSnapshot", "startObservingGc", "startPeriodicMemoryUsageTracing", "info", "pid"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;2DAND;qBACY;4BACJ;4BACU;uBACe;;;;;;AAEzC,SAASA;IACd,sEAAsE;IACtE,sEAAsE;IACtE,iEAAiE;IACjE,IAAI,kCAAkCC,WAAE,EAAE;QACxC,uDAAuD;QACvDA,WAAE,CAACC,4BAA4B,CAAC;IAClC;IAEA,wEAAwE;IACxE,qEAAqE;IACrE,wBAAwB;IACxBD,WAAE,CAACE,kBAAkB,CAAC;IAEtB,0EAA0E;IAC1E,2BAA2B;IAC3BC,QAAQC,EAAE,CAAC,WAAW;QACpBC,IAAAA,SAAI,EACF,CAAC,mDAAmD,EAAEC,IAAAA,kBAAM,EAC1D,mCACA,CAAC;QAELN,WAAE,CAACO,iBAAiB;IACtB;IAEAC,IAAAA,4BAAgB;IAChBC,IAAAA,sCAA+B;IAE/BJ,IAAAA,SAAI,EACF,CAAC,kCAAkC,EAAEC,IAAAA,kBAAM,EACzC,uCACA,CAAC;IAELI,IAAAA,SAAI,EACF;IAEFA,IAAAA,SAAI,EACF,CAAC,4FAA4F,EAAEP,QAAQQ,GAAG,CAAC,EAAE,CAAC;IAEhHD,IAAAA,SAAI,EACF;IAEFA,IAAAA,SAAI,EACF;AAEJ"}