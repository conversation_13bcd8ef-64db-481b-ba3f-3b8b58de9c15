'use client'

import { motion } from 'framer-motion'
import { TypeAnimation } from 'react-type-animation'
import { ArrowDown, Download, Mail } from 'lucide-react'

export default function Hero() {
  const scrollToSection = (href: string) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <div className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Animated background grid */}
      <div className="absolute inset-0 cyber-grid opacity-20"></div>
      
      {/* Floating geometric shapes */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute top-20 left-20 w-20 h-20 border-2 border-neon-blue/30 rotate-45"
          animate={{
            rotate: [45, 405],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="absolute top-40 right-32 w-16 h-16 border-2 border-neon-pink/30"
          animate={{
            rotate: [0, 360],
            y: [0, -20, 0],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-40 left-40 w-12 h-12 bg-neon-green/20 rounded-full"
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.2, 0.8, 0.2],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>

      <div className="relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-5xl mx-auto">
        {/* Greeting */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="mb-6"
        >
          <span className="text-neon-blue font-tech text-lg md:text-xl">
            Hello, I'm
          </span>
        </motion.div>

        {/* Name with glitch effect */}
        <motion.h1
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1, delay: 0.2 }}
          className="glitch font-cyber text-4xl md:text-6xl lg:text-8xl font-bold mb-8"
          data-text="YOUR NAME"
        >
          YOUR NAME
        </motion.h1>

        {/* Typing animation for tagline */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 1 }}
          className="mb-8"
        >
          <TypeAnimation
            sequence={[
              'Passionate Self-Taught Developer',
              2000,
              'AI & Machine Learning Enthusiast',
              2000,
              'Full-Stack Web Developer',
              2000,
              'Creative Problem Solver',
              2000,
              'Future Technology Builder',
              2000,
            ]}
            wrapper="h2"
            speed={50}
            className="text-2xl md:text-4xl font-tech text-gray-300 neon-glow-subtle"
            repeat={Infinity}
          />
        </motion.div>

        {/* Description */}
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.5 }}
          className="text-lg md:text-xl text-gray-400 mb-12 max-w-3xl mx-auto leading-relaxed"
        >
          I love AI, web development, futuristic tech, and creative coding. 
          I build projects to learn and to inspire the next generation of developers.
        </motion.p>

        {/* CTA Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 2 }}
          className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16"
        >
          <motion.button
            onClick={() => scrollToSection('#contact')}
            className="btn-cyber group flex items-center gap-3 text-lg"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Mail size={20} />
            Hire Me
            <motion.div
              className="w-0 h-0.5 bg-neon-blue group-hover:w-full transition-all duration-300"
            />
          </motion.button>

          <motion.button
            onClick={() => scrollToSection('#projects')}
            className="btn-cyber group flex items-center gap-3 text-lg border-neon-green text-neon-green hover:text-black"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Download size={20} />
            View Work
            <motion.div
              className="w-0 h-0.5 bg-neon-green group-hover:w-full transition-all duration-300"
            />
          </motion.button>
        </motion.div>

        {/* Scroll indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 2.5 }}
          className="flex flex-col items-center"
        >
          <span className="text-sm text-gray-500 mb-4 font-tech">
            Scroll to explore
          </span>
          <motion.button
            onClick={() => scrollToSection('#about')}
            className="text-neon-blue hover:text-neon-green transition-colors"
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            whileHover={{ scale: 1.2 }}
          >
            <ArrowDown size={24} />
          </motion.button>
        </motion.div>
      </div>

      {/* Holographic overlay */}
      <div className="absolute inset-0 holographic opacity-10 pointer-events-none" />
    </div>
  )
}
