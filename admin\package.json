{"name": "portfolio-admin", "version": "1.0.0", "description": "Admin panel for portfolio website", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"next": "14.2.5", "react": "^18", "react-dom": "^18", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "lucide-react": "^0.427.0", "framer-motion": "^11.3.19", "recharts": "^2.8.0", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "date-fns": "^2.30.0", "js-cookie": "^3.0.5"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/js-cookie": "^3.0.6", "autoprefixer": "^10.4.19", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8.4.40", "tailwindcss": "^3.4.7"}}