"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tsparticles-interaction-particles-attract";
exports.ids = ["vendor-chunks/tsparticles-interaction-particles-attract"];
exports.modules = {

/***/ "(ssr)/./node_modules/tsparticles-interaction-particles-attract/esm/Attractor.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-particles-attract/esm/Attractor.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Attractor: () => (/* binding */ Attractor)\n/* harmony export */ });\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Core/Utils/ParticlesInteractorBase.js\");\n/* harmony import */ var tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tsparticles-engine */ \"(ssr)/./node_modules/tsparticles-engine/esm/Utils/NumberUtils.js\");\n\nclass Attractor extends tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ParticlesInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    clear() {\n    }\n    init() {\n    }\n    async interact(p1) {\n        const container = this.container, distance = p1.retina.attractDistance ?? container.retina.attractDistance, pos1 = p1.getPosition(), query = container.particles.quadTree.queryCircle(pos1, distance);\n        for (const p2 of query) {\n            if (p1 === p2 || !p2.options.move.attract.enable || p2.destroyed || p2.spawning) {\n                continue;\n            }\n            const pos2 = p2.getPosition(), { dx, dy } = (0,tsparticles_engine__WEBPACK_IMPORTED_MODULE_1__.getDistances)(pos1, pos2), rotate = p1.options.move.attract.rotate, ax = dx / (rotate.x * 1000), ay = dy / (rotate.y * 1000), p1Factor = p2.size.value / p1.size.value, p2Factor = 1 / p1Factor;\n            p1.velocity.x -= ax * p1Factor;\n            p1.velocity.y -= ay * p1Factor;\n            p2.velocity.x += ax * p2Factor;\n            p2.velocity.y += ay * p2Factor;\n        }\n    }\n    isEnabled(particle) {\n        return particle.options.move.attract.enable;\n    }\n    reset() {\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tcGFydGljbGVzLWF0dHJhY3QvZXNtL0F0dHJhY3Rvci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNEU7QUFDckUsd0JBQXdCLHVFQUF1QjtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZDQUE2QyxTQUFTLEVBQUUsZ0VBQVk7QUFDcEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnV0dXJpc3RpYy1wb3J0Zm9saW8vLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tcGFydGljbGVzLWF0dHJhY3QvZXNtL0F0dHJhY3Rvci5qcz8xNjBlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBhcnRpY2xlc0ludGVyYWN0b3JCYXNlLCBnZXREaXN0YW5jZXMsIH0gZnJvbSBcInRzcGFydGljbGVzLWVuZ2luZVwiO1xuZXhwb3J0IGNsYXNzIEF0dHJhY3RvciBleHRlbmRzIFBhcnRpY2xlc0ludGVyYWN0b3JCYXNlIHtcbiAgICBjb25zdHJ1Y3Rvcihjb250YWluZXIpIHtcbiAgICAgICAgc3VwZXIoY29udGFpbmVyKTtcbiAgICB9XG4gICAgY2xlYXIoKSB7XG4gICAgfVxuICAgIGluaXQoKSB7XG4gICAgfVxuICAgIGFzeW5jIGludGVyYWN0KHAxKSB7XG4gICAgICAgIGNvbnN0IGNvbnRhaW5lciA9IHRoaXMuY29udGFpbmVyLCBkaXN0YW5jZSA9IHAxLnJldGluYS5hdHRyYWN0RGlzdGFuY2UgPz8gY29udGFpbmVyLnJldGluYS5hdHRyYWN0RGlzdGFuY2UsIHBvczEgPSBwMS5nZXRQb3NpdGlvbigpLCBxdWVyeSA9IGNvbnRhaW5lci5wYXJ0aWNsZXMucXVhZFRyZWUucXVlcnlDaXJjbGUocG9zMSwgZGlzdGFuY2UpO1xuICAgICAgICBmb3IgKGNvbnN0IHAyIG9mIHF1ZXJ5KSB7XG4gICAgICAgICAgICBpZiAocDEgPT09IHAyIHx8ICFwMi5vcHRpb25zLm1vdmUuYXR0cmFjdC5lbmFibGUgfHwgcDIuZGVzdHJveWVkIHx8IHAyLnNwYXduaW5nKSB7XG4gICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBwb3MyID0gcDIuZ2V0UG9zaXRpb24oKSwgeyBkeCwgZHkgfSA9IGdldERpc3RhbmNlcyhwb3MxLCBwb3MyKSwgcm90YXRlID0gcDEub3B0aW9ucy5tb3ZlLmF0dHJhY3Qucm90YXRlLCBheCA9IGR4IC8gKHJvdGF0ZS54ICogMTAwMCksIGF5ID0gZHkgLyAocm90YXRlLnkgKiAxMDAwKSwgcDFGYWN0b3IgPSBwMi5zaXplLnZhbHVlIC8gcDEuc2l6ZS52YWx1ZSwgcDJGYWN0b3IgPSAxIC8gcDFGYWN0b3I7XG4gICAgICAgICAgICBwMS52ZWxvY2l0eS54IC09IGF4ICogcDFGYWN0b3I7XG4gICAgICAgICAgICBwMS52ZWxvY2l0eS55IC09IGF5ICogcDFGYWN0b3I7XG4gICAgICAgICAgICBwMi52ZWxvY2l0eS54ICs9IGF4ICogcDJGYWN0b3I7XG4gICAgICAgICAgICBwMi52ZWxvY2l0eS55ICs9IGF5ICogcDJGYWN0b3I7XG4gICAgICAgIH1cbiAgICB9XG4gICAgaXNFbmFibGVkKHBhcnRpY2xlKSB7XG4gICAgICAgIHJldHVybiBwYXJ0aWNsZS5vcHRpb25zLm1vdmUuYXR0cmFjdC5lbmFibGU7XG4gICAgfVxuICAgIHJlc2V0KCkge1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-particles-attract/esm/Attractor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tsparticles-interaction-particles-attract/esm/index.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/tsparticles-interaction-particles-attract/esm/index.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadParticlesAttractInteraction: () => (/* binding */ loadParticlesAttractInteraction)\n/* harmony export */ });\n/* harmony import */ var _Attractor__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Attractor */ \"(ssr)/./node_modules/tsparticles-interaction-particles-attract/esm/Attractor.js\");\n\nasync function loadParticlesAttractInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"particlesAttract\", (container) => new _Attractor__WEBPACK_IMPORTED_MODULE_0__.Attractor(container), refresh);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tcGFydGljbGVzLWF0dHJhY3QvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXdDO0FBQ2pDO0FBQ1Asc0VBQXNFLGlEQUFTO0FBQy9FIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnV0dXJpc3RpYy1wb3J0Zm9saW8vLi9ub2RlX21vZHVsZXMvdHNwYXJ0aWNsZXMtaW50ZXJhY3Rpb24tcGFydGljbGVzLWF0dHJhY3QvZXNtL2luZGV4LmpzPzEyOGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXR0cmFjdG9yIH0gZnJvbSBcIi4vQXR0cmFjdG9yXCI7XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gbG9hZFBhcnRpY2xlc0F0dHJhY3RJbnRlcmFjdGlvbihlbmdpbmUsIHJlZnJlc2ggPSB0cnVlKSB7XG4gICAgYXdhaXQgZW5naW5lLmFkZEludGVyYWN0b3IoXCJwYXJ0aWNsZXNBdHRyYWN0XCIsIChjb250YWluZXIpID0+IG5ldyBBdHRyYWN0b3IoY29udGFpbmVyKSwgcmVmcmVzaCk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tsparticles-interaction-particles-attract/esm/index.js\n");

/***/ })

};
;