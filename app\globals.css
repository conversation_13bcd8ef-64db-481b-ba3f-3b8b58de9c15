@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #00d4ff, #0ea5e9);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #0ea5e9, #00d4ff);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom cursor styles */
.cursor-none {
  cursor: none;
}

/* Improved Glitch effect - less blurry, more readable */
.glitch {
  position: relative;
  color: #00d4ff;
  font-size: 4rem;
  font-weight: bold;
  text-transform: uppercase;
  animation: glitch 3s infinite;
  text-shadow:
    0 0 2px currentColor,
    0 0 4px currentColor;
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.8;
}

.glitch::before {
  animation: glitch-1 0.8s infinite;
  color: #ff10f0;
  z-index: -1;
}

.glitch::after {
  animation: glitch-2 0.8s infinite;
  color: #39ff14;
  z-index: -2;
}

@keyframes glitch {
  0%, 90%, 92%, 100% {
    transform: translate(0);
  }
  91% {
    transform: translate(-1px, 1px);
  }
}

@keyframes glitch-1 {
  0%, 90%, 92%, 100% {
    transform: translate(0);
    opacity: 0;
  }
  91% {
    transform: translate(1px, -1px);
    opacity: 0.8;
  }
}

@keyframes glitch-2 {
  0%, 90%, 92%, 100% {
    transform: translate(0);
    opacity: 0;
  }
  91% {
    transform: translate(-1px, -1px);
    opacity: 0.8;
  }
}

/* Neon glow effect - reduced for better readability */
.neon-glow {
  text-shadow:
    0 0 2px currentColor,
    0 0 4px currentColor,
    0 0 8px currentColor;
}

/* Subtle glow for better readability */
.neon-glow-subtle {
  text-shadow:
    0 0 1px currentColor,
    0 0 2px currentColor;
}

/* Cyber grid background */
.cyber-grid {
  background-image: 
    linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* Holographic effect */
.holographic {
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(0, 212, 255, 0.5) 50%,
    transparent 70%
  );
  background-size: 250% 250%;
  animation: holographic 3s ease-in-out infinite;
}

@keyframes holographic {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Particle container */
.particles-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

/* Loading animation */
.loading-dots {
  display: inline-block;
  position: relative;
  width: 80px;
  height: 80px;
}

.loading-dots div {
  position: absolute;
  top: 33px;
  width: 13px;
  height: 13px;
  border-radius: 50%;
  background: #00d4ff;
  animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.loading-dots div:nth-child(1) {
  left: 8px;
  animation: loading-dots1 0.6s infinite;
}

.loading-dots div:nth-child(2) {
  left: 8px;
  animation: loading-dots2 0.6s infinite;
}

.loading-dots div:nth-child(3) {
  left: 32px;
  animation: loading-dots2 0.6s infinite;
}

.loading-dots div:nth-child(4) {
  left: 56px;
  animation: loading-dots3 0.6s infinite;
}

@keyframes loading-dots1 {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes loading-dots3 {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0);
  }
}

@keyframes loading-dots2 {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(24px, 0);
  }
}

/* Responsive design utilities */
@media (max-width: 768px) {
  .glitch {
    font-size: 2.5rem;
  }
}

/* Dark mode specific styles */
.dark {
  color-scheme: dark;
}

/* Custom button styles */
.btn-cyber {
  @apply relative px-8 py-3 font-tech font-semibold text-white bg-transparent border-2 border-neon-blue rounded-lg overflow-hidden transition-all duration-300 hover:text-black;
}

.btn-cyber::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, #00d4ff, transparent);
  transition: left 0.5s;
}

.btn-cyber:hover::before {
  left: 100%;
}

.btn-cyber::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #00d4ff;
  z-index: -1;
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s;
}

.btn-cyber:hover::after {
  transform: scaleX(1);
}
