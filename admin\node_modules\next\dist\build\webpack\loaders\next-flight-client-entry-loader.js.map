{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-flight-client-entry-loader.ts"], "names": ["transformSource", "modules", "server", "getOptions", "isServer", "Array", "isArray", "code", "map", "x", "JSON", "parse", "filter", "request", "regexCSS", "test", "ids", "importPath", "stringify", "startsWith", "BARREL_OPTIMIZATION_PREFIX", "replace", "length", "includes", "join", "buildInfo", "getModuleBuildInfo", "_module", "rsc", "type", "RSC_MODULE_TYPES", "client"], "mappings": ";;;;+BA2BA;;;eAAwBA;;;2BAvBjB;oCAC4B;uBACV;AAqBV,SAASA;IAGtB,IAAI,EAAEC,OAAO,EAAEC,MAAM,EAAE,GAAG,IAAI,CAACC,UAAU;IACzC,MAAMC,WAAWF,WAAW;IAE5B,IAAI,CAACG,MAAMC,OAAO,CAACL,UAAU;QAC3BA,UAAUA,UAAU;YAACA;SAAQ,GAAG,EAAE;IACpC;IAEA,MAAMM,OAAON,QACVO,GAAG,CAAC,CAACC,IAAMC,KAAKC,KAAK,CAACF,GACvB,8CAA8C;KAC7CG,MAAM,CAAC,CAAC,EAAEC,OAAO,EAAE,GAAMT,WAAW,CAACU,eAAQ,CAACC,IAAI,CAACF,WAAW,MAC9DL,GAAG,CAAC,CAAC,EAAEK,OAAO,EAAEG,GAAG,EAA+B;QACjD,MAAMC,aAAaP,KAAKQ,SAAS,CAC/BL,QAAQM,UAAU,CAACC,qCAA0B,IACzCP,QAAQQ,OAAO,CAAC,KAAK,SACrBR;QAGN,4FAA4F;QAC5F,0FAA0F;QAC1F,wDAAwD;QACxD,IAAIG,IAAIM,MAAM,KAAK,KAAKN,IAAIO,QAAQ,CAAC,MAAM;YACzC,OAAO,CAAC,kCAAkC,EAAEN,WAAW,IAAI,CAAC;QAC9D,OAAO;YACL,OAAO,CAAC,gDAAgD,EAAEP,KAAKQ,SAAS,CACtEF,KACA,IAAI,EAAEC,WAAW,IAAI,CAAC;QAC1B;IACF,GACCO,IAAI,CAAC;IAER,MAAMC,YAAYC,IAAAA,sCAAkB,EAAC,IAAI,CAACC,OAAO;IAEjDF,UAAUG,GAAG,GAAG;QACdC,MAAMC,2BAAgB,CAACC,MAAM;IAC/B;IAEA,OAAOxB;AACT"}